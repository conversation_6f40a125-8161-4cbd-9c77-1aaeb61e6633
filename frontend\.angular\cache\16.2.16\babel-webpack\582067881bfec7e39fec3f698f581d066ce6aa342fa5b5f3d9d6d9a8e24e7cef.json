{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_24_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r7, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fichier_r7.split(\"/\").pop(), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"h3\", 29);\n    i0.ɵɵtext(2, \"Fichiers joints:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 30);\n    i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_6_div_24_li_4_Template, 3, 2, \"li\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getFieldError(\"scores.structure\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getFieldError(\"scores.pratiques\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getFieldError(\"scores.fonctionnalite\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getFieldError(\"scores.originalite\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"commentaires\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 67);\n    i0.ɵɵelement(2, \"path\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Soumettre l'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 69);\n    i0.ɵɵelement(2, \"circle\", 70)(3, \"path\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Soumission en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"form\", 33);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_6_form_43_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 34)(2, \"h3\", 35);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 36);\n    i0.ɵɵelement(4, \"path\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Crit\\u00E8res d'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\", 38)(7, \"div\", 39)(8, \"label\", 40);\n    i0.ɵɵtext(9, \" Structure du code \");\n    i0.ɵɵelementStart(10, \"span\", 41);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 42);\n    i0.ɵɵelement(13, \"input\", 43);\n    i0.ɵɵelementStart(14, \"div\", 44)(15, \"span\", 45);\n    i0.ɵɵtext(16, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, ProjectEvaluationComponent_div_6_form_43_div_17_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 39)(19, \"label\", 40);\n    i0.ɵɵtext(20, \" Bonnes pratiques \");\n    i0.ɵɵelementStart(21, \"span\", 41);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 42);\n    i0.ɵɵelement(24, \"input\", 47);\n    i0.ɵɵelementStart(25, \"div\", 44)(26, \"span\", 45);\n    i0.ɵɵtext(27, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, ProjectEvaluationComponent_div_6_form_43_div_28_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 39)(30, \"label\", 40);\n    i0.ɵɵtext(31, \" Fonctionnalit\\u00E9 \");\n    i0.ɵɵelementStart(32, \"span\", 41);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 42);\n    i0.ɵɵelement(35, \"input\", 48);\n    i0.ɵɵelementStart(36, \"div\", 44)(37, \"span\", 45);\n    i0.ɵɵtext(38, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(39, ProjectEvaluationComponent_div_6_form_43_div_39_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 39)(41, \"label\", 40);\n    i0.ɵɵtext(42, \" Originalit\\u00E9 \");\n    i0.ɵɵelementStart(43, \"span\", 41);\n    i0.ɵɵtext(44, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 42);\n    i0.ɵɵelement(46, \"input\", 49);\n    i0.ɵɵelementStart(47, \"div\", 44)(48, \"span\", 45);\n    i0.ɵɵtext(49, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(50, ProjectEvaluationComponent_div_6_form_43_div_50_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 50)(52, \"div\", 51)(53, \"span\", 52);\n    i0.ɵɵtext(54, \"Score total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 17)(56, \"span\", 53);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"span\", 54);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 55);\n    i0.ɵɵelement(61, \"div\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"div\", 39)(63, \"label\", 40);\n    i0.ɵɵtext(64, \" Commentaires d\\u00E9taill\\u00E9s \");\n    i0.ɵɵelementStart(65, \"span\", 41);\n    i0.ɵɵtext(66, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(67, \"textarea\", 57);\n    i0.ɵɵtemplate(68, ProjectEvaluationComponent_div_6_form_43_div_68_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementStart(69, \"p\", 58);\n    i0.ɵɵtext(70, \"D\\u00E9crivez les points forts et les axes d'am\\u00E9lioration du projet.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 59)(72, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_form_43_Template_button_click_72_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.annuler());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(73, \"svg\", 61);\n    i0.ɵɵelement(74, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(75, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(76, \"button\", 63);\n    i0.ɵɵtemplate(77, ProjectEvaluationComponent_div_6_form_43_span_77_Template, 4, 0, \"span\", 64);\n    i0.ɵɵtemplate(78, ProjectEvaluationComponent_div_6_form_43_span_78_Template, 5, 0, \"span\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.evaluationForm);\n    i0.ɵɵadvance(13);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.structure\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.structure\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.pratiques\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.pratiques\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.fonctionnalite\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.fonctionnalite\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.originalite\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.originalite\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.getScoreTotal());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"/\", ctx_r4.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.getScoreTotal() / ctx_r4.getScoreMaximum() * 100, \"%\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"commentaires\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"commentaires\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.evaluationForm.invalid || ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_44_div_1_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 67);\n    i0.ɵɵelement(2, \"path\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Lancer l'\\u00E9valuation IA \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_44_div_1_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 69);\n    i0.ɵɵelement(2, \"circle\", 70)(3, \"path\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Lancement en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_44_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 74)(2, \"div\", 75);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 76);\n    i0.ɵɵelement(4, \"path\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 77);\n    i0.ɵɵtext(6, \"\\u00C9valuation automatique par IA\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 78)(8, \"div\", 79)(9, \"div\", 80);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 81);\n    i0.ɵɵelement(11, \"path\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"div\", 83)(13, \"h4\", 84);\n    i0.ɵɵtext(14, \"Comment \\u00E7a fonctionne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 85);\n    i0.ɵɵtext(16, \"Notre syst\\u00E8me d'IA (Mistral 7B) analysera automatiquement le code soumis selon les crit\\u00E8res suivants :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ul\", 86)(18, \"li\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 87);\n    i0.ɵɵelement(20, \"path\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Structure et organisation du code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"li\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 87);\n    i0.ɵɵelement(24, \"path\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Respect des bonnes pratiques \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"li\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(27, \"svg\", 87);\n    i0.ɵɵelement(28, \"path\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \" Fonctionnalit\\u00E9s impl\\u00E9ment\\u00E9es \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"li\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(31, \"svg\", 87);\n    i0.ɵɵelement(32, \"path\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Originalit\\u00E9 et cr\\u00E9ativit\\u00E9 \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(34, \"div\", 89)(35, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_div_44_div_1_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.annuler());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 61);\n    i0.ɵɵelement(37, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_div_44_div_1_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.onSubmit());\n    });\n    i0.ɵɵtemplate(40, ProjectEvaluationComponent_div_6_div_44_div_1_span_40_Template, 4, 0, \"span\", 64);\n    i0.ɵɵtemplate(41, ProjectEvaluationComponent_div_6_div_44_div_1_span_41_Template, 5, 0, \"span\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(39);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r18.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_44_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"div\", 93);\n    i0.ɵɵelementStart(3, \"div\", 94);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 95);\n    i0.ɵɵelement(5, \"path\", 20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h3\", 96);\n    i0.ɵɵtext(7, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 97);\n    i0.ɵɵtext(9, \"Notre syst\\u00E8me examine le code selon les crit\\u00E8res d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 98)(11, \"div\", 99);\n    i0.ɵɵelement(12, \"div\", 100)(13, \"div\", 101)(14, \"div\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 103);\n    i0.ɵɵtext(16, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_6_div_44_div_1_Template, 42, 3, \"div\", 5);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_6_div_44_div_2_Template, 17, 0, \"div\", 73);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 9)(2, \"h2\", 10);\n    i0.ɵɵtext(3, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 11)(5, \"div\")(6, \"p\")(7, \"span\", 12);\n    i0.ɵɵtext(8, \"Projet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"span\", 12);\n    i0.ɵɵtext(12, \"\\u00C9tudiant:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\")(15, \"p\")(16, \"span\", 12);\n    i0.ɵɵtext(17, \"Date de soumission:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\")(21, \"span\", 12);\n    i0.ɵɵtext(22, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, ProjectEvaluationComponent_div_6_div_24_Template, 5, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 14)(26, \"div\", 15)(27, \"div\", 16)(28, \"div\", 17)(29, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(30, \"svg\", 19);\n    i0.ɵɵelement(31, \"path\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"h2\", 21);\n    i0.ɵɵtext(33, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 22)(35, \"span\");\n    i0.ɵɵtext(36, \" Manuel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \" IA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.toggleEvaluationMode());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(40, \"svg\", 24);\n    i0.ɵɵelement(41, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" Changer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(43, ProjectEvaluationComponent_div_6_form_43_Template, 79, 28, \"form\", 26);\n    i0.ɵɵtemplate(44, ProjectEvaluationComponent_div_6_div_44_Template, 3, 2, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.projet.titre, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(19, 14, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.description || \"Aucune description\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassMapInterpolate1(\"px-3 py-2 text-sm font-medium \", ctx_r2.evaluationMode === \"manual\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600\", \" rounded-md transition-all duration-200\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"px-3 py-2 text-sm font-medium \", ctx_r2.evaluationMode === \"ai\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600\", \" rounded-md transition-all duration-200\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.successMessage = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n    console.log('Form values:', this.evaluationForm.value);\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\n      console.log('Form is invalid, marking fields as touched');\n      this.markFormGroupTouched(this.evaluationForm);\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\n      return;\n    }\n    this.isSubmitting = true;\n    this.error = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    console.log('Sending evaluation data:', evaluationData);\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/list-rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/list-rendus']);\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldError(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return 'Ce champ est obligatoire';\n      }\n      if (field.errors['min']) {\n        return `La valeur minimum est ${field.errors['min'].min}`;\n      }\n      if (field.errors['max']) {\n        return `La valeur maximum est ${field.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 7,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-lg\", \"shadow\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-6\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-blue-500\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"mb-6\", \"p-4\", \"bg-gray-50\", \"rounded-lg\"], [1, \"text-xl\", \"font-semibold\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"font-medium\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mb-8\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-sm\", \"border\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-6\"], [1, \"flex\", \"items-center\"], [1, \"bg-purple-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"], [1, \"text-2xl\", \"font-semibold\", \"text-gray-800\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"rounded-lg\", \"p-1\"], [1, \"ml-2\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"text-white\", \"rounded-lg\", \"hover:from-purple-600\", \"hover:to-blue-600\", \"transition-all\", \"duration-200\", \"shadow-sm\", \"hover:shadow-md\", \"transform\", \"hover:scale-105\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"], [\"class\", \"space-y-6\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"font-medium\", \"mb-2\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", 3, \"href\"], [1, \"space-y-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"bg-gray-50\", \"rounded-lg\", \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"mb-4\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\", \"text-purple-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"form-group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [1, \"text-red-500\"], [1, \"relative\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-3\"], [1, \"text-sm\", \"text-gray-500\"], [\"class\", \"mt-1 text-sm text-red-600\", 4, \"ngIf\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [1, \"mt-6\", \"p-4\", \"bg-white\", \"rounded-lg\", \"border-2\", \"border-purple-200\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-700\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-600\"], [1, \"text-lg\", \"text-gray-500\", \"ml-1\"], [1, \"mt-2\", \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2\"], [1, \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"h-2\", \"rounded-full\", \"transition-all\", \"duration-300\"], [\"formControlName\", \"commentaires\", \"rows\", \"6\", \"placeholder\", \"Saisissez vos commentaires d\\u00E9taill\\u00E9s sur l'\\u00E9valuation...\"], [1, \"mt-2\", \"text-sm\", \"text-gray-500\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\", \"pt-6\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", 1, \"w-full\", \"sm:w-auto\", \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"text-gray-700\", \"rounded-lg\", \"hover:bg-gray-50\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"type\", \"submit\", 1, \"w-full\", \"sm:w-auto\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-green-500\", \"to-emerald-500\", \"text-white\", \"rounded-lg\", \"hover:from-green-600\", \"hover:to-emerald-600\", \"transition-all\", \"duration-200\", \"font-semibold\", \"shadow-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", \"disabled:shadow-none\", 3, \"disabled\"], [\"class\", \"flex items-center justify-center\", 4, \"ngIf\"], [1, \"mt-1\", \"text-sm\", \"text-red-600\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"bg-gradient-to-br\", \"from-indigo-50\", \"to-purple-50\", \"rounded-xl\", \"p-6\", \"border\", \"border-indigo-200\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"mb-4\"], [1, \"bg-indigo-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-white\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-800\"], [1, \"bg-white\", \"rounded-lg\", \"p-4\", \"mb-6\", \"border\", \"border-indigo-100\"], [1, \"flex\", \"items-start\"], [1, \"flex-shrink-0\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-indigo-500\", \"mt-0.5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"ml-3\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\", \"mb-1\"], [1, \"text-sm\", \"text-gray-600\", \"mb-2\"], [1, \"text-sm\", \"text-gray-600\", \"space-y-1\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"w-3\", \"h-3\", \"text-green-500\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\"], [1, \"w-full\", \"sm:w-auto\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-indigo-500\", \"to-purple-500\", \"text-white\", \"rounded-lg\", \"hover:from-indigo-600\", \"hover:to-purple-600\", \"transition-all\", \"duration-200\", \"font-semibold\", \"shadow-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", \"disabled:shadow-none\", 3, \"disabled\", \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"text-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-t-4\", \"border-b-4\", \"border-indigo-500\", \"mx-auto\", \"mb-6\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-indigo-500\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"mb-2\"], [1, \"text-gray-600\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"p-4\", \"max-w-md\", \"mx-auto\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"text-sm\", \"text-gray-500\", \"mt-2\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_4_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ProjectEvaluationComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵtemplate(6, ProjectEvaluationComponent_div_6_Template, 45, 17, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n.fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.5s ease-out;\\n}\\n\\n.pulse-animation[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);\\n  border-color: #8b5cf6;\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);\\n}\\n\\n\\n\\n.card-hover[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.card-hover[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #8b5cf6, #3b82f6);\\n  transition: width 0.5s ease;\\n}\\n\\n\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\\n}\\n\\n.alert-error[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n\\n  .grid-responsive[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n\\n\\n.icon-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵproperty", "fichier_r7", "ɵɵsanitizeUrl", "split", "pop", "ɵɵtemplate", "ProjectEvaluationComponent_div_6_div_24_li_4_Template", "ctx_r3", "rendu", "fichiers", "ctx_r8", "getFieldError", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵlistener", "ProjectEvaluationComponent_div_6_form_43_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProjectEvaluationComponent_div_6_form_43_div_17_Template", "ProjectEvaluationComponent_div_6_form_43_div_28_Template", "ProjectEvaluationComponent_div_6_form_43_div_39_Template", "ProjectEvaluationComponent_div_6_form_43_div_50_Template", "ProjectEvaluationComponent_div_6_form_43_div_68_Template", "ProjectEvaluationComponent_div_6_form_43_Template_button_click_72_listener", "ctx_r17", "annuler", "ProjectEvaluationComponent_div_6_form_43_span_77_Template", "ProjectEvaluationComponent_div_6_form_43_span_78_Template", "ctx_r4", "evaluationForm", "ɵɵclassMapInterpolate1", "isFieldInvalid", "ɵɵtextInterpolate", "getScoreTotal", "getScoreMaximum", "ɵɵstyleProp", "invalid", "isSubmitting", "ProjectEvaluationComponent_div_6_div_44_div_1_Template_button_click_35_listener", "_r23", "ctx_r22", "ProjectEvaluationComponent_div_6_div_44_div_1_Template_button_click_39_listener", "ctx_r24", "ProjectEvaluationComponent_div_6_div_44_div_1_span_40_Template", "ProjectEvaluationComponent_div_6_div_44_div_1_span_41_Template", "ctx_r18", "ProjectEvaluationComponent_div_6_div_44_div_1_Template", "ProjectEvaluationComponent_div_6_div_44_div_2_Template", "ctx_r5", "aiProcessing", "ProjectEvaluationComponent_div_6_div_24_Template", "ProjectEvaluationComponent_div_6_Template_button_click_39_listener", "_r26", "ctx_r25", "toggleEvaluationMode", "ProjectEvaluationComponent_div_6_form_43_Template", "ProjectEvaluationComponent_div_6_div_44_Template", "ctx_r2", "projet", "titre", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "length", "evaluationMode", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "isLoading", "successMessage", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "err", "console", "log", "valid", "value", "markFormGroupTouched", "evaluationData", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "field", "dirty", "touched", "errors", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ProjectEvaluationComponent_div_4_Template", "ProjectEvaluationComponent_div_5_Template", "ProjectEvaluationComponent_div_6_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  successMessage: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\r\n    console.log('Form values:', this.evaluationForm.value);\r\n\r\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\r\n      console.log('Form is invalid, marking fields as touched');\r\n      this.markFormGroupTouched(this.evaluationForm);\r\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n    console.log('Sending evaluation data:', evaluationData);\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/list-rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    this.router.navigate(['/admin/projects/list-rendus']);\r\n  }\r\n\r\n  markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    if (field && field.errors && (field.dirty || field.touched)) {\r\n      if (field.errors['required']) {\r\n        return 'Ce champ est obligatoire';\r\n      }\r\n      if (field.errors['min']) {\r\n        return `La valeur minimum est ${field.errors['min'].min}`;\r\n      }\r\n      if (field.errors['max']) {\r\n        return `La valeur maximum est ${field.errors['max'].max}`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"container mx-auto px-4 py-6\">\r\n  <div class=\"max-w-4xl mx-auto bg-white rounded-lg shadow p-6\">\r\n    <h1 class=\"text-2xl font-bold text-gray-800 mb-6\">Évaluation du projet</h1>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\r\n      <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n    </div>\r\n\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error }}\r\n    </div>\r\n\r\n    <div *ngIf=\"rendu && !isLoading\">\r\n      <div class=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n        <h2 class=\"text-xl font-semibold mb-4\">Informations sur le rendu</h2>\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <p><span class=\"font-medium\">Projet:</span> {{ rendu.projet.titre }}</p>\r\n            <p><span class=\"font-medium\">Étudiant:</span> {{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n          </div>\r\n          <div>\r\n            <p><span class=\"font-medium\">Date de soumission:</span> {{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n            <p><span class=\"font-medium\">Description:</span> {{ rendu.description || 'Aucune description' }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"mt-4\">\r\n          <h3 class=\"font-medium mb-2\">Fichiers joints:</h3>\r\n          <ul class=\"list-disc pl-5\">\r\n            <li *ngFor=\"let fichier of rendu.fichiers\">\r\n              <a [href]=\"'http://localhost:3000/' + fichier\" target=\"_blank\" class=\"text-blue-600 hover:underline\">\r\n                {{ fichier.split('/').pop() }}\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-8\">\r\n        <div class=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n          <div class=\"flex items-center justify-between mb-6\">\r\n            <div class=\"flex items-center\">\r\n              <div class=\"bg-purple-500 p-2 rounded-lg mr-3\">\r\n                <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h2 class=\"text-2xl font-semibold text-gray-800\">Mode d'évaluation</h2>\r\n            </div>\r\n            <div class=\"flex items-center bg-gray-100 rounded-lg p-1\">\r\n              <span class=\"px-3 py-2 text-sm font-medium {{ evaluationMode === 'manual' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600' }} rounded-md transition-all duration-200\">\r\n                Manuel\r\n              </span>\r\n              <span class=\"px-3 py-2 text-sm font-medium {{ evaluationMode === 'ai' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600' }} rounded-md transition-all duration-200\">\r\n                IA\r\n              </span>\r\n              <button\r\n                (click)=\"toggleEvaluationMode()\"\r\n                class=\"ml-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n              >\r\n                <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"></path>\r\n                </svg>\r\n                Changer\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\" class=\"space-y-6\">\r\n            <div class=\"bg-gray-50 rounded-lg p-6\">\r\n              <h3 class=\"text-lg font-semibold text-gray-800 mb-4 flex items-center\">\r\n                <svg class=\"w-5 h-5 mr-2 text-purple-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\r\n                </svg>\r\n                Critères d'évaluation\r\n              </h3>\r\n\r\n              <div formGroupName=\"scores\" class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Structure du code\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"structure\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.structure') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.structure')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.structure') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Bonnes pratiques\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"pratiques\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.pratiques') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.pratiques')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.pratiques') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Fonctionnalité\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"fonctionnalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.fonctionnalite') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.fonctionnalite')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.fonctionnalite') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Originalité\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"originalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.originalite') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.originalite')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.originalite') }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Score total display -->\r\n              <div class=\"mt-6 p-4 bg-white rounded-lg border-2 border-purple-200\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <span class=\"text-lg font-semibold text-gray-700\">Score total:</span>\r\n                  <div class=\"flex items-center\">\r\n                    <span class=\"text-2xl font-bold text-purple-600\">{{ getScoreTotal() }}</span>\r\n                    <span class=\"text-lg text-gray-500 ml-1\">/{{ getScoreMaximum() }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"mt-2 w-full bg-gray-200 rounded-full h-2\">\r\n                  <div class=\"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300\"\r\n                       [style.width.%]=\"(getScoreTotal() / getScoreMaximum()) * 100\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                Commentaires détaillés\r\n                <span class=\"text-red-500\">*</span>\r\n              </label>\r\n              <textarea\r\n                formControlName=\"commentaires\"\r\n                rows=\"6\"\r\n                class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('commentaires') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                placeholder=\"Saisissez vos commentaires détaillés sur l'évaluation...\"\r\n              ></textarea>\r\n              <div *ngIf=\"isFieldInvalid('commentaires')\" class=\"mt-1 text-sm text-red-600\">\r\n                {{ getFieldError('commentaires') }}\r\n              </div>\r\n              <p class=\"mt-2 text-sm text-gray-500\">Décrivez les points forts et les axes d'amélioration du projet.</p>\r\n            </div>\r\n\r\n            <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center pt-6 border-t border-gray-200\">\r\n              <button\r\n                type=\"button\"\r\n                (click)=\"annuler()\"\r\n                class=\"w-full sm:w-auto px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium\"\r\n              >\r\n                <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                </svg>\r\n                Annuler\r\n              </button>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                [disabled]=\"evaluationForm.invalid || isSubmitting\"\r\n                class=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none\"\r\n              >\r\n                <span *ngIf=\"!isSubmitting\" class=\"flex items-center justify-center\">\r\n                  <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  Soumettre l'évaluation\r\n                </span>\r\n                <span *ngIf=\"isSubmitting\" class=\"flex items-center justify-center\">\r\n                  <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                    <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Soumission en cours...\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </form>\r\n\r\n          <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200\">\r\n            <div *ngIf=\"!aiProcessing\">\r\n              <div class=\"flex items-center mb-4\">\r\n                <div class=\"bg-indigo-500 p-2 rounded-lg mr-3\">\r\n                  <svg class=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <h3 class=\"text-xl font-semibold text-gray-800\">Évaluation automatique par IA</h3>\r\n              </div>\r\n\r\n              <div class=\"bg-white rounded-lg p-4 mb-6 border border-indigo-100\">\r\n                <div class=\"flex items-start\">\r\n                  <div class=\"flex-shrink-0\">\r\n                    <svg class=\"w-5 h-5 text-indigo-500 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"ml-3\">\r\n                    <h4 class=\"text-sm font-medium text-gray-900 mb-1\">Comment ça fonctionne</h4>\r\n                    <p class=\"text-sm text-gray-600 mb-2\">Notre système d'IA (Mistral 7B) analysera automatiquement le code soumis selon les critères suivants :</p>\r\n                    <ul class=\"text-sm text-gray-600 space-y-1\">\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Structure et organisation du code\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Respect des bonnes pratiques\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Fonctionnalités implémentées\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Originalité et créativité\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  (click)=\"annuler()\"\r\n                  class=\"w-full sm:w-auto px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium\"\r\n                >\r\n                  <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                  Annuler\r\n                </button>\r\n\r\n                <button\r\n                  (click)=\"onSubmit()\"\r\n                  [disabled]=\"isSubmitting\"\r\n                  class=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none\"\r\n                >\r\n                  <span *ngIf=\"!isSubmitting\" class=\"flex items-center justify-center\">\r\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\r\n                    </svg>\r\n                    Lancer l'évaluation IA\r\n                  </span>\r\n                  <span *ngIf=\"isSubmitting\" class=\"flex items-center justify-center\">\r\n                    <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                      <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    Lancement en cours...\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"aiProcessing\" class=\"text-center py-12\">\r\n              <div class=\"relative\">\r\n                <div class=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-500 mx-auto mb-6\"></div>\r\n                <div class=\"absolute inset-0 flex items-center justify-center\">\r\n                  <svg class=\"w-6 h-6 text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <h3 class=\"text-lg font-semibold text-gray-800 mb-2\">L'IA analyse le projet...</h3>\r\n              <p class=\"text-gray-600 mb-4\">Notre système examine le code selon les critères d'évaluation</p>\r\n              <div class=\"bg-white rounded-lg p-4 max-w-md mx-auto\">\r\n                <div class=\"flex items-center justify-center space-x-2\">\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\"></div>\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\" style=\"animation-delay: 0.1s\"></div>\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\" style=\"animation-delay: 0.2s\"></div>\r\n                </div>\r\n                <p class=\"text-sm text-gray-500 mt-2\">Cela peut prendre quelques instants</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;;;ICG/DC,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,SAAA,aAA6F;IAC/FF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAAgG;IAC9FD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAmBQR,EAAA,CAAAC,cAAA,SAA2C;IAEvCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAFDH,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAS,UAAA,oCAAAC,UAAA,EAAAV,EAAA,CAAAW,aAAA,CAA2C;IAC5CX,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAI,UAAA,CAAAE,KAAA,MAAAC,GAAA,QACF;;;;;IANNb,EAAA,CAAAC,cAAA,cAAsE;IACvCD,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAc,UAAA,IAAAC,qDAAA,iBAIK;IACPf,EAAA,CAAAG,YAAA,EAAK;;;;IALqBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAS,UAAA,YAAAO,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;IAmEnClB,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAa,MAAA,CAAAC,aAAA,0BACF;;;;;IAqBApB,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAe,MAAA,CAAAD,aAAA,0BACF;;;;;IAqBApB,EAAA,CAAAC,cAAA,cAAuF;IACrFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAgB,OAAA,CAAAF,aAAA,+BACF;;;;;IAqBApB,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,OAAA,CAAAH,aAAA,4BACF;;;;;IA+BJpB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAkB,OAAA,CAAAJ,aAAA,sBACF;;;;;IAqBEpB,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAyB,cAAA,EAAgF;IAAhFzB,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,eAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAyB,cAAA,EAA2H;IAA3HzB,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,iBAAkG;IAEpGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,+BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;;IArKbH,EAAA,CAAA0B,eAAA,EAAiH;IAAjH1B,EAAA,CAAAC,cAAA,eAAiH;IAA9ED,EAAA,CAAA2B,UAAA,sBAAAC,2EAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAgC,aAAA;MAAA,OAAYhC,EAAA,CAAAiC,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxDlC,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAyB,cAAA,EAAgG;IAAhGzB,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,SAAA,eAAsR;IACxRF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAA0B,eAAA,EAA0E;IAA1E1B,EAAA,CAAAC,cAAA,cAA0E;IAGpED,EAAA,CAAAI,MAAA,0BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAc,UAAA,KAAAqB,wDAAA,kBAEM;IACRnC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,0BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAc,UAAA,KAAAsB,wDAAA,kBAEM;IACRpC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,6BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAc,UAAA,KAAAuB,wDAAA,kBAEM;IACRrC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,0BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAc,UAAA,KAAAwB,wDAAA,kBAEM;IACRtC,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAAqE;IAEfD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,eAA+B;IACoBD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAC,cAAA,gBAAyC;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG5EH,EAAA,CAAAC,cAAA,eAAsD;IACpDD,EAAA,CAAAE,SAAA,eACyE;IAC3EF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,0CACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAE,SAAA,oBAKY;IACZF,EAAA,CAAAc,UAAA,KAAAyB,wDAAA,kBAEM;IACNvC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAI,MAAA,iFAA+D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAG3GH,EAAA,CAAAC,cAAA,eAAwG;IAGpGD,EAAA,CAAA2B,UAAA,mBAAAa,2EAAA;MAAAxC,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAAzC,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAQ,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnB1C,EAAA,CAAAyB,cAAA,EAAuF;IAAvFzB,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAsG;IACxGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAA0B,eAAA,EAIC;IAJD1B,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAAc,UAAA,KAAA6B,yDAAA,mBAKO;IACP3C,EAAA,CAAAc,UAAA,KAAA8B,yDAAA,mBAMO;IACT5C,EAAA,CAAAG,YAAA,EAAS;;;;IAtKPH,EAAA,CAAAS,UAAA,cAAAoC,MAAA,CAAAC,cAAA,CAA4B;IAqBtB9C,EAAA,CAAAK,SAAA,IAAuQ;IAAvQL,EAAA,CAAA+C,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,gGAAuQ;IAOrQhD,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAS,UAAA,SAAAoC,MAAA,CAAAG,cAAA,qBAAwC;IAgB1ChD,EAAA,CAAAK,SAAA,GAAuQ;IAAvQL,EAAA,CAAA+C,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,gGAAuQ;IAOrQhD,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAS,UAAA,SAAAoC,MAAA,CAAAG,cAAA,qBAAwC;IAgB1ChD,EAAA,CAAAK,SAAA,GAA4Q;IAA5QL,EAAA,CAAA+C,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,qGAA4Q;IAO1QhD,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAS,UAAA,SAAAoC,MAAA,CAAAG,cAAA,0BAA6C;IAgB/ChD,EAAA,CAAAK,SAAA,GAAyQ;IAAzQL,EAAA,CAAA+C,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,kGAAyQ;IAOvQhD,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAS,UAAA,SAAAoC,MAAA,CAAAG,cAAA,uBAA0C;IAWGhD,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAiD,iBAAA,CAAAJ,MAAA,CAAAK,aAAA,GAAqB;IAC7BlD,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,kBAAA,MAAAuC,MAAA,CAAAM,eAAA,OAAwB;IAK9DnD,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAAoD,WAAA,UAAAP,MAAA,CAAAK,aAAA,KAAAL,MAAA,CAAAM,eAAA,cAA6D;IAapEnD,EAAA,CAAAK,SAAA,GAAmQ;IAAnQL,EAAA,CAAA+C,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,4FAAmQ;IAG/PhD,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAS,UAAA,SAAAoC,MAAA,CAAAG,cAAA,iBAAoC;IAoBxChD,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAS,UAAA,aAAAoC,MAAA,CAAAC,cAAA,CAAAO,OAAA,IAAAR,MAAA,CAAAS,YAAA,CAAmD;IAG5CtD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAoC,MAAA,CAAAS,YAAA,CAAmB;IAMnBtD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAoC,MAAA,CAAAS,YAAA,CAAkB;;;;;IA+EvBtD,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAyB,cAAA,EAAgF;IAAhFzB,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,eAA4G;IAC9GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAyB,cAAA,EAA2H;IAA3HzB,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,iBAAkG;IAEpGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,8BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IA/EbH,EAAA,CAAAC,cAAA,UAA2B;IAGrBD,EAAA,CAAAyB,cAAA,EAAsF;IAAtFzB,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAA0B,eAAA,EAAgD;IAAhD1B,EAAA,CAAAC,cAAA,aAAgD;IAAAD,EAAA,CAAAI,MAAA,yCAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGpFH,EAAA,CAAAC,cAAA,cAAmE;IAG7DD,EAAA,CAAAyB,cAAA,EAAkG;IAAlGzB,EAAA,CAAAC,cAAA,eAAkG;IAChGD,EAAA,CAAAE,SAAA,gBAA2I;IAC7IF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAA0B,eAAA,EAAkB;IAAlB1B,EAAA,CAAAC,cAAA,eAAkB;IACmCD,EAAA,CAAAI,MAAA,kCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAI,MAAA,wHAAsG;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChJH,EAAA,CAAAC,cAAA,cAA4C;IAExCD,EAAA,CAAAyB,cAAA,EAAiF;IAAjFzB,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,2CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAA0B,eAAA,EAA8B;IAA9B1B,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAyB,cAAA,EAAiF;IAAjFzB,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,sCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAA0B,eAAA,EAA8B;IAA9B1B,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAyB,cAAA,EAAiF;IAAjFzB,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,qDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAA0B,eAAA,EAA8B;IAA9B1B,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAyB,cAAA,EAAiF;IAAjFzB,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAMbH,EAAA,CAAA0B,eAAA,EAA0E;IAA1E1B,EAAA,CAAAC,cAAA,eAA0E;IAGtED,EAAA,CAAA2B,UAAA,mBAAA4B,gFAAA;MAAAvD,EAAA,CAAA6B,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAzD,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAwB,OAAA,CAAAf,OAAA,EAAS;IAAA,EAAC;IAGnB1C,EAAA,CAAAyB,cAAA,EAAuF;IAAvFzB,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAsG;IACxGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAA0B,eAAA,EAIC;IAJD1B,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAA2B,UAAA,mBAAA+B,gFAAA;MAAA1D,EAAA,CAAA6B,aAAA,CAAA2B,IAAA;MAAA,MAAAG,OAAA,GAAA3D,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAA0B,OAAA,CAAAzB,QAAA,EAAU;IAAA,EAAC;IAIpBlC,EAAA,CAAAc,UAAA,KAAA8C,8DAAA,mBAKO;IACP5D,EAAA,CAAAc,UAAA,KAAA+C,8DAAA,mBAMO;IACT7D,EAAA,CAAAG,YAAA,EAAS;;;;IAhBPH,EAAA,CAAAK,SAAA,IAAyB;IAAzBL,EAAA,CAAAS,UAAA,aAAAqD,OAAA,CAAAR,YAAA,CAAyB;IAGlBtD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAqD,OAAA,CAAAR,YAAA,CAAmB;IAMnBtD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAqD,OAAA,CAAAR,YAAA,CAAkB;;;;;IAW/BtD,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAE,SAAA,cAA4G;IAC5GF,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAyB,cAAA,EAA2F;IAA3FzB,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAA0B,eAAA,EAAqD;IAArD1B,EAAA,CAAAC,cAAA,aAAqD;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAI,MAAA,mFAA6D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC/FH,EAAA,CAAAC,cAAA,eAAsD;IAElDD,EAAA,CAAAE,SAAA,gBAAqE;IAGvEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAI,MAAA,2CAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAtGnFH,EAAA,CAAA0B,eAAA,EAAmI;IAAnI1B,EAAA,CAAAC,cAAA,cAAmI;IACjID,EAAA,CAAAc,UAAA,IAAAiD,sDAAA,kBAkFM;IAEN/D,EAAA,CAAAc,UAAA,IAAAkD,sDAAA,mBAmBM;IACRhE,EAAA,CAAAG,YAAA,EAAM;;;;IAxGEH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAwD,MAAA,CAAAC,YAAA,CAAmB;IAoFnBlE,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAwD,MAAA,CAAAC,YAAA,CAAkB;;;;;;IAvThClE,EAAA,CAAAC,cAAA,UAAiC;IAEUD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,cAAmD;IAElBD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,GAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,sBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAExGH,EAAA,CAAAC,cAAA,WAAK;IAC0BD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChHH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAA+C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIxGH,EAAA,CAAAc,UAAA,KAAAqD,gDAAA,kBASM;IACRnE,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAKRD,EAAA,CAAAyB,cAAA,EAAsF;IAAtFzB,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,gBAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAA0B,eAAA,EAAiD;IAAjD1B,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEzEH,EAAA,CAAAC,cAAA,eAA0D;IAEtDD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAA0K;IACxKD,EAAA,CAAAI,MAAA,YACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAA2B,UAAA,mBAAAyC,mEAAA;MAAApE,EAAA,CAAA6B,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAqC,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCvE,EAAA,CAAAyB,cAAA,EAAuF;IAAvFzB,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAc,UAAA,KAAA0D,iDAAA,qBAwKO;IAEPxE,EAAA,CAAAc,UAAA,KAAA2D,gDAAA,kBAyGM;IACRzE,EAAA,CAAAG,YAAA,EAAM;;;;IAvU0CH,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,kBAAA,MAAAoE,MAAA,CAAAzD,KAAA,CAAA0D,MAAA,CAAAC,KAAA,KAAwB;IACtB5E,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA6E,kBAAA,MAAAH,MAAA,CAAAzD,KAAA,CAAA6D,QAAA,CAAAC,GAAA,OAAAL,MAAA,CAAAzD,KAAA,CAAA6D,QAAA,CAAAE,MAAA,KAAoD;IAG1ChF,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAiF,WAAA,SAAAP,MAAA,CAAAzD,KAAA,CAAAiE,cAAA,0BAAoD;IAC3DlF,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAM,kBAAA,MAAAoE,MAAA,CAAAzD,KAAA,CAAAkE,WAAA,6BAA+C;IAI9FnF,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAS,UAAA,SAAAiE,MAAA,CAAAzD,KAAA,CAAAC,QAAA,IAAAwD,MAAA,CAAAzD,KAAA,CAAAC,QAAA,CAAAkE,MAAA,KAAiD;IAwB3CpF,EAAA,CAAAK,SAAA,IAAuK;IAAvKL,EAAA,CAAA+C,sBAAA,mCAAA2B,MAAA,CAAAW,cAAA,kHAAuK;IAGvKrF,EAAA,CAAAK,SAAA,GAAmK;IAAnKL,EAAA,CAAA+C,sBAAA,mCAAA2B,MAAA,CAAAW,cAAA,8GAAmK;IAejHrF,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAS,UAAA,SAAAiE,MAAA,CAAAW,cAAA,cAAiC;IA0KvFrF,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAS,UAAA,SAAAiE,MAAA,CAAAW,cAAA,UAA6B;;;ADpO7C,OAAM,MAAOC,0BAA0B;EAWrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAdvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAA3E,KAAK,GAAQ,IAAI;IAEjB,KAAA4E,SAAS,GAAY,IAAI;IACzB,KAAAvC,YAAY,GAAY,KAAK;IAC7B,KAAA9C,KAAK,GAAW,EAAE;IAClB,KAAAsF,cAAc,GAAW,EAAE;IAC3B,KAAAT,cAAc,GAAoB,QAAQ;IAC1C,KAAAnB,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAACpB,cAAc,GAAG,IAAI,CAAC0C,EAAE,CAACO,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACR,EAAE,CAACO,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAClG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACtG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACvG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACxG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAEzG,UAAU,CAACmG,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACd,OAAO,GAAG,IAAI,CAACH,KAAK,CAACkB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACrB,KAAK,CAACkB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACzB,cAAc,GAAGyB,IAAI;MAC1B,IAAI,CAAChE,cAAc,CAACkE,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC9B,cAAc,GAAG8B,UAAU;QAChC,IAAI,CAACrE,cAAc,CAACkE,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACvB,OAAO,EAAE;MAChB,IAAI,CAACyB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAC7G,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACqF,SAAS,GAAG,KAAK;;EAE1B;EAEAwB,SAASA,CAAA;IACP,IAAI,CAACxB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAAC2B,YAAY,CAAC,IAAI,CAAC1B,OAAO,CAAC,CAAC2B,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACxG,KAAK,GAAGwG,IAAI;QACjB,IAAI,CAAC5B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDrF,KAAK,EAAGkH,GAAQ,IAAI;QAClB,IAAI,CAAClH,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACqF,SAAS,GAAG,KAAK;QACtB8B,OAAO,CAACnH,KAAK,CAACkH,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAnD,oBAAoBA,CAAA;IAClB,IAAI,CAACc,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAACvC,cAAc,CAACkE,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACpB,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E4B,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC7B,cAAc,CAAC;EAC7D;EAEAnD,QAAQA,CAAA;IACNyF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC9E,cAAc,CAAC+E,KAAK,CAAC;IACrEF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC9E,cAAc,CAACgF,KAAK,CAAC;IAEtD,IAAI,IAAI,CAACzC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACvC,cAAc,CAACO,OAAO,EAAE;MACnEsE,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACG,oBAAoB,CAAC,IAAI,CAACjF,cAAc,CAAC;MAC9C,IAAI,CAACtC,KAAK,GAAG,gDAAgD;MAC7D;;IAGF,IAAI,CAAC8C,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC9C,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAAC6E,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAACvC,cAAc,CAACkE,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACvC,YAAY,GAAG,IAAI;;IAG1B,MAAM8D,cAAc,GAAG,IAAI,CAAClF,cAAc,CAACgF,KAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,cAAc,CAAC;IAEvD,IAAI,CAACrC,aAAa,CAACsC,aAAa,CAAC,IAAI,CAACrC,OAAO,EAAEoC,cAAc,CAAC,CAACT,SAAS,CAAC;MACvEC,IAAI,EAAGU,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC7C,cAAc,KAAK,IAAI,IAAI6C,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACnC,MAAM;UAC3C,MAAMqC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAAC3B,YAAY;UAEvD,IAAI,CAAC1D,cAAc,CAACkE,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEmC,QAAQ,CAACnC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAE+B,QAAQ,CAAC/B,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE8B,QAAQ,CAAC9B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE6B,QAAQ,CAAC7B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE6B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAACnE,YAAY,GAAG,KAAK;UACzB,IAAI,CAACZ,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAAC9C,KAAK,GAAG,EAAE;UACf8H,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAAChF,YAAY,GAAG,KAAK;UACzBgF,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;;MAEzD,CAAC;MACD/H,KAAK,EAAGkH,GAAQ,IAAI;QAClB,IAAI,CAAClH,KAAK,GAAG,yCAAyC,IAAIkH,GAAG,CAAClH,KAAK,EAAEgI,OAAO,IAAId,GAAG,CAACc,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAAClF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACY,YAAY,GAAG,KAAK;QACzByD,OAAO,CAACnH,KAAK,CAACkH,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAxE,aAAaA,CAAA;IACX,MAAM8C,MAAM,GAAG,IAAI,CAAClD,cAAc,CAAC+D,GAAG,CAAC,QAAQ,CAAC,EAAEiB,KAAK;IACvD,IAAI,CAAC9B,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEApD,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAT,OAAOA,CAAA;IACL,IAAI,CAACgD,MAAM,CAAC6C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EACvD;EAEAR,oBAAoBA,CAACU,SAAoB;IACvCC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAC5B,GAAG,CAACiC,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAYjJ,SAAS,EAAE;QAChC,IAAI,CAACiI,oBAAoB,CAACgB,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA/F,cAAcA,CAACiG,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAACpG,cAAc,CAAC+D,GAAG,CAACoC,SAAS,CAAC;IAChD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAAC7F,OAAO,KAAK6F,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACrE;EAEAhI,aAAaA,CAAC6H,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACpG,cAAc,CAAC+D,GAAG,CAACoC,SAAS,CAAC;IAChD,IAAIC,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAKH,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,EAAE;MAC3D,IAAIF,KAAK,CAACG,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,0BAA0B;;MAEnC,IAAIH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,CAAClD,GAAG,EAAE;;MAE3D,IAAI+C,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,CAACjD,GAAG,EAAE;;;IAG7D,OAAO,EAAE;EACX;;;uBAxLWd,0BAA0B,EAAAtF,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA3J,EAAA,CAAAsJ,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1BvE,0BAA0B;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvCpK,EAAA,CAAAC,cAAA,aAAyC;UAEaD,EAAA,CAAAI,MAAA,gCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAE3EH,EAAA,CAAAc,UAAA,IAAAwJ,yCAAA,iBAEM;UAENtK,EAAA,CAAAc,UAAA,IAAAyJ,yCAAA,iBAEM;UAENvK,EAAA,CAAAc,UAAA,IAAA0J,yCAAA,mBA8UM;UACRxK,EAAA,CAAAG,YAAA,EAAM;;;UAvVEH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAS,UAAA,SAAA4J,GAAA,CAAAxE,SAAA,CAAe;UAIf7F,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAS,UAAA,SAAA4J,GAAA,CAAA7J,KAAA,CAAW;UAIXR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAS,UAAA,SAAA4J,GAAA,CAAApJ,KAAA,KAAAoJ,GAAA,CAAAxE,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}