{"ast": null, "code": "import { DatePipe } from '@angular/common';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/rendus.service\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction ListRendusComponent_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r6);\n  }\n}\nfunction ListRendusComponent_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r7.titre);\n  }\n}\nfunction ListRendusComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListRendusComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.error, \" \");\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/evaluation-details\", a1];\n};\nfunction ListRendusComponent_div_43_tr_18_a_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 49);\n    i0.ɵɵtext(1, \" Voir l'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rendu_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, rendu_r9._id));\n  }\n}\nfunction ListRendusComponent_div_43_tr_18_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_43_tr_18_div_25_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.evaluerRendu(rendu_r9._id, \"manual\"));\n    });\n    i0.ɵɵtext(2, \" \\u00C9valuer manuellement \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_43_tr_18_div_25_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.evaluerRendu(rendu_r9._id, \"ai\"));\n    });\n    i0.ɵɵtext(4, \" \\u00C9valuer par IA \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ListRendusComponent_div_43_tr_18_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_43_tr_18_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.navigateToEditEvaluation(rendu_r9._id));\n    });\n    i0.ɵɵtext(1, \" Modifier l'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListRendusComponent_div_43_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 36)(2, \"div\", 37)(3, \"div\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"div\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 41);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"td\", 36)(11, \"div\", 42);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 36)(14, \"div\", 42);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 36)(17, \"div\", 42);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"td\", 36)(20, \"span\", 43);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\", 44)(23, \"div\", 45);\n    i0.ɵɵtemplate(24, ListRendusComponent_div_43_tr_18_a_24_Template, 2, 3, \"a\", 46);\n    i0.ɵɵtemplate(25, ListRendusComponent_div_43_tr_18_div_25_Template, 5, 0, \"div\", 47);\n    i0.ɵɵtemplate(26, ListRendusComponent_div_43_tr_18_button_26_Template, 2, 0, \"button\", 48);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const rendu_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.nom == null ? null : rendu_r9.etudiant.nom.charAt(0), \"\", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.prenom == null ? null : rendu_r9.etudiant.prenom.charAt(0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.nom, \" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.prenom, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", rendu_r9.etudiant == null ? null : rendu_r9.etudiant.email, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((rendu_r9.etudiant == null ? null : rendu_r9.etudiant.group) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(rendu_r9.projet == null ? null : rendu_r9.projet.titre);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.formatDate(rendu_r9.dateSoumission));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getClasseStatut(rendu_r9));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getStatutEvaluation(rendu_r9), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", rendu_r9.evaluation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !rendu_r9.evaluation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", rendu_r9.evaluation);\n  }\n}\nfunction ListRendusComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"table\", 31)(3, \"thead\", 32)(4, \"tr\")(5, \"th\", 33);\n    i0.ɵɵtext(6, \" \\u00C9tudiant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 33);\n    i0.ɵɵtext(8, \" Groupe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 33);\n    i0.ɵɵtext(10, \" Projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 33);\n    i0.ɵɵtext(12, \" Date de soumission \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 33);\n    i0.ɵɵtext(14, \" Statut \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 33);\n    i0.ɵɵtext(16, \" Actions \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"tbody\", 34);\n    i0.ɵɵtemplate(18, ListRendusComponent_div_43_tr_18_Template, 27, 13, \"tr\", 35);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.filteredRendus);\n  }\n}\nfunction ListRendusComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 54);\n    i0.ɵɵelement(2, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"h3\", 56);\n    i0.ɵɵtext(4, \"Aucun rendu disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 57);\n    i0.ɵɵtext(6, \"Aucun rendu ne correspond \\u00E0 vos crit\\u00E8res de filtrage\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ListRendusComponent {\n  constructor(rendusService, projetService, router, datePipe) {\n    this.rendusService = rendusService;\n    this.projetService = projetService;\n    this.router = router;\n    this.datePipe = datePipe;\n    this.rendus = [];\n    this.filteredRendus = [];\n    this.isLoading = true;\n    this.error = '';\n    this.searchTerm = '';\n    this.filterStatus = 'all';\n    // Nouvelles propriétés pour les filtres\n    this.filtreGroupe = '';\n    this.filtreProjet = '';\n    this.groupes = [];\n    this.projets = [];\n  }\n  ngOnInit() {\n    this.loadRendus();\n    this.loadProjets();\n    this.extractGroupes();\n  }\n  loadRendus() {\n    this.isLoading = true;\n    this.rendusService.getAllRendus().subscribe({\n      next: data => {\n        this.rendus = data;\n        this.extractGroupes();\n        this.applyFilters();\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des rendus', err);\n        this.error = 'Impossible de charger les rendus. Veuillez réessayer plus tard.';\n        this.isLoading = false;\n      }\n    });\n  }\n  loadProjets() {\n    this.projetService.getProjets().subscribe({\n      next: data => {\n        this.projets = data;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des projets', err);\n      }\n    });\n  }\n  extractGroupes() {\n    // Extraire les groupes uniques des rendus\n    if (this.rendus && this.rendus.length > 0) {\n      const groupesSet = new Set();\n      this.rendus.forEach(rendu => {\n        if (rendu.etudiant?.groupe) {\n          groupesSet.add(rendu.etudiant.groupe);\n        }\n      });\n      this.groupes = Array.from(groupesSet);\n    }\n  }\n  applyFilters() {\n    let results = this.rendus;\n    // Filtre par statut d'évaluation\n    if (this.filterStatus === 'evaluated') {\n      results = results.filter(rendu => rendu.evaluation && rendu.evaluation.scores);\n    } else if (this.filterStatus === 'pending') {\n      results = results.filter(rendu => !rendu.evaluation || !rendu.evaluation.scores);\n    }\n    // Filtre par terme de recherche\n    if (this.searchTerm.trim() !== '') {\n      const term = this.searchTerm.toLowerCase().trim();\n      results = results.filter(rendu => rendu.etudiant?.nom?.toLowerCase().includes(term) || rendu.etudiant?.prenom?.toLowerCase().includes(term) || rendu.projet?.titre?.toLowerCase().includes(term));\n    }\n    // Filtre par groupe\n    if (this.filtreGroupe) {\n      results = results.filter(rendu => rendu.etudiant?.groupe === this.filtreGroupe);\n    }\n    // Filtre par projet\n    if (this.filtreProjet) {\n      results = results.filter(rendu => rendu.projet?._id === this.filtreProjet);\n    }\n    this.filteredRendus = results;\n  }\n  // Méthode pour la compatibilité avec le template\n  filtrerRendus() {\n    return this.filteredRendus;\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  setFilterStatus(status) {\n    this.filterStatus = status;\n    this.applyFilters();\n  }\n  evaluateRendu(renduId) {\n    this.router.navigate(['/admin/projects/evaluate', renduId]);\n  }\n  // Méthode pour la compatibilité avec le template\n  evaluerRendu(renduId, mode) {\n    // Rediriger vers la page d'évaluation avec le mode approprié\n    this.router.navigate(['/admin/projects/evaluate', renduId], {\n      queryParams: {\n        mode: mode\n      }\n    });\n  }\n  viewEvaluationDetails(renduId) {\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n  }\n  getStatusClass(rendu) {\n    if (rendu.evaluation && rendu.evaluation.scores) {\n      return 'bg-green-100 text-green-800';\n    }\n    return 'bg-yellow-100 text-yellow-800';\n  }\n  // Méthode pour la compatibilité avec le template\n  getClasseStatut(rendu) {\n    return this.getStatusClass(rendu);\n  }\n  getStatusText(rendu) {\n    // Vérifier si l'évaluation existe de plusieurs façons\n    if (rendu.evaluation && rendu.evaluation._id) {\n      return 'Évalué';\n    }\n    if (rendu.statut === 'évalué') {\n      return 'Évalué';\n    }\n    return 'En attente';\n  }\n  // Méthode pour la compatibilité avec le template\n  getStatutEvaluation(rendu) {\n    return this.getStatusText(rendu);\n  }\n  getScoreTotal(rendu) {\n    if (!rendu.evaluation || !rendu.evaluation.scores) return 0;\n    const scores = rendu.evaluation.scores;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreClass(score) {\n    if (score >= 16) return 'text-green-600';\n    if (score >= 12) return 'text-blue-600';\n    if (score >= 8) return 'text-yellow-600';\n    return 'text-red-600';\n  }\n  formatDate(date) {\n    if (!date) return '';\n    return this.datePipe.transform(date, 'dd/MM/yyyy') || '';\n  }\n  navigateToEditEvaluation(renduId) {\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n  }\n  // Méthodes pour gérer les fichiers\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  static {\n    this.ɵfac = function ListRendusComponent_Factory(t) {\n      return new (t || ListRendusComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.DatePipe));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListRendusComponent,\n      selectors: [[\"app-list-rendus\"]],\n      features: [i0.ɵɵProvidersFeature([DatePipe])],\n      decls: 45,\n      vars: 10,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"space-x-4\", \"text-white/80\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\"], [1, \"text-sm\"], [1, \"w-px\", \"h-12\", \"bg-white/20\"], [1, \"max-w-6xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-md\", \"p-4\", \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [1, \"w-full\", \"p-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"focus:ring-[#4f5fad]\", \"focus:border-[#4f5fad]\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"max-w-6xl mx-auto flex justify-center py-12\", 4, \"ngIf\"], [\"class\", \"max-w-6xl mx-auto bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"max-w-6xl mx-auto bg-white rounded-xl shadow-md overflow-hidden\", 4, \"ngIf\"], [\"class\", \"max-w-6xl mx-auto text-center py-12\", 4, \"ngIf\"], [3, \"value\"], [1, \"max-w-6xl\", \"mx-auto\", \"flex\", \"justify-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"max-w-6xl\", \"mx-auto\", \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"max-w-6xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"overflow-x-auto\"], [1, \"min-w-full\", \"divide-y\", \"divide-gray-200\"], [1, \"bg-gray-50\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\", \"text-left\", \"text-xs\", \"font-medium\", \"text-gray-500\", \"uppercase\", \"tracking-wider\"], [1, \"bg-white\", \"divide-y\", \"divide-gray-200\"], [4, \"ngFor\", \"ngForOf\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\"], [1, \"flex\", \"items-center\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-xs\", \"font-bold\"], [1, \"ml-4\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"text-sm\", \"text-gray-900\"], [1, \"px-2\", \"inline-flex\", \"text-xs\", \"leading-5\", \"font-semibold\", \"rounded-full\", 3, \"ngClass\"], [1, \"px-6\", \"py-4\", \"whitespace-nowrap\", \"text-sm\", \"font-medium\"], [1, \"flex\", \"space-x-2\"], [\"class\", \"text-indigo-600 hover:text-indigo-900\", 3, \"routerLink\", 4, \"ngIf\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"text-blue-600 hover:text-blue-800 mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"text-indigo-600\", \"hover:text-indigo-900\", 3, \"routerLink\"], [1, \"text-green-600\", \"hover:text-green-900\", \"bg-green-100\", \"px-2\", \"py-1\", \"rounded\", 3, \"click\"], [1, \"text-blue-600\", \"hover:text-blue-900\", \"bg-blue-100\", \"px-2\", \"py-1\", \"rounded\", 3, \"click\"], [1, \"text-blue-600\", \"hover:text-blue-800\", \"mr-2\", 3, \"click\"], [1, \"max-w-6xl\", \"mx-auto\", \"text-center\", \"py-12\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-[#bdc6cc]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1\", \"d\", \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"mt-4\", \"text-lg\", \"font-medium\", \"text-[#6d6870]\"], [1, \"mt-1\", \"text-sm\", \"text-[#6d6870]\"]],\n      template: function ListRendusComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 6);\n          i0.ɵɵelement(7, \"path\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n          i0.ɵɵtext(10, \"Liste des Rendus\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \"Gestion et \\u00E9valuation des projets \\u00E9tudiants\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵtext(18, \"Total\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(19, \"div\", 14);\n          i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 13);\n          i0.ɵɵtext(24, \"\\u00C9valu\\u00E9s\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(25, \"div\", 15)(26, \"div\", 16)(27, \"div\")(28, \"label\", 17);\n          i0.ɵɵtext(29, \"Filtrer par groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"select\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_30_listener($event) {\n            return ctx.filtreGroupe = $event;\n          })(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_30_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(31, \"option\", 19);\n          i0.ɵɵtext(32, \"Tous les groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, ListRendusComponent_option_33_Template, 2, 2, \"option\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\")(35, \"label\", 17);\n          i0.ɵɵtext(36, \"Filtrer par projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"select\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_37_listener($event) {\n            return ctx.filtreProjet = $event;\n          })(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_37_listener() {\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(38, \"option\", 19);\n          i0.ɵɵtext(39, \"Tous les projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, ListRendusComponent_option_40_Template, 2, 2, \"option\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(41, ListRendusComponent_div_41_Template, 2, 0, \"div\", 21);\n          i0.ɵɵtemplate(42, ListRendusComponent_div_42_Template, 2, 1, \"div\", 22);\n          i0.ɵɵtemplate(43, ListRendusComponent_div_43_Template, 19, 1, \"div\", 23);\n          i0.ɵɵtemplate(44, ListRendusComponent_div_44_Template, 7, 0, \"div\", 24);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtextInterpolate(ctx.rendus.length);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getEvaluatedCount());\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.filtreGroupe);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.filtreProjet);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.projets);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredRendus.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredRendus.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.RouterLink, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\"\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImxpc3QtcmVuZHVzLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsaURBQWlEO0FBQ2pEO0VBQ0UsYUFBYTtFQUNiLHVCQUF1QjtFQUN2QixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQiIsImZpbGUiOiJsaXN0LXJlbmR1cy5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGRlIGxpc3RlIGRlcyByZW5kdXMgKi9cclxuLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBtYXJnaW46IDJyZW0gMDtcclxufVxyXG5cclxuLmVycm9yLW1lc3NhZ2Uge1xyXG4gIGNvbG9yOiAjZGMzNTQ1O1xyXG4gIG1hcmdpbi10b3A6IDAuMjVyZW07XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvbGlzdC1yZW5kdXMvbGlzdC1yZW5kdXMuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxpREFBaUQ7QUFDakQ7RUFDRSxhQUFhO0VBQ2IsdUJBQXVCO0VBQ3ZCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsbUJBQW1CO0FBQ3JCO0FBQ0EsNG9CQUE0b0IiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZGUgbGlzdGUgZGVzIHJlbmR1cyAqL1xyXG4ubG9hZGluZy1zcGlubmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIG1hcmdpbjogMnJlbSAwO1xyXG59XHJcblxyXG4uZXJyb3ItbWVzc2FnZSB7XHJcbiAgY29sb3I6ICNkYzM1NDU7XHJcbiAgbWFyZ2luLXRvcDogMC4yNXJlbTtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DatePipe", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "groupe_r6", "ɵɵadvance", "ɵɵtextInterpolate", "projet_r7", "_id", "titre", "ɵɵelement", "ɵɵtextInterpolate1", "ctx_r3", "error", "ɵɵpureFunction1", "_c0", "rendu_r9", "ɵɵlistener", "ListRendusComponent_div_43_tr_18_div_25_Template_button_click_1_listener", "ɵɵrestoreView", "_r16", "ɵɵnextContext", "$implicit", "ctx_r14", "ɵɵresetView", "evaluerRendu", "ListRendusComponent_div_43_tr_18_div_25_Template_button_click_3_listener", "ctx_r17", "ListRendusComponent_div_43_tr_18_button_26_Template_button_click_0_listener", "_r21", "ctx_r19", "navigateToEditEvaluation", "ɵɵtemplate", "ListRendusComponent_div_43_tr_18_a_24_Template", "ListRendusComponent_div_43_tr_18_div_25_Template", "ListRendusComponent_div_43_tr_18_button_26_Template", "ɵɵtextInterpolate2", "etudiant", "nom", "char<PERSON>t", "prenom", "email", "group", "projet", "ctx_r8", "formatDate", "dateSoumission", "getClasseStatut", "getStatutEvaluation", "evaluation", "ListRendusComponent_div_43_tr_18_Template", "ctx_r4", "filteredRendus", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ListRendusComponent", "constructor", "rendusService", "projetService", "router", "datePipe", "rendus", "isLoading", "searchTerm", "filterStatus", "filtreGroupe", "filtreProjet", "groupes", "projets", "ngOnInit", "loadRendus", "loadProjets", "extractGroupes", "getAllRendus", "subscribe", "next", "data", "applyFilters", "err", "console", "getProjets", "length", "groupesSet", "Set", "for<PERSON>ach", "rendu", "groupe", "add", "Array", "from", "results", "filter", "scores", "trim", "term", "toLowerCase", "includes", "filtrerRendus", "onSearchChange", "setFilterStatus", "status", "evaluateRendu", "renduId", "navigate", "mode", "queryParams", "viewEvaluationDetails", "getStatusClass", "getStatusText", "statut", "getScoreTotal", "structure", "pratiques", "fonctionnalite", "originalite", "getScoreClass", "score", "date", "transform", "getFileUrl", "filePath", "fileName", "parts", "split", "urlBackend", "getFileName", "ɵɵdirectiveInject", "i1", "RendusService", "i2", "ProjetService", "i3", "Router", "i4", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "ListRendusComponent_Template", "rf", "ctx", "ListRendusComponent_Template_select_ngModelChange_30_listener", "$event", "ListRendusComponent_option_33_Template", "ListRendusComponent_Template_select_ngModelChange_37_listener", "ListRendusComponent_option_40_Template", "ListRendusComponent_div_41_Template", "ListRendusComponent_div_42_Template", "ListRendusComponent_div_43_Template", "ListRendusComponent_div_44_Template", "getEvaluatedCount"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\list-rendus\\list-rendus.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\list-rendus\\list-rendus.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { DatePipe } from '@angular/common';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-list-rendus',\r\n  templateUrl: './list-rendus.component.html',\r\n  styleUrls: ['./list-rendus.component.css'],\r\n  providers: [DatePipe],\r\n})\r\nexport class ListRendusComponent implements OnInit {\r\n  rendus: any[] = [];\r\n  filteredRendus: any[] = [];\r\n  isLoading = true;\r\n  error = '';\r\n  searchTerm = '';\r\n  filterStatus: 'all' | 'evaluated' | 'pending' = 'all';\r\n\r\n  // Nouvelles propriétés pour les filtres\r\n  filtreGroupe: string = '';\r\n  filtreProjet: string = '';\r\n  groupes: string[] = [];\r\n  projets: any[] = [];\r\n\r\n  constructor(\r\n    private rendusService: RendusService,\r\n    private projetService: ProjetService,\r\n    private router: Router,\r\n    private datePipe: DatePipe\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadRendus();\r\n    this.loadProjets();\r\n    this.extractGroupes();\r\n  }\r\n\r\n  loadRendus(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getAllRendus().subscribe({\r\n      next: (data) => {\r\n        this.rendus = data;\r\n        this.extractGroupes();\r\n        this.applyFilters();\r\n        this.isLoading = false;\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors du chargement des rendus', err);\r\n        this.error =\r\n          'Impossible de charger les rendus. Veuillez réessayer plus tard.';\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  loadProjets(): void {\r\n    this.projetService.getProjets().subscribe({\r\n      next: (data) => {\r\n        this.projets = data;\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors du chargement des projets', err);\r\n      },\r\n    });\r\n  }\r\n\r\n  extractGroupes(): void {\r\n    // Extraire les groupes uniques des rendus\r\n    if (this.rendus && this.rendus.length > 0) {\r\n      const groupesSet = new Set<string>();\r\n      this.rendus.forEach((rendu) => {\r\n        if (rendu.etudiant?.groupe) {\r\n          groupesSet.add(rendu.etudiant.groupe);\r\n        }\r\n      });\r\n      this.groupes = Array.from(groupesSet);\r\n    }\r\n  }\r\n\r\n  applyFilters(): void {\r\n    let results = this.rendus;\r\n\r\n    // Filtre par statut d'évaluation\r\n    if (this.filterStatus === 'evaluated') {\r\n      results = results.filter(\r\n        (rendu) => rendu.evaluation && rendu.evaluation.scores\r\n      );\r\n    } else if (this.filterStatus === 'pending') {\r\n      results = results.filter(\r\n        (rendu) => !rendu.evaluation || !rendu.evaluation.scores\r\n      );\r\n    }\r\n\r\n    // Filtre par terme de recherche\r\n    if (this.searchTerm.trim() !== '') {\r\n      const term = this.searchTerm.toLowerCase().trim();\r\n      results = results.filter(\r\n        (rendu) =>\r\n          rendu.etudiant?.nom?.toLowerCase().includes(term) ||\r\n          rendu.etudiant?.prenom?.toLowerCase().includes(term) ||\r\n          rendu.projet?.titre?.toLowerCase().includes(term)\r\n      );\r\n    }\r\n\r\n    // Filtre par groupe\r\n    if (this.filtreGroupe) {\r\n      results = results.filter(\r\n        (rendu) => rendu.etudiant?.groupe === this.filtreGroupe\r\n      );\r\n    }\r\n\r\n    // Filtre par projet\r\n    if (this.filtreProjet) {\r\n      results = results.filter(\r\n        (rendu) => rendu.projet?._id === this.filtreProjet\r\n      );\r\n    }\r\n\r\n    this.filteredRendus = results;\r\n  }\r\n\r\n  // Méthode pour la compatibilité avec le template\r\n  filtrerRendus(): any[] {\r\n    return this.filteredRendus;\r\n  }\r\n\r\n  onSearchChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  setFilterStatus(status: 'all' | 'evaluated' | 'pending'): void {\r\n    this.filterStatus = status;\r\n    this.applyFilters();\r\n  }\r\n\r\n  evaluateRendu(renduId: string): void {\r\n    this.router.navigate(['/admin/projects/evaluate', renduId]);\r\n  }\r\n\r\n  // Méthode pour la compatibilité avec le template\r\n  evaluerRendu(renduId: string, mode: 'manual' | 'ai'): void {\r\n    // Rediriger vers la page d'évaluation avec le mode approprié\r\n    this.router.navigate(['/admin/projects/evaluate', renduId], {\r\n      queryParams: { mode: mode },\r\n    });\r\n  }\r\n\r\n  viewEvaluationDetails(renduId: string): void {\r\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\r\n  }\r\n\r\n  getStatusClass(rendu: any): string {\r\n    if (rendu.evaluation && rendu.evaluation.scores) {\r\n      return 'bg-green-100 text-green-800';\r\n    }\r\n    return 'bg-yellow-100 text-yellow-800';\r\n  }\r\n\r\n  // Méthode pour la compatibilité avec le template\r\n  getClasseStatut(rendu: any): string {\r\n    return this.getStatusClass(rendu);\r\n  }\r\n\r\n  getStatusText(rendu: any): string {\r\n    // Vérifier si l'évaluation existe de plusieurs façons\r\n    if (rendu.evaluation && rendu.evaluation._id) {\r\n      return 'Évalué';\r\n    }\r\n    if (rendu.statut === 'évalué') {\r\n      return 'Évalué';\r\n    }\r\n    return 'En attente';\r\n  }\r\n\r\n  // Méthode pour la compatibilité avec le template\r\n  getStatutEvaluation(rendu: any): string {\r\n    return this.getStatusText(rendu);\r\n  }\r\n\r\n  getScoreTotal(rendu: any): number {\r\n    if (!rendu.evaluation || !rendu.evaluation.scores) return 0;\r\n\r\n    const scores = rendu.evaluation.scores;\r\n    return (\r\n      scores.structure +\r\n      scores.pratiques +\r\n      scores.fonctionnalite +\r\n      scores.originalite\r\n    );\r\n  }\r\n\r\n  getScoreClass(score: number): string {\r\n    if (score >= 16) return 'text-green-600';\r\n    if (score >= 12) return 'text-blue-600';\r\n    if (score >= 8) return 'text-yellow-600';\r\n    return 'text-red-600';\r\n  }\r\n\r\n  formatDate(date: string): string {\r\n    if (!date) return '';\r\n    return this.datePipe.transform(date, 'dd/MM/yyyy') || '';\r\n  }\r\n\r\n  navigateToEditEvaluation(renduId: string): void {\r\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\r\n  }\r\n\r\n  // Méthodes pour gérer les fichiers\r\n  getFileUrl(filePath: string): string {\r\n    if (!filePath) return '';\r\n\r\n    // Extraire uniquement le nom du fichier\r\n    let fileName = filePath;\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      fileName = parts[parts.length - 1];\r\n    }\r\n\r\n    // Utiliser la route spécifique pour le téléchargement\r\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\r\n  }\r\n\r\n  getFileName(filePath: string): string {\r\n    if (!filePath) return 'Fichier';\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      return parts[parts.length - 1];\r\n    }\r\n\r\n    return filePath;\r\n  }\r\n}\r\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n    <!-- Header avec gradient -->\r\n    <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm\">\r\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h1 class=\"text-3xl font-bold text-white mb-2\">Liste des Rendus</h1>\r\n            <p class=\"text-white/80\">Gestion et évaluation des projets étudiants</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"hidden md:flex items-center space-x-4 text-white/80\">\r\n          <div class=\"text-center\">\r\n            <div class=\"text-2xl font-bold\">{{ rendus.length }}</div>\r\n            <div class=\"text-sm\">Total</div>\r\n          </div>\r\n          <div class=\"w-px h-12 bg-white/20\"></div>\r\n          <div class=\"text-center\">\r\n            <div class=\"text-2xl font-bold\">{{ getEvaluatedCount() }}</div>\r\n            <div class=\"text-sm\">Évalués</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  <!-- Filtres -->\r\n  <div class=\"max-w-6xl mx-auto bg-white rounded-xl shadow-md p-4 mb-6\">\r\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Filtrer par groupe</label>\r\n        <select\r\n          [(ngModel)]=\"filtreGroupe\"\r\n          (ngModelChange)=\"applyFilters()\"\r\n          class=\"w-full p-2 border border-gray-300 rounded-md focus:ring-[#4f5fad] focus:border-[#4f5fad]\"\r\n        >\r\n          <option value=\"\">Tous les groupes</option>\r\n          <option *ngFor=\"let groupe of groupes\" [value]=\"groupe\">{{ groupe }}</option>\r\n        </select>\r\n      </div>\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Filtrer par projet</label>\r\n        <select\r\n          [(ngModel)]=\"filtreProjet\"\r\n          (ngModelChange)=\"applyFilters()\"\r\n          class=\"w-full p-2 border border-gray-300 rounded-md focus:ring-[#4f5fad] focus:border-[#4f5fad]\"\r\n        >\r\n          <option value=\"\">Tous les projets</option>\r\n          <option *ngFor=\"let projet of projets\" [value]=\"projet._id\">{{ projet.titre }}</option>\r\n        </select>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading Spinner -->\r\n  <div *ngIf=\"isLoading\" class=\"max-w-6xl mx-auto flex justify-center py-12\">\r\n    <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#4f5fad]\"></div>\r\n  </div>\r\n\r\n  <!-- Error Message -->\r\n  <div *ngIf=\"error\" class=\"max-w-6xl mx-auto bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n    {{ error }}\r\n  </div>\r\n\r\n  <!-- Rendus Table -->\r\n  <div *ngIf=\"!isLoading && filteredRendus.length > 0\" class=\"max-w-6xl mx-auto bg-white rounded-xl shadow-md overflow-hidden\">\r\n    <div class=\"overflow-x-auto\">\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Étudiant\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Groupe\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Projet\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Date de soumission\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Statut\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              Actions\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <tr *ngFor=\"let rendu of filteredRendus\">\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <div class=\"h-8 w-8 rounded-full bg-[#6C63FF] flex items-center justify-center text-white text-xs font-bold\">\r\n                  {{ rendu.etudiant?.nom?.charAt(0) }}{{ rendu.etudiant?.prenom?.charAt(0) }}\r\n                </div>\r\n                <div class=\"ml-4\">\r\n                  <div class=\"text-sm font-medium text-gray-900\">\r\n                    {{ rendu.etudiant?.nom }} {{ rendu.etudiant?.prenom }}\r\n                  </div>\r\n                  <div class=\"text-sm text-gray-500\">\r\n                    {{ rendu.etudiant?.email }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ rendu.etudiant?.group || 'Non spécifié' }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ rendu.projet?.titre }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ formatDate(rendu.dateSoumission) }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <span class=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full\"\r\n                    [ngClass]=\"getClasseStatut(rendu)\">\r\n                {{ getStatutEvaluation(rendu) }}\r\n              </span>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n              <div class=\"flex space-x-2\">\r\n                <!-- Si déjà évalué, afficher le bouton de visualisation -->\r\n                <a *ngIf=\"rendu.evaluation\"\r\n                   [routerLink]=\"['/admin/projects/evaluation-details', rendu._id]\"\r\n                   class=\"text-indigo-600 hover:text-indigo-900\">\r\n                  Voir l'évaluation\r\n                </a>\r\n\r\n                <!-- Si non évalué, afficher les boutons d'évaluation -->\r\n                <div *ngIf=\"!rendu.evaluation\" class=\"flex space-x-2\">\r\n                  <button (click)=\"evaluerRendu(rendu._id, 'manual')\"\r\n                          class=\"text-green-600 hover:text-green-900 bg-green-100 px-2 py-1 rounded\">\r\n                    Évaluer manuellement\r\n                  </button>\r\n                  <button (click)=\"evaluerRendu(rendu._id, 'ai')\"\r\n                          class=\"text-blue-600 hover:text-blue-900 bg-blue-100 px-2 py-1 rounded\">\r\n                    Évaluer par IA\r\n                  </button>\r\n                </div>\r\n\r\n                <!-- Bouton pour modifier l'évaluation si elle existe déjà -->\r\n                <button *ngIf=\"rendu.evaluation\"\r\n                  (click)=\"navigateToEditEvaluation(rendu._id)\"\r\n                  class=\"text-blue-600 hover:text-blue-800 mr-2\">\r\n                  Modifier l'évaluation\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Empty State -->\r\n  <div *ngIf=\"!isLoading && filteredRendus.length === 0\" class=\"max-w-6xl mx-auto text-center py-12\">\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-16 w-16 mx-auto text-[#bdc6cc]\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1\" d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n    </svg>\r\n    <h3 class=\"mt-4 text-lg font-medium text-[#6d6870]\">Aucun rendu disponible</h3>\r\n    <p class=\"mt-1 text-sm text-[#6d6870]\">Aucun rendu ne correspond à vos critères de filtrage</p>\r\n  </div>\r\n</div>\r\n\r\n"], "mappings": "AAIA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;ICoChDC,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAtCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAgB;IAACL,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,iBAAA,CAAAF,SAAA,CAAY;;;;;IAWpEL,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAI,SAAA,CAAAC,GAAA,CAAoB;IAACT,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAO,iBAAA,CAAAC,SAAA,CAAAE,KAAA,CAAkB;;;;;IAOtFV,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAW,SAAA,cAA8F;IAChGX,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAkH;IAChHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;;;;IA+Dcd,EAAA,CAAAC,cAAA,YAEiD;IAC/CD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAHDH,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAR,GAAA,EAAgE;;;;;;IAMnET,EAAA,CAAAC,cAAA,cAAsD;IAC5CD,EAAA,CAAAkB,UAAA,mBAAAC,yEAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAJ,QAAA,GAAAjB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAD,OAAA,CAAAE,YAAA,CAAAT,QAAA,CAAAR,GAAA,EAAwB,QAAQ,CAAC;IAAA,EAAC;IAEjDT,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBACgF;IADxED,EAAA,CAAAkB,UAAA,mBAAAS,yEAAA;MAAA3B,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAJ,QAAA,GAAAjB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAK,OAAA,GAAA5B,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAG,OAAA,CAAAF,YAAA,CAAAT,QAAA,CAAAR,GAAA,EAAwB,IAAI,CAAC;IAAA,EAAC;IAE7CT,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAIXH,EAAA,CAAAC,cAAA,iBAEiD;IAD/CD,EAAA,CAAAkB,UAAA,mBAAAW,4EAAA;MAAA7B,EAAA,CAAAoB,aAAA,CAAAU,IAAA;MAAA,MAAAb,QAAA,GAAAjB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAQ,OAAA,GAAA/B,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAyB,WAAA,CAAAM,OAAA,CAAAC,wBAAA,CAAAf,QAAA,CAAAR,GAAA,CAAmC;IAAA,EAAC;IAE7CT,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAzDfH,EAAA,CAAAC,cAAA,SAAyC;IAIjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAkB;IAEdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIZH,EAAA,CAAAC,cAAA,cAAwC;IACHD,EAAA,CAAAE,MAAA,IAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAExFH,EAAA,CAAAC,cAAA,cAAwC;IACHD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEpEH,EAAA,CAAAC,cAAA,cAAwC;IACHD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEjFH,EAAA,CAAAC,cAAA,cAAwC;IAGpCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAA4D;IAGxDD,EAAA,CAAAiC,UAAA,KAAAC,8CAAA,gBAII;IAGJlC,EAAA,CAAAiC,UAAA,KAAAE,gDAAA,kBASM;IAGNnC,EAAA,CAAAiC,UAAA,KAAAG,mDAAA,qBAIS;IACXpC,EAAA,CAAAG,YAAA,EAAM;;;;;IAtDFH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAqC,kBAAA,MAAApB,QAAA,CAAAqB,QAAA,kBAAArB,QAAA,CAAAqB,QAAA,CAAAC,GAAA,kBAAAtB,QAAA,CAAAqB,QAAA,CAAAC,GAAA,CAAAC,MAAA,SAAAvB,QAAA,CAAAqB,QAAA,kBAAArB,QAAA,CAAAqB,QAAA,CAAAG,MAAA,kBAAAxB,QAAA,CAAAqB,QAAA,CAAAG,MAAA,CAAAD,MAAA,SACF;IAGIxC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAqC,kBAAA,MAAApB,QAAA,CAAAqB,QAAA,kBAAArB,QAAA,CAAAqB,QAAA,CAAAC,GAAA,OAAAtB,QAAA,CAAAqB,QAAA,kBAAArB,QAAA,CAAAqB,QAAA,CAAAG,MAAA,MACF;IAEEzC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAK,QAAA,CAAAqB,QAAA,kBAAArB,QAAA,CAAAqB,QAAA,CAAAI,KAAA,MACF;IAK+B1C,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,iBAAA,EAAAU,QAAA,CAAAqB,QAAA,kBAAArB,QAAA,CAAAqB,QAAA,CAAAK,KAAA,8BAA6C;IAG7C3C,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,iBAAA,CAAAU,QAAA,CAAA2B,MAAA,kBAAA3B,QAAA,CAAA2B,MAAA,CAAAlC,KAAA,CAAyB;IAGzBV,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAO,iBAAA,CAAAsC,MAAA,CAAAC,UAAA,CAAA7B,QAAA,CAAA8B,cAAA,EAAsC;IAInE/C,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAyC,MAAA,CAAAG,eAAA,CAAA/B,QAAA,EAAkC;IACtCjB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAiC,MAAA,CAAAI,mBAAA,CAAAhC,QAAA,OACF;IAKMjB,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAa,QAAA,CAAAiC,UAAA,CAAsB;IAOpBlD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAI,UAAA,UAAAa,QAAA,CAAAiC,UAAA,CAAuB;IAYpBlD,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAa,QAAA,CAAAiC,UAAA,CAAsB;;;;;IA/E7ClD,EAAA,CAAAC,cAAA,cAA6H;IAMjHD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAuG;IACrGD,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAuG;IACrGD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGTH,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAiC,UAAA,KAAAkB,yCAAA,mBA4DK;IACPnD,EAAA,CAAAG,YAAA,EAAQ;;;;IA7DgBH,EAAA,CAAAM,SAAA,IAAiB;IAAjBN,EAAA,CAAAI,UAAA,YAAAgD,MAAA,CAAAC,cAAA,CAAiB;;;;;IAmE/CrD,EAAA,CAAAC,cAAA,cAAmG;IACjGD,EAAA,CAAAsD,cAAA,EAAuI;IAAvItD,EAAA,CAAAC,cAAA,cAAuI;IACrID,EAAA,CAAAW,SAAA,eAA+J;IACjKX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAuD,eAAA,EAAoD;IAApDvD,EAAA,CAAAC,cAAA,aAAoD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/EH,EAAA,CAAAC,cAAA,YAAuC;IAAAD,EAAA,CAAAE,MAAA,qEAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AD1JnG,OAAM,MAAOqD,mBAAmB;EAc9BC,YACUC,aAA4B,EAC5BC,aAA4B,EAC5BC,MAAc,EACdC,QAAkB;IAHlB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAjBlB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAT,cAAc,GAAU,EAAE;IAC1B,KAAAU,SAAS,GAAG,IAAI;IAChB,KAAAjD,KAAK,GAAG,EAAE;IACV,KAAAkD,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAoC,KAAK;IAErD;IACA,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAU,EAAE;EAOhB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAF,UAAUA,CAAA;IACR,IAAI,CAACR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACL,aAAa,CAACgB,YAAY,EAAE,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACf,MAAM,GAAGe,IAAI;QAClB,IAAI,CAACJ,cAAc,EAAE;QACrB,IAAI,CAACK,YAAY,EAAE;QACnB,IAAI,CAACf,SAAS,GAAG,KAAK;MACxB,CAAC;MACDjD,KAAK,EAAGiE,GAAG,IAAI;QACbC,OAAO,CAAClE,KAAK,CAAC,sCAAsC,EAAEiE,GAAG,CAAC;QAC1D,IAAI,CAACjE,KAAK,GACR,iEAAiE;QACnE,IAAI,CAACiD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAS,WAAWA,CAAA;IACT,IAAI,CAACb,aAAa,CAACsB,UAAU,EAAE,CAACN,SAAS,CAAC;MACxCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACR,OAAO,GAAGQ,IAAI;MACrB,CAAC;MACD/D,KAAK,EAAGiE,GAAG,IAAI;QACbC,OAAO,CAAClE,KAAK,CAAC,uCAAuC,EAAEiE,GAAG,CAAC;MAC7D;KACD,CAAC;EACJ;EAEAN,cAAcA,CAAA;IACZ;IACA,IAAI,IAAI,CAACX,MAAM,IAAI,IAAI,CAACA,MAAM,CAACoB,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,IAAI,CAACtB,MAAM,CAACuB,OAAO,CAAEC,KAAK,IAAI;QAC5B,IAAIA,KAAK,CAAChD,QAAQ,EAAEiD,MAAM,EAAE;UAC1BJ,UAAU,CAACK,GAAG,CAACF,KAAK,CAAChD,QAAQ,CAACiD,MAAM,CAAC;;MAEzC,CAAC,CAAC;MACF,IAAI,CAACnB,OAAO,GAAGqB,KAAK,CAACC,IAAI,CAACP,UAAU,CAAC;;EAEzC;EAEAL,YAAYA,CAAA;IACV,IAAIa,OAAO,GAAG,IAAI,CAAC7B,MAAM;IAEzB;IACA,IAAI,IAAI,CAACG,YAAY,KAAK,WAAW,EAAE;MACrC0B,OAAO,GAAGA,OAAO,CAACC,MAAM,CACrBN,KAAK,IAAKA,KAAK,CAACpC,UAAU,IAAIoC,KAAK,CAACpC,UAAU,CAAC2C,MAAM,CACvD;KACF,MAAM,IAAI,IAAI,CAAC5B,YAAY,KAAK,SAAS,EAAE;MAC1C0B,OAAO,GAAGA,OAAO,CAACC,MAAM,CACrBN,KAAK,IAAK,CAACA,KAAK,CAACpC,UAAU,IAAI,CAACoC,KAAK,CAACpC,UAAU,CAAC2C,MAAM,CACzD;;IAGH;IACA,IAAI,IAAI,CAAC7B,UAAU,CAAC8B,IAAI,EAAE,KAAK,EAAE,EAAE;MACjC,MAAMC,IAAI,GAAG,IAAI,CAAC/B,UAAU,CAACgC,WAAW,EAAE,CAACF,IAAI,EAAE;MACjDH,OAAO,GAAGA,OAAO,CAACC,MAAM,CACrBN,KAAK,IACJA,KAAK,CAAChD,QAAQ,EAAEC,GAAG,EAAEyD,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,IACjDT,KAAK,CAAChD,QAAQ,EAAEG,MAAM,EAAEuD,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,IACpDT,KAAK,CAAC1C,MAAM,EAAElC,KAAK,EAAEsF,WAAW,EAAE,CAACC,QAAQ,CAACF,IAAI,CAAC,CACpD;;IAGH;IACA,IAAI,IAAI,CAAC7B,YAAY,EAAE;MACrByB,OAAO,GAAGA,OAAO,CAACC,MAAM,CACrBN,KAAK,IAAKA,KAAK,CAAChD,QAAQ,EAAEiD,MAAM,KAAK,IAAI,CAACrB,YAAY,CACxD;;IAGH;IACA,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBwB,OAAO,GAAGA,OAAO,CAACC,MAAM,CACrBN,KAAK,IAAKA,KAAK,CAAC1C,MAAM,EAAEnC,GAAG,KAAK,IAAI,CAAC0D,YAAY,CACnD;;IAGH,IAAI,CAACd,cAAc,GAAGsC,OAAO;EAC/B;EAEA;EACAO,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC7C,cAAc;EAC5B;EAEA8C,cAAcA,CAAA;IACZ,IAAI,CAACrB,YAAY,EAAE;EACrB;EAEAsB,eAAeA,CAACC,MAAuC;IACrD,IAAI,CAACpC,YAAY,GAAGoC,MAAM;IAC1B,IAAI,CAACvB,YAAY,EAAE;EACrB;EAEAwB,aAAaA,CAACC,OAAe;IAC3B,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,0BAA0B,EAAED,OAAO,CAAC,CAAC;EAC7D;EAEA;EACA7E,YAAYA,CAAC6E,OAAe,EAAEE,IAAqB;IACjD;IACA,IAAI,CAAC7C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,0BAA0B,EAAED,OAAO,CAAC,EAAE;MAC1DG,WAAW,EAAE;QAAED,IAAI,EAAEA;MAAI;KAC1B,CAAC;EACJ;EAEAE,qBAAqBA,CAACJ,OAAe;IACnC,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,oCAAoC,EAAED,OAAO,CAAC,CAAC;EACvE;EAEAK,cAAcA,CAACtB,KAAU;IACvB,IAAIA,KAAK,CAACpC,UAAU,IAAIoC,KAAK,CAACpC,UAAU,CAAC2C,MAAM,EAAE;MAC/C,OAAO,6BAA6B;;IAEtC,OAAO,+BAA+B;EACxC;EAEA;EACA7C,eAAeA,CAACsC,KAAU;IACxB,OAAO,IAAI,CAACsB,cAAc,CAACtB,KAAK,CAAC;EACnC;EAEAuB,aAAaA,CAACvB,KAAU;IACtB;IACA,IAAIA,KAAK,CAACpC,UAAU,IAAIoC,KAAK,CAACpC,UAAU,CAACzC,GAAG,EAAE;MAC5C,OAAO,QAAQ;;IAEjB,IAAI6E,KAAK,CAACwB,MAAM,KAAK,QAAQ,EAAE;MAC7B,OAAO,QAAQ;;IAEjB,OAAO,YAAY;EACrB;EAEA;EACA7D,mBAAmBA,CAACqC,KAAU;IAC5B,OAAO,IAAI,CAACuB,aAAa,CAACvB,KAAK,CAAC;EAClC;EAEAyB,aAAaA,CAACzB,KAAU;IACtB,IAAI,CAACA,KAAK,CAACpC,UAAU,IAAI,CAACoC,KAAK,CAACpC,UAAU,CAAC2C,MAAM,EAAE,OAAO,CAAC;IAE3D,MAAMA,MAAM,GAAGP,KAAK,CAACpC,UAAU,CAAC2C,MAAM;IACtC,OACEA,MAAM,CAACmB,SAAS,GAChBnB,MAAM,CAACoB,SAAS,GAChBpB,MAAM,CAACqB,cAAc,GACrBrB,MAAM,CAACsB,WAAW;EAEtB;EAEAC,aAAaA,CAACC,KAAa;IACzB,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,OAAO,cAAc;EACvB;EAEAvE,UAAUA,CAACwE,IAAY;IACrB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,OAAO,IAAI,CAACzD,QAAQ,CAAC0D,SAAS,CAACD,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE;EAC1D;EAEAtF,wBAAwBA,CAACuE,OAAe;IACtC,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,iCAAiC,EAAED,OAAO,CAAC,CAAC;EACpE;EAEA;EACAiB,UAAUA,CAACC,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACxB,QAAQ,CAAC,GAAG,CAAC,IAAIwB,QAAQ,CAACxB,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAM0B,KAAK,GAAGF,QAAQ,CAACG,KAAK,CAAC,QAAQ,CAAC;MACtCF,QAAQ,GAAGC,KAAK,CAACA,KAAK,CAACzC,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGnF,WAAW,CAAC8H,UAAU,uBAAuBH,QAAQ,EAAE;EACnE;EAEAI,WAAWA,CAACL,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACxB,QAAQ,CAAC,GAAG,CAAC,IAAIwB,QAAQ,CAACxB,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAM0B,KAAK,GAAGF,QAAQ,CAACG,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACzC,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOuC,QAAQ;EACjB;;;uBAhOWjE,mBAAmB,EAAAxD,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAnI,EAAA,CAAA+H,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAArI,EAAA,CAAA+H,iBAAA,CAAAO,EAAA,CAAAxI,QAAA;IAAA;EAAA;;;YAAnB0D,mBAAmB;MAAA+E,SAAA;MAAAC,QAAA,GAAAxI,EAAA,CAAAyI,kBAAA,CAFnB,CAAC3I,QAAQ,CAAC;MAAA4I,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXvB/I,EAAA,CAAAC,cAAA,aAA8F;UAOlFD,EAAA,CAAAsD,cAAA,EAAsF;UAAtFtD,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAW,SAAA,cAAsM;UACxMX,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAuD,eAAA,EAAK;UAALvD,EAAA,CAAAC,cAAA,UAAK;UAC4CD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpEH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,6DAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG5EH,EAAA,CAAAC,cAAA,eAAiE;UAE7BD,EAAA,CAAAE,MAAA,IAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzDH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAElCH,EAAA,CAAAW,SAAA,eAAyC;UACzCX,EAAA,CAAAC,cAAA,eAAyB;UACSD,EAAA,CAAAE,MAAA,IAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC/DH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,yBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAO5CH,EAAA,CAAAC,cAAA,eAAsE;UAGJD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtFH,EAAA,CAAAC,cAAA,kBAIC;UAHCD,EAAA,CAAAkB,UAAA,2BAAA+H,8DAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA9E,YAAA,GAAAgF,MAAA;UAAA,EAA0B,2BAAAD,8DAAA;YAAA,OACTD,GAAA,CAAAlE,YAAA,EAAc;UAAA,EADL;UAI1B9E,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1CH,EAAA,CAAAiC,UAAA,KAAAkH,sCAAA,qBAA6E;UAC/EnJ,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,WAAK;UACyDD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtFH,EAAA,CAAAC,cAAA,kBAIC;UAHCD,EAAA,CAAAkB,UAAA,2BAAAkI,8DAAAF,MAAA;YAAA,OAAAF,GAAA,CAAA7E,YAAA,GAAA+E,MAAA;UAAA,EAA0B,2BAAAE,8DAAA;YAAA,OACTJ,GAAA,CAAAlE,YAAA,EAAc;UAAA,EADL;UAI1B9E,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1CH,EAAA,CAAAiC,UAAA,KAAAoH,sCAAA,qBAAuF;UACzFrJ,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAiC,UAAA,KAAAqH,mCAAA,kBAEM;UAGNtJ,EAAA,CAAAiC,UAAA,KAAAsH,mCAAA,kBAEM;UAGNvJ,EAAA,CAAAiC,UAAA,KAAAuH,mCAAA,mBA0FM;UAGNxJ,EAAA,CAAAiC,UAAA,KAAAwH,mCAAA,kBAMM;UACRzJ,EAAA,CAAAG,YAAA,EAAM;;;UAvJsCH,EAAA,CAAAM,SAAA,IAAmB;UAAnBN,EAAA,CAAAO,iBAAA,CAAAyI,GAAA,CAAAlF,MAAA,CAAAoB,MAAA,CAAmB;UAKnBlF,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAO,iBAAA,CAAAyI,GAAA,CAAAU,iBAAA,GAAyB;UAa3D1J,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA4I,GAAA,CAAA9E,YAAA,CAA0B;UAKClE,EAAA,CAAAM,SAAA,GAAU;UAAVN,EAAA,CAAAI,UAAA,YAAA4I,GAAA,CAAA5E,OAAA,CAAU;UAMrCpE,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA4I,GAAA,CAAA7E,YAAA,CAA0B;UAKCnE,EAAA,CAAAM,SAAA,GAAU;UAAVN,EAAA,CAAAI,UAAA,YAAA4I,GAAA,CAAA3E,OAAA,CAAU;UAOvCrE,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAjF,SAAA,CAAe;UAKf/D,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAI,UAAA,SAAA4I,GAAA,CAAAlI,KAAA,CAAW;UAKXd,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAAI,UAAA,UAAA4I,GAAA,CAAAjF,SAAA,IAAAiF,GAAA,CAAA3F,cAAA,CAAA6B,MAAA,KAA6C;UA6F7ClF,EAAA,CAAAM,SAAA,GAA+C;UAA/CN,EAAA,CAAAI,UAAA,UAAA4I,GAAA,CAAAjF,SAAA,IAAAiF,GAAA,CAAA3F,cAAA,CAAA6B,MAAA,OAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}