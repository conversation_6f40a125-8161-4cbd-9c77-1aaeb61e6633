<div class="min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300">
  <div class="container mx-auto px-4 py-8">
    <!-- Header avec gradient -->
    <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white mb-2">Liste des Évaluations</h1>
            <p class="text-white/80">Gestion et suivi des évaluations de projets</p>
          </div>
        </div>
        <div class="hidden md:flex items-center space-x-4 text-white/80">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ evaluations.length }}</div>
            <div class="text-sm">Total</div>
          </div>
          <div class="w-px h-12 bg-white/20"></div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ getAverageScore() }}</div>
            <div class="text-sm">Moyenne</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Barre de recherche globale -->
    <div *ngIf="!isLoading && !error" class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-4 mb-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50">
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="w-5 h-5 text-gray-400 dark:text-dark-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
        <input type="text"
               [(ngModel)]="searchTerm"
               (input)="onSearchChange()"
               class="block w-full pl-10 pr-3 py-3 border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl leading-5 bg-white dark:bg-dark-bg-secondary text-text-dark dark:text-dark-text-primary placeholder-gray-500 dark:placeholder-dark-text-secondary focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200"
               placeholder="Rechercher par nom, email, projet ou groupe...">
        <div *ngIf="searchTerm" class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <button (click)="clearSearch()" class="text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text-primary transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
      <div *ngIf="searchTerm" class="mt-2 text-sm text-text dark:text-dark-text-secondary">
        {{ filteredEvaluations.length }} résultat(s) trouvé(s) pour "{{ searchTerm }}"
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex flex-col items-center justify-center py-16">
      <div class="relative">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 dark:border-dark-accent-primary/30"></div>
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-transparent border-t-primary dark:border-t-dark-accent-primary absolute top-0 left-0"></div>
      </div>
      <p class="mt-4 text-text dark:text-dark-text-secondary animate-pulse">Chargement des évaluations...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-6 mb-6 backdrop-blur-sm">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <svg class="w-5 h-5 text-danger dark:text-danger-dark flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <p class="font-medium">{{ error }}</p>
        </div>
        <button (click)="loadEvaluations()"
          class="px-4 py-2 bg-danger/20 dark:bg-danger-dark/20 text-danger dark:text-danger-dark rounded-lg hover:bg-danger/30 dark:hover:bg-danger-dark/30 transition-colors font-medium">
          Réessayer
        </button>
      </div>
    </div>

    <!-- Filtres modernes -->
    <div *ngIf="!isLoading && !error" class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50">
      <div class="flex items-center space-x-3 mb-6">
        <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
          </svg>
        </div>
        <h2 class="text-xl font-bold text-text-dark dark:text-dark-text-primary">Filtres et recherche</h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Filtre par groupe -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <span>Filtrer par groupe</span>
            </div>
          </label>
          <select [(ngModel)]="filterGroupe" (change)="applyFilters()"
            class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary">
            <option value="">Tous les groupes ({{ groupes.length }})</option>
            <option *ngFor="let groupe of groupes" [value]="groupe">{{ groupe }}</option>
          </select>
        </div>

        <!-- Filtre par projet -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
              </svg>
              <span>Filtrer par projet</span>
            </div>
          </label>
          <select [(ngModel)]="filterProjet" (change)="applyFilters()"
            class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary">
            <option value="">Tous les projets ({{ projets.length }})</option>
            <option *ngFor="let projet of projets" [value]="projet._id">{{ projet.titre }}</option>
          </select>
        </div>

        <!-- Actions -->
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span>Actions rapides</span>
            </div>
          </label>
          <div class="flex flex-col space-y-2">
            <button (click)="resetFilters()"
              class="px-4 py-3 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span>Réinitialiser</span>
              </div>
            </button>
            <button (click)="updateMissingGroups()"
              class="px-4 py-3 bg-gradient-to-r from-info to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
                <span>Maj groupes</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Liste des évaluations en cartes modernes -->
    <div *ngIf="!isLoading && !error && filteredEvaluations.length > 0" class="space-y-6">
      <div *ngFor="let evaluation of filteredEvaluations" class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">

          <!-- Informations étudiant -->
          <div class="flex items-center space-x-4">
            <div class="relative">
              <div class="h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center text-white text-lg font-bold shadow-lg">
                {{ getStudentInitials(evaluation.etudiant) }}
              </div>
              <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-bold text-text-dark dark:text-dark-text-primary">
                {{ getStudentName(evaluation.etudiant) }}
              </h3>
              <p class="text-sm text-text dark:text-dark-text-secondary">{{ evaluation.etudiant?.email || 'Email non disponible' }}</p>
              <div class="flex items-center space-x-4 mt-2">
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <span class="text-sm font-medium text-text-dark dark:text-dark-text-primary">
                    {{ getStudentGroup(evaluation.etudiant) }}
                  </span>
                </div>
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                  </svg>
                  <span class="text-sm font-medium text-text-dark dark:text-dark-text-primary">
                    {{ getProjectTitle(evaluation) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Informations de l'évaluation -->
          <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-6">
            <!-- Date d'évaluation -->
            <div class="flex items-center space-x-2">
              <div class="bg-info/10 dark:bg-dark-accent-primary/10 p-2 rounded-lg">
                <svg class="w-4 h-4 text-info dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs text-text dark:text-dark-text-secondary">Évaluée le</p>
                <p class="text-sm font-semibold text-text-dark dark:text-dark-text-primary">{{ formatDate(evaluation.dateEvaluation) }}</p>
              </div>
            </div>

            <!-- Score -->
            <div class="flex items-center space-x-2">
              <div [ngClass]="getScoreIconClass(getScoreTotal(evaluation))" class="p-2 rounded-lg">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs text-text dark:text-dark-text-secondary">Score total</p>
                <span [ngClass]="getScoreColorClass(getScoreTotal(evaluation))" class="text-lg font-bold">
                  {{ getScoreTotal(evaluation) }}/20
                </span>
              </div>
            </div>

            <!-- Détails des scores -->
            <div class="bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-lg p-3">
              <p class="text-xs text-text dark:text-dark-text-secondary mb-1">Détail des scores</p>
              <div class="grid grid-cols-2 gap-1 text-xs">
                <div class="flex justify-between">
                  <span>Structure:</span>
                  <span class="font-medium">{{ evaluation.scores.structure || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Pratiques:</span>
                  <span class="font-medium">{{ evaluation.scores.pratiques || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Fonctionnalité:</span>
                  <span class="font-medium">{{ evaluation.scores.fonctionnalite || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Originalité:</span>
                  <span class="font-medium">{{ evaluation.scores.originalite || 0 }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col sm:flex-row gap-2">
            <button (click)="viewEvaluationDetails(evaluation.rendu)"
                    class="group/btn px-4 py-2 bg-gradient-to-r from-info to-primary dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <span>Voir détails</span>
              </div>
            </button>
            <button (click)="editEvaluation(evaluation.rendu)"
                    class="group/btn px-4 py-2 bg-gradient-to-r from-secondary to-primary-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                <span>Modifier</span>
              </div>
            </button>
            <button (click)="deleteEvaluation(evaluation._id)"
                    class="group/btn px-4 py-2 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4 group-hover/btn:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span>Supprimer</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State moderne -->
    <div *ngIf="!isLoading && !error && filteredEvaluations.length === 0" class="text-center py-16">
      <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto">
        <div class="bg-gradient-to-br from-primary/10 to-secondary/10 dark:from-dark-accent-primary/20 dark:to-dark-accent-secondary/20 rounded-2xl p-6 mb-6">
          <svg class="h-16 w-16 mx-auto text-primary dark:text-dark-accent-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2">Aucune évaluation trouvée</h3>
        <p class="text-text dark:text-dark-text-secondary mb-4">Aucune évaluation ne correspond à vos critères de filtrage actuels.</p>
        <button (click)="resetFilters()" class="px-6 py-2 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
          <div class="flex items-center justify-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>Réinitialiser les filtres</span>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>



