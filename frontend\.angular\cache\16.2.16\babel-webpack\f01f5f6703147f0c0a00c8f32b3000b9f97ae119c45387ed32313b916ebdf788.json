{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction AddProjectComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Le titre est requis\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProjectComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La description est requise\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProjectComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La date limite est requise\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProjectComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Le groupe est requis\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProjectComponent_div_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Groupe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"opacity-50 cursor-not-allowed\": a0\n  };\n};\nexport class AddProjectComponent {\n  constructor(fb, projetService, router, authService) {\n    this.fb = fb;\n    this.projetService = projetService;\n    this.router = router;\n    this.authService = authService;\n    this.selectedFiles = [];\n    this.isSubmitting = false;\n    this.projetForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      dateLimite: ['', Validators.required],\n      fichiers: [null],\n      groupe: ['', Validators.required] // ← champ pour l'ID du groupe\n    });\n  }\n\n  onFileChange(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = Array.from(input.files);\n    }\n  }\n  onSubmit() {\n    if (this.projetForm.invalid) return;\n    this.isSubmitting = true;\n    console.log('Soumission du formulaire de projet');\n    const formData = new FormData();\n    formData.append('titre', this.projetForm.value.titre);\n    formData.append('description', this.projetForm.value.description || '');\n    formData.append('dateLimite', this.projetForm.value.dateLimite);\n    formData.append('groupe', this.projetForm.value.groupe);\n    // Méthode 1: Via le service d'authentification (recommandée)\n    const userIdFromService = this.authService.getCurrentUserId();\n    // Méthode 2: Via le currentUser du service\n    const currentUser = this.authService.getCurrentUser();\n    // Méthode 3: Vérification localStorage\n    const user = localStorage.getItem('user');\n    // Utiliser l'ID du service d'authentification en priorité\n    let userId = userIdFromService;\n    if (!userId && currentUser) {\n      userId = currentUser._id || currentUser.id;\n    }\n    if (!userId && user) {\n      userId = JSON.parse(user).id;\n    }\n    if (userId) {\n      formData.append('professeur', userId);\n    } else {\n      alert(\"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\");\n      return;\n    }\n    this.selectedFiles.forEach(file => {\n      formData.append('fichiers', file);\n    });\n    console.log('Données du formulaire:', {\n      titre: this.projetForm.value.titre,\n      description: this.projetForm.value.description,\n      dateLimite: this.projetForm.value.dateLimite,\n      groupe: this.projetForm.value.groupe,\n      fichiers: this.selectedFiles.map(f => f.name)\n    });\n    this.projetService.addProjet(formData).subscribe({\n      next: () => {\n        console.log('Projet ajouté avec succès');\n        alert('Projet ajouté avec succès');\n        this.router.navigate(['/admin/projects']);\n      },\n      error: err => {\n        console.error(\"Erreur lors de l'ajout du projet:\", err);\n        alert(\"Erreur lors de l'ajout du projet: \" + (err.error?.message || err.message || 'Erreur inconnue'));\n        this.isSubmitting = false;\n      },\n      complete: () => {\n        this.isSubmitting = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AddProjectComponent_Factory(t) {\n      return new (t || AddProjectComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddProjectComponent,\n      selectors: [[\"app-add-project\"]],\n      decls: 123,\n      vars: 10,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"overflow-hidden\"], [1, \"p-8\"], [\"enctype\", \"multipart/form-data\", 1, \"space-y-8\", 3, \"formGroup\", \"ngSubmit\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-lg\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"space-y-2\"], [\"for\", \"titre\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"], [1, \"text-danger\", \"dark:text-danger-dark\"], [1, \"relative\"], [\"type\", \"text\", \"id\", \"titre\", \"formControlName\", \"titre\", \"placeholder\", \"Ex: D\\u00E9veloppement d'une application web\", 1, \"w-full\", \"px-4\", \"py-3\", \"pl-12\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-400\", \"dark:placeholder-dark-text-secondary\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [\"class\", \"flex items-center space-x-2 text-danger dark:text-danger-dark text-sm\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez les objectifs, les livrables attendus et les crit\\u00E8res d'\\u00E9valuation...\", 1, \"w-full\", \"px-4\", \"py-3\", \"pl-12\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-400\", \"dark:placeholder-dark-text-secondary\", \"resize-none\"], [1, \"absolute\", \"top-3\", \"left-0\", \"pl-3\", \"flex\", \"items-start\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\", \"dark:text-dark-text-secondary\", \"mt-0.5\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"dateLimite\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"type\", \"date\", \"id\", \"dateLimite\", \"formControlName\", \"dateLimite\", 1, \"w-full\", \"px-4\", \"py-3\", \"pl-12\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"for\", \"groupe\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"id\", \"groupe\", \"formControlName\", \"groupe\", 1, \"w-full\", \"px-4\", \"py-3\", \"pl-12\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"appearance-none\"], [\"value\", \"\"], [\"value\", \"1cinfo\"], [\"value\", \"2cinfo1\"], [\"value\", \"2cinfo2\"], [\"value\", \"2cinfo3\"], [\"value\", \"tous\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [\"for\", \"fichiers\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"w-full\", \"px-4\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"bg-white\", \"focus:outline-none\", 3, \"change\"], [\"for\", \"groupe\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"id\", \"groupe\", \"formControlName\", \"groupe\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"bg-white\", \"transition-all\"], [\"class\", \"text-[#ff6b69] text-sm mt-1\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"bg-[#7826b5]\", \"hover:bg-[#4f5fad]\", \"text-white\", \"font-bold\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"focus:ring-offset-2\", 3, \"disabled\", \"ngClass\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-danger\", \"dark:text-danger-dark\", \"text-sm\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-[#ff6b69]\", \"text-sm\", \"mt-1\"]],\n      template: function AddProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n          i0.ɵɵtext(5, \"Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 5);\n          i0.ɵɵelement(7, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9, \"Nouveau projet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(13, \"svg\", 11);\n          i0.ɵɵelement(14, \"path\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(15, \"div\")(16, \"h1\", 13);\n          i0.ɵɵtext(17, \" Cr\\u00E9er un nouveau projet \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"p\", 14);\n          i0.ɵɵtext(19, \" Ajoutez un projet pour organiser le travail de vos \\u00E9tudiants \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(20, \"div\", 15)(21, \"div\", 16)(22, \"div\", 17)(23, \"form\", 18);\n          i0.ɵɵlistener(\"ngSubmit\", function AddProjectComponent_Template_form_ngSubmit_23_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(24, \"div\", 19)(25, \"div\", 20)(26, \"div\", 21);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(27, \"svg\", 22);\n          i0.ɵɵelement(28, \"path\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(29, \"h3\", 24);\n          i0.ɵɵtext(30, \"Informations g\\u00E9n\\u00E9rales\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 25)(32, \"label\", 26);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(33, \"svg\", 27);\n          i0.ɵɵelement(34, \"path\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(35, \"span\");\n          i0.ɵɵtext(36, \"Titre du projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 29);\n          i0.ɵɵtext(38, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 30);\n          i0.ɵɵelement(40, \"input\", 31);\n          i0.ɵɵelementStart(41, \"div\", 32);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(42, \"svg\", 33);\n          i0.ɵɵelement(43, \"path\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(44, AddProjectComponent_div_44_Template, 5, 0, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(45, \"div\", 25)(46, \"label\", 35);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(47, \"svg\", 27);\n          i0.ɵɵelement(48, \"path\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(49, \"span\");\n          i0.ɵɵtext(50, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 29);\n          i0.ɵɵtext(52, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 30);\n          i0.ɵɵelement(54, \"textarea\", 37);\n          i0.ɵɵelementStart(55, \"div\", 38);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(56, \"svg\", 39);\n          i0.ɵɵelement(57, \"path\", 36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(58, AddProjectComponent_div_58_Template, 5, 0, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(59, \"div\", 40)(60, \"div\", 25)(61, \"label\", 41);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(62, \"svg\", 27);\n          i0.ɵɵelement(63, \"path\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(64, \"span\");\n          i0.ɵɵtext(65, \"Date limite\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"span\", 29);\n          i0.ɵɵtext(67, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 30);\n          i0.ɵɵelement(69, \"input\", 43);\n          i0.ɵɵelementStart(70, \"div\", 32);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(71, \"svg\", 33);\n          i0.ɵɵelement(72, \"path\", 42);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(73, AddProjectComponent_div_73_Template, 5, 0, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(74, \"div\", 25)(75, \"label\", 44);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(76, \"svg\", 27);\n          i0.ɵɵelement(77, \"path\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(78, \"span\");\n          i0.ɵɵtext(79, \"Groupe cible\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"span\", 29);\n          i0.ɵɵtext(81, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 30)(83, \"select\", 46)(84, \"option\", 47);\n          i0.ɵɵtext(85, \"S\\u00E9lectionner un groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"option\", 48);\n          i0.ɵɵtext(87, \"1cinfo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"option\", 49);\n          i0.ɵɵtext(89, \"2cinfo1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"option\", 50);\n          i0.ɵɵtext(91, \"2cinfo2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"option\", 51);\n          i0.ɵɵtext(93, \"2cinfo3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"option\", 52);\n          i0.ɵɵtext(95, \"Tous les groupes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"div\", 32);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(97, \"svg\", 33);\n          i0.ɵɵelement(98, \"path\", 45);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(99, \"div\", 53);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(100, \"svg\", 33);\n          i0.ɵɵelement(101, \"path\", 54);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(102, AddProjectComponent_div_102_Template, 5, 0, \"div\", 34);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(103, \"div\")(104, \"label\", 55);\n          i0.ɵɵtext(105, \"Fichiers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"input\", 56);\n          i0.ɵɵlistener(\"change\", function AddProjectComponent_Template_input_change_106_listener($event) {\n            return ctx.onFileChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\")(108, \"label\", 57);\n          i0.ɵɵtext(109, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"select\", 58)(111, \"option\", 47);\n          i0.ɵɵtext(112, \"-- Choisir un groupe --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"option\", 49);\n          i0.ɵɵtext(114, \"2cinfo1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"option\", 50);\n          i0.ɵɵtext(116, \"2cinfo2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"option\", 51);\n          i0.ɵɵtext(118, \"2cinfo3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(119, AddProjectComponent_div_119_Template, 2, 0, \"div\", 59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"div\")(121, \"button\", 60);\n          i0.ɵɵtext(122, \" Ajouter \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"formGroup\", ctx.projetForm);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.projetForm.invalid)(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx.projetForm.invalid));\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJhZGQtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvYWRkLXByb2plY3QvYWRkLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0Esd0tBQXdLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "AddProjectComponent", "constructor", "fb", "projetService", "router", "authService", "selectedFiles", "isSubmitting", "projetForm", "group", "titre", "required", "description", "dateLimite", "fichiers", "groupe", "onFileChange", "event", "input", "target", "files", "Array", "from", "onSubmit", "invalid", "console", "log", "formData", "FormData", "append", "value", "userIdFromService", "getCurrentUserId", "currentUser", "getCurrentUser", "user", "localStorage", "getItem", "userId", "_id", "id", "JSON", "parse", "alert", "for<PERSON>ach", "file", "map", "f", "name", "addProjet", "subscribe", "next", "navigate", "error", "err", "message", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProjetService", "i3", "Router", "i4", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "AddProjectComponent_Template", "rf", "ctx", "ɵɵlistener", "AddProjectComponent_Template_form_ngSubmit_23_listener", "ɵɵtemplate", "AddProjectComponent_div_44_Template", "AddProjectComponent_div_58_Template", "AddProjectComponent_div_73_Template", "AddProjectComponent_div_102_Template", "AddProjectComponent_Template_input_change_106_listener", "$event", "AddProjectComponent_div_119_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "ɵɵpureFunction1", "_c0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\add-project\\add-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\add-project\\add-project.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { AuthuserService } from '@app/services/authuser.service';\r\n\r\n@Component({\r\n  selector: 'app-add-project',\r\n  templateUrl: './add-project.component.html',\r\n  styleUrls: ['./add-project.component.css'],\r\n})\r\nexport class AddProjectComponent {\r\n  projetForm: FormGroup;\r\n  selectedFiles: File[] = [];\r\n  isSubmitting = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private projetService: ProjetService,\r\n    private router: Router,\r\n    private authService: AuthuserService\r\n  ) {\r\n    this.projetForm = this.fb.group({\r\n      titre: ['', Validators.required],\r\n      description: [''],\r\n      dateLimite: ['', Validators.required],\r\n      fichiers: [null],\r\n      groupe: ['', Validators.required], // ← champ pour l'ID du groupe\r\n    });\r\n  }\r\n\r\n  onFileChange(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files) {\r\n      this.selectedFiles = Array.from(input.files);\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.projetForm.invalid) return;\r\n\r\n    this.isSubmitting = true;\r\n    console.log('Soumission du formulaire de projet');\r\n\r\n    const formData = new FormData();\r\n    formData.append('titre', this.projetForm.value.titre);\r\n    formData.append('description', this.projetForm.value.description || '');\r\n    formData.append('dateLimite', this.projetForm.value.dateLimite);\r\n    formData.append('groupe', this.projetForm.value.groupe);\r\n\r\n    // Méthode 1: Via le service d'authentification (recommandée)\r\n    const userIdFromService = this.authService.getCurrentUserId();\r\n    // Méthode 2: Via le currentUser du service\r\n    const currentUser = this.authService.getCurrentUser();\r\n\r\n    // Méthode 3: Vérification localStorage\r\n    const user = localStorage.getItem('user');\r\n    // Utiliser l'ID du service d'authentification en priorité\r\n    let userId = userIdFromService;\r\n    if (!userId && currentUser) {\r\n      userId = currentUser._id || currentUser.id;\r\n    }\r\n    if (!userId && user) {\r\n      userId = JSON.parse(user).id;\r\n    }\r\n\r\n    if (userId) {\r\n      formData.append('professeur', userId);\r\n    } else {\r\n      alert(\r\n        \"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    this.selectedFiles.forEach((file) => {\r\n      formData.append('fichiers', file);\r\n    });\r\n\r\n    console.log('Données du formulaire:', {\r\n      titre: this.projetForm.value.titre,\r\n      description: this.projetForm.value.description,\r\n      dateLimite: this.projetForm.value.dateLimite,\r\n      groupe: this.projetForm.value.groupe,\r\n      fichiers: this.selectedFiles.map((f) => f.name),\r\n    });\r\n\r\n    this.projetService.addProjet(formData).subscribe({\r\n      next: () => {\r\n        console.log('Projet ajouté avec succès');\r\n        alert('Projet ajouté avec succès');\r\n        this.router.navigate(['/admin/projects']);\r\n      },\r\n      error: (err) => {\r\n        console.error(\"Erreur lors de l'ajout du projet:\", err);\r\n        alert(\r\n          \"Erreur lors de l'ajout du projet: \" +\r\n            (err.error?.message || err.message || 'Erreur inconnue')\r\n        );\r\n        this.isSubmitting = false;\r\n      },\r\n      complete: () => {\r\n        this.isSubmitting = false;\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n\r\n    <!-- Header moderne avec breadcrumb -->\r\n    <div class=\"mb-8\">\r\n      <nav class=\"flex items-center space-x-2 text-sm text-text dark:text-dark-text-secondary mb-4\">\r\n        <a routerLink=\"/admin/projects/list-project\" class=\"hover:text-primary dark:hover:text-dark-accent-primary transition-colors\">Projets</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <span class=\"text-primary dark:text-dark-accent-primary font-medium\">Nouveau projet</span>\r\n      </nav>\r\n\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\r\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\r\n              Créer un nouveau projet\r\n            </h1>\r\n            <p class=\"text-text dark:text-dark-text-secondary\">\r\n              Ajoutez un projet pour organiser le travail de vos étudiants\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Formulaire moderne -->\r\n    <div class=\"max-w-4xl mx-auto\">\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 overflow-hidden\">\r\n\r\n        <!-- Contenu du formulaire -->\r\n        <div class=\"p-8\">\r\n          <form [formGroup]=\"projetForm\" (ngSubmit)=\"onSubmit()\" enctype=\"multipart/form-data\" class=\"space-y-8\">\r\n\r\n            <!-- Section informations générales -->\r\n            <div class=\"space-y-6\">\r\n              <div class=\"flex items-center space-x-3 mb-6\">\r\n                <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\r\n                  <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Informations générales</h3>\r\n              </div>\r\n\r\n              <!-- Titre -->\r\n              <div class=\"space-y-2\">\r\n                <label for=\"titre\" class=\"flex items-center space-x-2 text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"></path>\r\n                  </svg>\r\n                  <span>Titre du projet</span>\r\n                  <span class=\"text-danger dark:text-danger-dark\">*</span>\r\n                </label>\r\n                <div class=\"relative\">\r\n                  <input type=\"text\" id=\"titre\" formControlName=\"titre\" placeholder=\"Ex: Développement d'une application web\"\r\n                         class=\"w-full px-4 py-3 pl-12 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-gray-400 dark:placeholder-dark-text-secondary\">\r\n                  <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                    <svg class=\"w-5 h-5 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                </div>\r\n                <div *ngIf=\"projetForm.get('titre')?.invalid && projetForm.get('titre')?.touched\"\r\n                     class=\"flex items-center space-x-2 text-danger dark:text-danger-dark text-sm\">\r\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span>Le titre est requis</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Description -->\r\n              <div class=\"space-y-2\">\r\n                <label for=\"description\" class=\"flex items-center space-x-2 text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\r\n                  </svg>\r\n                  <span>Description</span>\r\n                  <span class=\"text-danger dark:text-danger-dark\">*</span>\r\n                </label>\r\n                <div class=\"relative\">\r\n                  <textarea id=\"description\" formControlName=\"description\" rows=\"4\" placeholder=\"Décrivez les objectifs, les livrables attendus et les critères d'évaluation...\"\r\n                            class=\"w-full px-4 py-3 pl-12 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-gray-400 dark:placeholder-dark-text-secondary resize-none\"></textarea>\r\n                  <div class=\"absolute top-3 left-0 pl-3 flex items-start pointer-events-none\">\r\n                    <svg class=\"w-5 h-5 text-gray-400 dark:text-dark-text-secondary mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\r\n                    </svg>\r\n                  </div>\r\n                </div>\r\n                <div *ngIf=\"projetForm.get('description')?.invalid && projetForm.get('description')?.touched\"\r\n                     class=\"flex items-center space-x-2 text-danger dark:text-danger-dark text-sm\">\r\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span>La description est requise</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Grille pour date et groupe -->\r\n              <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <!-- Date limite -->\r\n                <div class=\"space-y-2\">\r\n                  <label for=\"dateLimite\" class=\"flex items-center space-x-2 text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n                    <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\r\n                    </svg>\r\n                    <span>Date limite</span>\r\n                    <span class=\"text-danger dark:text-danger-dark\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input type=\"date\" id=\"dateLimite\" formControlName=\"dateLimite\"\r\n                           class=\"w-full px-4 py-3 pl-12 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary\">\r\n                    <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <svg class=\"w-5 h-5 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"projetForm.get('dateLimite')?.invalid && projetForm.get('dateLimite')?.touched\"\r\n                       class=\"flex items-center space-x-2 text-danger dark:text-danger-dark text-sm\">\r\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                    <span>La date limite est requise</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Groupe -->\r\n                <div class=\"space-y-2\">\r\n                  <label for=\"groupe\" class=\"flex items-center space-x-2 text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n                    <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                    </svg>\r\n                    <span>Groupe cible</span>\r\n                    <span class=\"text-danger dark:text-danger-dark\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <select id=\"groupe\" formControlName=\"groupe\"\r\n                            class=\"w-full px-4 py-3 pl-12 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary appearance-none\">\r\n                      <option value=\"\">Sélectionner un groupe</option>\r\n                      <option value=\"1cinfo\">1cinfo</option>\r\n                      <option value=\"2cinfo1\">2cinfo1</option>\r\n                      <option value=\"2cinfo2\">2cinfo2</option>\r\n                      <option value=\"2cinfo3\">2cinfo3</option>\r\n                      <option value=\"tous\">Tous les groupes</option>\r\n                    </select>\r\n                    <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <svg class=\"w-5 h-5 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <div class=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\r\n                      <svg class=\"w-5 h-5 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"></path>\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"projetForm.get('groupe')?.invalid && projetForm.get('groupe')?.touched\"\r\n                       class=\"flex items-center space-x-2 text-danger dark:text-danger-dark text-sm\">\r\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                    <span>Le groupe est requis</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n        <!-- Fichiers -->\r\n        <div>\r\n          <label\r\n            for=\"fichiers\"\r\n            class=\"block text-sm font-medium text-[#6d6870] mb-1\"\r\n            >Fichiers</label\r\n          >\r\n          <input\r\n            type=\"file\"\r\n            id=\"fichiers\"\r\n            (change)=\"onFileChange($event)\"\r\n            multiple\r\n            class=\"w-full px-4 py-2 rounded-lg border border-[#bdc6cc] bg-white focus:outline-none\"\r\n          />\r\n        </div>\r\n\r\n        <!-- Groupe -->\r\n        <div>\r\n          <label\r\n            for=\"groupe\"\r\n            class=\"block text-sm font-medium text-[#6d6870] mb-1\"\r\n            >Groupe</label\r\n          >\r\n          <select\r\n            id=\"groupe\"\r\n            formControlName=\"groupe\"\r\n            class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] focus:border-[#7826b5] focus:ring-2 focus:ring-[#dac4ea] bg-white transition-all\"\r\n          >\r\n            <option value=\"\">-- Choisir un groupe --</option>\r\n            <option value=\"2cinfo1\">2cinfo1</option>\r\n            <option value=\"2cinfo2\">2cinfo2</option>\r\n            <option value=\"2cinfo3\">2cinfo3</option>\r\n          </select>\r\n          <div\r\n            *ngIf=\"\r\n              projetForm.get('groupe')?.invalid &&\r\n              projetForm.get('groupe')?.touched\r\n            \"\r\n            class=\"text-[#ff6b69] text-sm mt-1\"\r\n          >\r\n            Groupe est requis\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Submit Button -->\r\n        <div>\r\n          <button\r\n            type=\"submit\"\r\n            class=\"w-full bg-[#7826b5] hover:bg-[#4f5fad] text-white font-bold py-3 px-4 rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-[#dac4ea] focus:ring-offset-2\"\r\n            [disabled]=\"projetForm.invalid\"\r\n            [ngClass]=\"{ 'opacity-50 cursor-not-allowed': projetForm.invalid }\"\r\n          >\r\n            Ajouter\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICqEnDC,EAAA,CAAAC,eAAA,EACmF;IADnFD,EAAA,CAAAE,cAAA,cACmF;IACjFF,EAAA,CAAAG,cAAA,EAA2E;IAA3EH,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAAI,SAAA,eAAmI;IACrIJ,EAAA,CAAAK,YAAA,EAAM;IACNL,EAAA,CAAAC,eAAA,EAAM;IAAND,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAM,MAAA,0BAAmB;IAAAN,EAAA,CAAAK,YAAA,EAAO;;;;;;IAsBlCL,EAAA,CAAAC,eAAA,EACmF;IADnFD,EAAA,CAAAE,cAAA,cACmF;IACjFF,EAAA,CAAAG,cAAA,EAA2E;IAA3EH,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAAI,SAAA,eAAmI;IACrIJ,EAAA,CAAAK,YAAA,EAAM;IACNL,EAAA,CAAAC,eAAA,EAAM;IAAND,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAM,MAAA,iCAA0B;IAAAN,EAAA,CAAAK,YAAA,EAAO;;;;;;IAwBvCL,EAAA,CAAAC,eAAA,EACmF;IADnFD,EAAA,CAAAE,cAAA,cACmF;IACjFF,EAAA,CAAAG,cAAA,EAA2E;IAA3EH,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAAI,SAAA,eAAmI;IACrIJ,EAAA,CAAAK,YAAA,EAAM;IACNL,EAAA,CAAAC,eAAA,EAAM;IAAND,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAM,MAAA,iCAA0B;IAAAN,EAAA,CAAAK,YAAA,EAAO;;;;;;IAkCzCL,EAAA,CAAAC,eAAA,EACmF;IADnFD,EAAA,CAAAE,cAAA,cACmF;IACjFF,EAAA,CAAAG,cAAA,EAA2E;IAA3EH,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAAI,SAAA,eAAmI;IACrIJ,EAAA,CAAAK,YAAA,EAAM;IACNL,EAAA,CAAAC,eAAA,EAAM;IAAND,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAM,MAAA,2BAAoB;IAAAN,EAAA,CAAAK,YAAA,EAAO;;;;;IAuC3CL,EAAA,CAAAE,cAAA,cAMC;IACCF,EAAA,CAAAM,MAAA,0BACF;IAAAN,EAAA,CAAAK,YAAA,EAAM;;;;;;;;AD9MhB,OAAM,MAAOE,mBAAmB;EAK9BC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,MAAc,EACdC,WAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAPrB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAG,KAAK;IAQlB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAElB,UAAU,CAACmB,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC,EAAE,EAAErB,UAAU,CAACmB,QAAQ,CAAC;MACrCG,QAAQ,EAAE,CAAC,IAAI,CAAC;MAChBC,MAAM,EAAE,CAAC,EAAE,EAAEvB,UAAU,CAACmB,QAAQ,CAAC,CAAE;KACpC,CAAC;EACJ;;EAEAK,YAAYA,CAACC,KAAY;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE;MACf,IAAI,CAACd,aAAa,GAAGe,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC;;EAEhD;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,UAAU,CAACgB,OAAO,EAAE;IAE7B,IAAI,CAACjB,YAAY,GAAG,IAAI;IACxBkB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACpB,KAAK,CAAC;IACrDiB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAAClB,WAAW,IAAI,EAAE,CAAC;IACvEe,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACjB,UAAU,CAAC;IAC/Dc,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACf,MAAM,CAAC;IAEvD;IACA,MAAMgB,iBAAiB,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,gBAAgB,EAAE;IAC7D;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,cAAc,EAAE;IAErD;IACA,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACzC;IACA,IAAIC,MAAM,GAAGP,iBAAiB;IAC9B,IAAI,CAACO,MAAM,IAAIL,WAAW,EAAE;MAC1BK,MAAM,GAAGL,WAAW,CAACM,GAAG,IAAIN,WAAW,CAACO,EAAE;;IAE5C,IAAI,CAACF,MAAM,IAAIH,IAAI,EAAE;MACnBG,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACP,IAAI,CAAC,CAACK,EAAE;;IAG9B,IAAIF,MAAM,EAAE;MACVX,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAES,MAAM,CAAC;KACtC,MAAM;MACLK,KAAK,CACH,8EAA8E,CAC/E;MACD;;IAGF,IAAI,CAACrC,aAAa,CAACsC,OAAO,CAAEC,IAAI,IAAI;MAClClB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEgB,IAAI,CAAC;IACnC,CAAC,CAAC;IAEFpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpChB,KAAK,EAAE,IAAI,CAACF,UAAU,CAACsB,KAAK,CAACpB,KAAK;MAClCE,WAAW,EAAE,IAAI,CAACJ,UAAU,CAACsB,KAAK,CAAClB,WAAW;MAC9CC,UAAU,EAAE,IAAI,CAACL,UAAU,CAACsB,KAAK,CAACjB,UAAU;MAC5CE,MAAM,EAAE,IAAI,CAACP,UAAU,CAACsB,KAAK,CAACf,MAAM;MACpCD,QAAQ,EAAE,IAAI,CAACR,aAAa,CAACwC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI;KAC/C,CAAC;IAEF,IAAI,CAAC7C,aAAa,CAAC8C,SAAS,CAACtB,QAAQ,CAAC,CAACuB,SAAS,CAAC;MAC/CC,IAAI,EAAEA,CAAA,KAAK;QACT1B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCiB,KAAK,CAAC,2BAA2B,CAAC;QAClC,IAAI,CAACvC,MAAM,CAACgD,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MAC3C,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb7B,OAAO,CAAC4B,KAAK,CAAC,mCAAmC,EAAEC,GAAG,CAAC;QACvDX,KAAK,CACH,oCAAoC,IACjCW,GAAG,CAACD,KAAK,EAAEE,OAAO,IAAID,GAAG,CAACC,OAAO,IAAI,iBAAiB,CAAC,CAC3D;QACD,IAAI,CAAChD,YAAY,GAAG,KAAK;MAC3B,CAAC;MACDiD,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACjD,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;;;uBA9FWP,mBAAmB,EAAAP,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAApE,EAAA,CAAAgE,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAtE,EAAA,CAAAgE,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAnBjE,mBAAmB;MAAAkE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVhC/E,EAAA,CAAAE,cAAA,aAAiK;UAM3BF,EAAA,CAAAM,MAAA,cAAO;UAAAN,EAAA,CAAAK,YAAA,EAAI;UACzIL,EAAA,CAAAG,cAAA,EAA2E;UAA3EH,EAAA,CAAAE,cAAA,aAA2E;UACzEF,EAAA,CAAAI,SAAA,cAA8F;UAChGJ,EAAA,CAAAK,YAAA,EAAM;UACNL,EAAA,CAAAC,eAAA,EAAqE;UAArED,EAAA,CAAAE,cAAA,cAAqE;UAAAF,EAAA,CAAAM,MAAA,qBAAc;UAAAN,EAAA,CAAAK,YAAA,EAAO;UAG5FL,EAAA,CAAAE,cAAA,cAA2J;UAGrJF,EAAA,CAAAG,cAAA,EAAsF;UAAtFH,EAAA,CAAAE,cAAA,eAAsF;UACpFF,EAAA,CAAAI,SAAA,gBAA4G;UAC9GJ,EAAA,CAAAK,YAAA,EAAM;UAERL,EAAA,CAAAC,eAAA,EAAK;UAALD,EAAA,CAAAE,cAAA,WAAK;UAEDF,EAAA,CAAAM,MAAA,sCACF;UAAAN,EAAA,CAAAK,YAAA,EAAK;UACLL,EAAA,CAAAE,cAAA,aAAmD;UACjDF,EAAA,CAAAM,MAAA,2EACF;UAAAN,EAAA,CAAAK,YAAA,EAAI;UAOZL,EAAA,CAAAE,cAAA,eAA+B;UAKMF,EAAA,CAAAiF,UAAA,sBAAAC,uDAAA;YAAA,OAAYF,GAAA,CAAAlD,QAAA,EAAU;UAAA,EAAC;UAGpD9B,EAAA,CAAAE,cAAA,eAAuB;UAGjBF,EAAA,CAAAG,cAAA,EAAsH;UAAtHH,EAAA,CAAAE,cAAA,eAAsH;UACpHF,EAAA,CAAAI,SAAA,gBAA2I;UAC7IJ,EAAA,CAAAK,YAAA,EAAM;UAERL,EAAA,CAAAC,eAAA,EAA6E;UAA7ED,EAAA,CAAAE,cAAA,cAA6E;UAAAF,EAAA,CAAAM,MAAA,wCAAsB;UAAAN,EAAA,CAAAK,YAAA,EAAK;UAI1GL,EAAA,CAAAE,cAAA,eAAuB;UAEnBF,EAAA,CAAAG,cAAA,EAAsH;UAAtHH,EAAA,CAAAE,cAAA,eAAsH;UACpHF,EAAA,CAAAI,SAAA,gBAAsN;UACxNJ,EAAA,CAAAK,YAAA,EAAM;UACNL,EAAA,CAAAC,eAAA,EAAM;UAAND,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAM,MAAA,uBAAe;UAAAN,EAAA,CAAAK,YAAA,EAAO;UAC5BL,EAAA,CAAAE,cAAA,gBAAgD;UAAAF,EAAA,CAAAM,MAAA,SAAC;UAAAN,EAAA,CAAAK,YAAA,EAAO;UAE1DL,EAAA,CAAAE,cAAA,eAAsB;UACpBF,EAAA,CAAAI,SAAA,iBACma;UACnaJ,EAAA,CAAAE,cAAA,eAAkF;UAChFF,EAAA,CAAAG,cAAA,EAAuH;UAAvHH,EAAA,CAAAE,cAAA,eAAuH;UACrHF,EAAA,CAAAI,SAAA,gBAAsN;UACxNJ,EAAA,CAAAK,YAAA,EAAM;UAGVL,EAAA,CAAAmF,UAAA,KAAAC,mCAAA,kBAMM;UACRpF,EAAA,CAAAK,YAAA,EAAM;UAGNL,EAAA,CAAAC,eAAA,EAAuB;UAAvBD,EAAA,CAAAE,cAAA,eAAuB;UAEnBF,EAAA,CAAAG,cAAA,EAAsH;UAAtHH,EAAA,CAAAE,cAAA,eAAsH;UACpHF,EAAA,CAAAI,SAAA,gBAAwG;UAC1GJ,EAAA,CAAAK,YAAA,EAAM;UACNL,EAAA,CAAAC,eAAA,EAAM;UAAND,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAK,YAAA,EAAO;UACxBL,EAAA,CAAAE,cAAA,gBAAgD;UAAAF,EAAA,CAAAM,MAAA,SAAC;UAAAN,EAAA,CAAAK,YAAA,EAAO;UAE1DL,EAAA,CAAAE,cAAA,eAAsB;UACpBF,EAAA,CAAAI,SAAA,oBAC6b;UAC7bJ,EAAA,CAAAE,cAAA,eAA6E;UAC3EF,EAAA,CAAAG,cAAA,EAA8H;UAA9HH,EAAA,CAAAE,cAAA,eAA8H;UAC5HF,EAAA,CAAAI,SAAA,gBAAwG;UAC1GJ,EAAA,CAAAK,YAAA,EAAM;UAGVL,EAAA,CAAAmF,UAAA,KAAAE,mCAAA,kBAMM;UACRrF,EAAA,CAAAK,YAAA,EAAM;UAGNL,EAAA,CAAAC,eAAA,EAAmD;UAAnDD,EAAA,CAAAE,cAAA,eAAmD;UAI7CF,EAAA,CAAAG,cAAA,EAAsH;UAAtHH,EAAA,CAAAE,cAAA,eAAsH;UACpHF,EAAA,CAAAI,SAAA,gBAAwK;UAC1KJ,EAAA,CAAAK,YAAA,EAAM;UACNL,EAAA,CAAAC,eAAA,EAAM;UAAND,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAK,YAAA,EAAO;UACxBL,EAAA,CAAAE,cAAA,gBAAgD;UAAAF,EAAA,CAAAM,MAAA,SAAC;UAAAN,EAAA,CAAAK,YAAA,EAAO;UAE1DL,EAAA,CAAAE,cAAA,eAAsB;UACpBF,EAAA,CAAAI,SAAA,iBACyW;UACzWJ,EAAA,CAAAE,cAAA,eAAkF;UAChFF,EAAA,CAAAG,cAAA,EAAuH;UAAvHH,EAAA,CAAAE,cAAA,eAAuH;UACrHF,EAAA,CAAAI,SAAA,gBAAwK;UAC1KJ,EAAA,CAAAK,YAAA,EAAM;UAGVL,EAAA,CAAAmF,UAAA,KAAAG,mCAAA,kBAMM;UACRtF,EAAA,CAAAK,YAAA,EAAM;UAGNL,EAAA,CAAAC,eAAA,EAAuB;UAAvBD,EAAA,CAAAE,cAAA,eAAuB;UAEnBF,EAAA,CAAAG,cAAA,EAAsH;UAAtHH,EAAA,CAAAE,cAAA,eAAsH;UACpHF,EAAA,CAAAI,SAAA,gBAAwV;UAC1VJ,EAAA,CAAAK,YAAA,EAAM;UACNL,EAAA,CAAAC,eAAA,EAAM;UAAND,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAM,MAAA,oBAAY;UAAAN,EAAA,CAAAK,YAAA,EAAO;UACzBL,EAAA,CAAAE,cAAA,gBAAgD;UAAAF,EAAA,CAAAM,MAAA,SAAC;UAAAN,EAAA,CAAAK,YAAA,EAAO;UAE1DL,EAAA,CAAAE,cAAA,eAAsB;UAGDF,EAAA,CAAAM,MAAA,mCAAsB;UAAAN,EAAA,CAAAK,YAAA,EAAS;UAChDL,EAAA,CAAAE,cAAA,kBAAuB;UAAAF,EAAA,CAAAM,MAAA,cAAM;UAAAN,EAAA,CAAAK,YAAA,EAAS;UACtCL,EAAA,CAAAE,cAAA,kBAAwB;UAAAF,EAAA,CAAAM,MAAA,eAAO;UAAAN,EAAA,CAAAK,YAAA,EAAS;UACxCL,EAAA,CAAAE,cAAA,kBAAwB;UAAAF,EAAA,CAAAM,MAAA,eAAO;UAAAN,EAAA,CAAAK,YAAA,EAAS;UACxCL,EAAA,CAAAE,cAAA,kBAAwB;UAAAF,EAAA,CAAAM,MAAA,eAAO;UAAAN,EAAA,CAAAK,YAAA,EAAS;UACxCL,EAAA,CAAAE,cAAA,kBAAqB;UAAAF,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAK,YAAA,EAAS;UAEhDL,EAAA,CAAAE,cAAA,eAAkF;UAChFF,EAAA,CAAAG,cAAA,EAAuH;UAAvHH,EAAA,CAAAE,cAAA,eAAuH;UACrHF,EAAA,CAAAI,SAAA,gBAAwV;UAC1VJ,EAAA,CAAAK,YAAA,EAAM;UAERL,EAAA,CAAAC,eAAA,EAAmF;UAAnFD,EAAA,CAAAE,cAAA,eAAmF;UACjFF,EAAA,CAAAG,cAAA,EAAuH;UAAvHH,EAAA,CAAAE,cAAA,gBAAuH;UACrHF,EAAA,CAAAI,SAAA,iBAAgG;UAClGJ,EAAA,CAAAK,YAAA,EAAM;UAGVL,EAAA,CAAAmF,UAAA,MAAAI,oCAAA,kBAMM;UACRvF,EAAA,CAAAK,YAAA,EAAM;UAKdL,EAAA,CAAAC,eAAA,EAAK;UAALD,EAAA,CAAAE,cAAA,YAAK;UAIAF,EAAA,CAAAM,MAAA,iBAAQ;UAAAN,EAAA,CAAAK,YAAA,EACV;UACDL,EAAA,CAAAE,cAAA,kBAME;UAHAF,EAAA,CAAAiF,UAAA,oBAAAO,uDAAAC,MAAA;YAAA,OAAUT,GAAA,CAAAzD,YAAA,CAAAkE,MAAA,CAAoB;UAAA,EAAC;UAHjCzF,EAAA,CAAAK,YAAA,EAME;UAIJL,EAAA,CAAAE,cAAA,YAAK;UAIAF,EAAA,CAAAM,MAAA,eAAM;UAAAN,EAAA,CAAAK,YAAA,EACR;UACDL,EAAA,CAAAE,cAAA,mBAIC;UACkBF,EAAA,CAAAM,MAAA,gCAAuB;UAAAN,EAAA,CAAAK,YAAA,EAAS;UACjDL,EAAA,CAAAE,cAAA,mBAAwB;UAAAF,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAK,YAAA,EAAS;UACxCL,EAAA,CAAAE,cAAA,mBAAwB;UAAAF,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAK,YAAA,EAAS;UACxCL,EAAA,CAAAE,cAAA,mBAAwB;UAAAF,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAK,YAAA,EAAS;UAE1CL,EAAA,CAAAmF,UAAA,MAAAO,oCAAA,kBAQM;UACR1F,EAAA,CAAAK,YAAA,EAAM;UAGNL,EAAA,CAAAE,cAAA,YAAK;UAODF,EAAA,CAAAM,MAAA,kBACF;UAAAN,EAAA,CAAAK,YAAA,EAAS;;;;;;;;UA9LHL,EAAA,CAAA2F,SAAA,IAAwB;UAAxB3F,EAAA,CAAA4F,UAAA,cAAAZ,GAAA,CAAAjE,UAAA,CAAwB;UA+BlBf,EAAA,CAAA2F,SAAA,IAA0E;UAA1E3F,EAAA,CAAA4F,UAAA,WAAAC,OAAA,GAAAb,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,4BAAAD,OAAA,CAAA9D,OAAA,OAAA8D,OAAA,GAAAb,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,4BAAAD,OAAA,CAAAE,OAAA,EAA0E;UA2B1E/F,EAAA,CAAA2F,SAAA,IAAsF;UAAtF3F,EAAA,CAAA4F,UAAA,WAAAI,OAAA,GAAAhB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,kCAAAE,OAAA,CAAAjE,OAAA,OAAAiE,OAAA,GAAAhB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,kCAAAE,OAAA,CAAAD,OAAA,EAAsF;UA6BpF/F,EAAA,CAAA2F,SAAA,IAAoF;UAApF3F,EAAA,CAAA4F,UAAA,WAAAK,OAAA,GAAAjB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,iCAAAG,OAAA,CAAAlE,OAAA,OAAAkE,OAAA,GAAAjB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,iCAAAG,OAAA,CAAAF,OAAA,EAAoF;UAuCpF/F,EAAA,CAAA2F,SAAA,IAA4E;UAA5E3F,EAAA,CAAA4F,UAAA,WAAAM,OAAA,GAAAlB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,6BAAAI,OAAA,CAAAnE,OAAA,OAAAmE,OAAA,GAAAlB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,6BAAAI,OAAA,CAAAH,OAAA,EAA4E;UA6CvF/F,EAAA,CAAA2F,SAAA,IAGD;UAHC3F,EAAA,CAAA4F,UAAA,WAAAO,OAAA,GAAAnB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,6BAAAK,OAAA,CAAApE,OAAA,OAAAoE,OAAA,GAAAnB,GAAA,CAAAjE,UAAA,CAAA+E,GAAA,6BAAAK,OAAA,CAAAJ,OAAA,EAGD;UAYA/F,EAAA,CAAA2F,SAAA,GAA+B;UAA/B3F,EAAA,CAAA4F,UAAA,aAAAZ,GAAA,CAAAjE,UAAA,CAAAgB,OAAA,CAA+B,YAAA/B,EAAA,CAAAoG,eAAA,IAAAC,GAAA,EAAArB,GAAA,CAAAjE,UAAA,CAAAgB,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}