{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"div\", 13);\n    i0.ɵɵelementStart(3, \"p\", 14);\n    i0.ɵɵtext(4, \"Chargement en cours...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectEvaluationComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 18);\n    i0.ɵɵelement(4, \"path\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"p\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProjectEvaluationComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 23);\n    i0.ɵɵelement(4, \"path\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"p\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.successMessage);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_21_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r8 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r8, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fichier_r8.split(\"/\").pop(), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"h3\", 39);\n    i0.ɵɵtext(2, \"Fichiers joints:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 40);\n    i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_11_div_21_li_4_Template, 3, 2, \"li\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 43);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_11_form_31_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 44)(2, \"div\", 45)(3, \"label\", 46);\n    i0.ɵɵtext(4, \"Structure du code (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"label\", 46);\n    i0.ɵɵtext(8, \"Bonnes pratiques (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 45)(11, \"label\", 46);\n    i0.ɵɵtext(12, \"Fonctionnalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 45)(15, \"label\", 46);\n    i0.ɵɵtext(16, \"Originalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 51)(19, \"label\", 46);\n    i0.ɵɵtext(20, \"Commentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 53)(23, \"button\", 54);\n    i0.ɵɵtext(24, \" Soumettre l'\\u00E9valuation \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.evaluationForm);\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.evaluationForm.invalid || ctx_r5.isLoading);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 57);\n    i0.ɵɵtext(2, \"L'\\u00E9valuation sera r\\u00E9alis\\u00E9e automatiquement par notre syst\\u00E8me d'IA (Mistral 7B).\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 30);\n    i0.ɵɵtext(4, \"L'IA analysera le code soumis et fournira une \\u00E9valuation bas\\u00E9e sur les crit\\u00E8res standards.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 53)(6, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_11_div_32_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r13.onSubmit());\n    });\n    i0.ɵɵtext(7, \" Lancer l'\\u00E9valuation IA \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_32_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"div\", 60);\n    i0.ɵɵelementStart(2, \"p\", 61);\n    i0.ɵɵtext(3, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 62);\n    i0.ɵɵtext(5, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_11_div_32_div_1_Template, 8, 1, \"div\", 10);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_11_div_32_div_2_Template, 6, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 26)(2, \"h2\", 27);\n    i0.ɵɵtext(3, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"span\", 28);\n    i0.ɵɵtext(6, \"Projet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"span\", 28);\n    i0.ɵɵtext(10, \"\\u00C9tudiant:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\")(13, \"span\", 28);\n    i0.ɵɵtext(14, \"Date de soumission:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\")(18, \"span\", 28);\n    i0.ɵɵtext(19, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, ProjectEvaluationComponent_div_11_div_21_Template, 5, 1, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 30)(23, \"div\", 31)(24, \"h2\", 32);\n    i0.ɵɵtext(25, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 33)(27, \"span\", 34);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_11_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.toggleEvaluationMode());\n    });\n    i0.ɵɵtext(30, \" Changer de mode \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(31, ProjectEvaluationComponent_div_11_form_31_Template, 25, 2, \"form\", 36);\n    i0.ɵɵtemplate(32, ProjectEvaluationComponent_div_11_div_32_Template, 3, 2, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.rendu.projet.titre, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.rendu.etudiant.nom, \" \", ctx_r3.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 9, ctx_r3.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.rendu.description, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.rendu.fichiers && ctx_r3.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.evaluationMode === \"manual\" ? \"Manuel\" : \"IA\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.successMessage = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\n      this.markFormGroupTouched(this.evaluationForm);\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\n      return;\n    }\n    this.isSubmitting = true;\n    this.error = '';\n    this.successMessage = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          this.successMessage = 'Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.';\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          this.successMessage = 'Évaluation soumise avec succès!';\n          setTimeout(() => {\n            this.router.navigate(['/admin/projects/list-rendus']);\n          }, 1500);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/rendus']);\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldError(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return 'Ce champ est obligatoire';\n      }\n      if (field.errors['min']) {\n        return `La valeur minimum est ${field.errors['min'].min}`;\n      }\n      if (field.errors['max']) {\n        return `La valeur maximum est ${field.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"p-8\"], [1, \"flex\", \"items-center\", \"mb-8\"], [1, \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"p-3\", \"rounded-lg\", \"mr-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-3xl\", \"font-bold\", \"text-gray-800\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded-r-lg\", 4, \"ngIf\"], [\"class\", \"bg-green-50 border-l-4 border-green-400 p-4 mb-6 rounded-r-lg\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"flex\", \"flex-col\", \"items-center\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-t-4\", \"border-b-4\", \"border-purple-500\"], [1, \"mt-4\", \"text-gray-600\"], [1, \"bg-red-50\", \"border-l-4\", \"border-red-400\", \"p-4\", \"mb-6\", \"rounded-r-lg\"], [1, \"flex\"], [1, \"flex-shrink-0\"], [\"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-red-400\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\", \"clip-rule\", \"evenodd\"], [1, \"ml-3\"], [1, \"text-sm\", \"text-red-700\"], [1, \"bg-green-50\", \"border-l-4\", \"border-green-400\", \"p-4\", \"mb-6\", \"rounded-r-lg\"], [\"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-green-400\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\", \"clip-rule\", \"evenodd\"], [1, \"text-sm\", \"text-green-700\"], [1, \"mb-6\", \"p-4\", \"bg-gray-50\", \"rounded-lg\"], [1, \"text-xl\", \"font-semibold\", \"mb-2\"], [1, \"font-medium\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-xl\", \"font-semibold\"], [1, \"flex\", \"items-center\"], [1, \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded\", \"hover:bg-purple-700\", \"transition-colors\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gray-50 p-4 rounded-lg\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"font-medium\", \"mb-2\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", 3, \"href\"], [3, \"formGroup\", \"ngSubmit\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\", \"mb-6\"], [1, \"form-group\"], [1, \"block\", \"text-gray-700\", \"mb-2\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [1, \"form-group\", \"mb-6\"], [\"formControlName\", \"commentaires\", \"rows\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [1, \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"px-6\", \"py-2\", \"bg-green-600\", \"text-white\", \"rounded\", \"hover:bg-green-700\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-green-600\", \"text-white\", \"rounded\", \"hover:bg-green-700\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-purple-500\", \"mx-auto\", \"mb-4\"], [1, \"text-gray-700\"], [1, \"text-sm\", \"text-gray-500\", \"mt-2\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(4, \"svg\", 4);\n          i0.ɵɵelement(5, \"path\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"h1\", 6);\n          i0.ɵɵtext(7, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, ProjectEvaluationComponent_div_8_Template, 5, 0, \"div\", 7);\n          i0.ɵɵtemplate(9, ProjectEvaluationComponent_div_9_Template, 8, 1, \"div\", 8);\n          i0.ɵɵtemplate(10, ProjectEvaluationComponent_div_10_Template, 8, 1, \"div\", 9);\n          i0.ɵɵtemplate(11, ProjectEvaluationComponent_div_11_Template, 33, 12, \"div\", 10);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtZXZhbHVhdGlvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG9EQUFvRDtBQUNwRDtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQiIsImZpbGUiOiJwcm9qZWN0LWV2YWx1YXRpb24uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBkJ8OpdmFsdWF0aW9uIGRlIHByb2pldCAqL1xyXG4uY29udGFpbmVyIHtcclxuICBtYXgtd2lkdGg6IDEyMDBweDtcclxuICBtYXJnaW46IDAgYXV0bztcclxufVxyXG5cclxuLmZvcm0tZ3JvdXAge1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5lcnJvci1tZXNzYWdlIHtcclxuICBjb2xvcjogI2RjMzU0NTtcclxuICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xyXG59XHJcblxyXG4ubG9hZGluZy1zcGlubmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIG1hcmdpbjogMnJlbSAwO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvcHJvamVjdC1ldmFsdWF0aW9uL3Byb2plY3QtZXZhbHVhdGlvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG9EQUFvRDtBQUNwRDtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQjtBQUNBLG81QkFBbzVCIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGQnw4PCqXZhbHVhdGlvbiBkZSBwcm9qZXQgKi9cclxuLmNvbnRhaW5lciB7XHJcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbn1cclxuXHJcbi5mb3JtLWdyb3VwIHtcclxuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4uZXJyb3ItbWVzc2FnZSB7XHJcbiAgY29sb3I6ICNkYzM1NDU7XHJcbiAgbWFyZ2luLXRvcDogMC4yNXJlbTtcclxufVxyXG5cclxuLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBtYXJnaW46IDJyZW0gMDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ctx_r2", "successMessage", "ɵɵproperty", "fichier_r8", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "split", "pop", "ɵɵtemplate", "ProjectEvaluationComponent_div_11_div_21_li_4_Template", "ctx_r4", "rendu", "fichiers", "ɵɵlistener", "ProjectEvaluationComponent_div_11_form_31_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ctx_r5", "evaluationForm", "invalid", "isLoading", "ProjectEvaluationComponent_div_11_div_32_div_1_Template_button_click_6_listener", "_r14", "ctx_r13", "ctx_r11", "isSubmitting", "ProjectEvaluationComponent_div_11_div_32_div_1_Template", "ProjectEvaluationComponent_div_11_div_32_div_2_Template", "ctx_r6", "aiProcessing", "ProjectEvaluationComponent_div_11_div_21_Template", "ProjectEvaluationComponent_div_11_Template_button_click_29_listener", "_r16", "ctx_r15", "toggleEvaluationMode", "ProjectEvaluationComponent_div_11_form_31_Template", "ProjectEvaluationComponent_div_11_div_32_Template", "ctx_r3", "projet", "titre", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "length", "evaluationMode", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "err", "console", "markFormGroupTouched", "evaluationData", "value", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "setTimeout", "navigate", "message", "getScoreTotal", "getScoreMaximum", "annuler", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "isFieldInvalid", "fieldName", "field", "dirty", "touched", "getFieldError", "errors", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ProjectEvaluationComponent_div_8_Template", "ProjectEvaluationComponent_div_9_Template", "ProjectEvaluationComponent_div_10_Template", "ProjectEvaluationComponent_div_11_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  successMessage: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\r\n      this.markFormGroupTouched(this.evaluationForm);\r\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n    this.successMessage = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          this.successMessage = 'Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.';\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          this.successMessage = 'Évaluation soumise avec succès!';\r\n          setTimeout(() => {\r\n            this.router.navigate(['/admin/projects/list-rendus']);\r\n          }, 1500);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    this.router.navigate(['/admin/projects/rendus']);\r\n  }\r\n\r\n  markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    if (field && field.errors && (field.dirty || field.touched)) {\r\n      if (field.errors['required']) {\r\n        return 'Ce champ est obligatoire';\r\n      }\r\n      if (field.errors['min']) {\r\n        return `La valeur minimum est ${field.errors['min'].min}`;\r\n      }\r\n      if (field.errors['max']) {\r\n        return `La valeur maximum est ${field.errors['max'].max}`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"container mx-auto px-4 py-8\">\r\n  <div class=\"max-w-4xl mx-auto bg-white rounded-xl shadow-lg p-8\">\r\n    <div class=\"flex items-center mb-8\">\r\n      <div class=\"bg-gradient-to-r from-purple-500 to-blue-500 p-3 rounded-lg mr-4\">\r\n        <svg class=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n        </svg>\r\n      </div>\r\n      <h1 class=\"text-3xl font-bold text-gray-800\">Évaluation du projet</h1>\r\n    </div>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\r\n      <div class=\"flex flex-col items-center\">\r\n        <div class=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-purple-500\"></div>\r\n        <p class=\"mt-4 text-gray-600\">Chargement en cours...</p>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"error\" class=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded-r-lg\">\r\n      <div class=\"flex\">\r\n        <div class=\"flex-shrink-0\">\r\n          <svg class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\" />\r\n          </svg>\r\n        </div>\r\n        <div class=\"ml-3\">\r\n          <p class=\"text-sm text-red-700\">{{ error }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"successMessage\" class=\"bg-green-50 border-l-4 border-green-400 p-4 mb-6 rounded-r-lg\">\r\n      <div class=\"flex\">\r\n        <div class=\"flex-shrink-0\">\r\n          <svg class=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\r\n          </svg>\r\n        </div>\r\n        <div class=\"ml-3\">\r\n          <p class=\"text-sm text-green-700\">{{ successMessage }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"rendu && !isLoading\">\r\n      <div class=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n        <h2 class=\"text-xl font-semibold mb-2\">Informations sur le rendu</h2>\r\n        <p><span class=\"font-medium\">Projet:</span> {{ rendu.projet.titre }}</p>\r\n        <p><span class=\"font-medium\">Étudiant:</span> {{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n        <p><span class=\"font-medium\">Date de soumission:</span> {{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n        <p><span class=\"font-medium\">Description:</span> {{ rendu.description }}</p>\r\n\r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"mt-4\">\r\n          <h3 class=\"font-medium mb-2\">Fichiers joints:</h3>\r\n          <ul class=\"list-disc pl-5\">\r\n            <li *ngFor=\"let fichier of rendu.fichiers\">\r\n              <a [href]=\"'http://localhost:3000/' + fichier\" target=\"_blank\" class=\"text-blue-600 hover:underline\">\r\n                {{ fichier.split('/').pop() }}\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-6\">\r\n        <div class=\"flex items-center justify-between mb-4\">\r\n          <h2 class=\"text-xl font-semibold\">Mode d'évaluation</h2>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"mr-2\">{{ evaluationMode === 'manual' ? 'Manuel' : 'IA' }}</span>\r\n            <button\r\n              (click)=\"toggleEvaluationMode()\"\r\n              class=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors\"\r\n            >\r\n              Changer de mode\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\">\r\n          <div formGroupName=\"scores\" class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Structure du code (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"structure\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Bonnes pratiques (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"pratiques\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Fonctionnalité (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"fonctionnalite\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Originalité (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"originalite\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-group mb-6\">\r\n            <label class=\"block text-gray-700 mb-2\">Commentaires</label>\r\n            <textarea\r\n              formControlName=\"commentaires\"\r\n              rows=\"5\"\r\n              class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n            ></textarea>\r\n          </div>\r\n\r\n          <div class=\"flex justify-end\">\r\n            <button\r\n              type=\"submit\"\r\n              [disabled]=\"evaluationForm.invalid || isLoading\"\r\n              class=\"px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50\"\r\n            >\r\n              Soumettre l'évaluation\r\n            </button>\r\n          </div>\r\n        </form>\r\n\r\n        <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n          <div *ngIf=\"!aiProcessing\">\r\n            <p class=\"mb-4\">L'évaluation sera réalisée automatiquement par notre système d'IA (Mistral 7B).</p>\r\n            <p class=\"mb-6\">L'IA analysera le code soumis et fournira une évaluation basée sur les critères standards.</p>\r\n\r\n            <div class=\"flex justify-end\">\r\n              <button\r\n                (click)=\"onSubmit()\"\r\n                [disabled]=\"isSubmitting\"\r\n                class=\"px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50\"\r\n              >\r\n                Lancer l'évaluation IA\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"aiProcessing\" class=\"text-center py-8\">\r\n            <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4\"></div>\r\n            <p class=\"text-gray-700\">L'IA analyse le projet...</p>\r\n            <p class=\"text-sm text-gray-500 mt-2\">Cela peut prendre quelques instants</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;;;ICU/DC,EAAA,CAAAC,cAAA,cAAyD;IAErDD,EAAA,CAAAE,SAAA,cAA+F;IAC/FF,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAI5DJ,EAAA,CAAAC,cAAA,cAAqF;IAG/ED,EAAA,CAAAK,cAAA,EAA0E;IAA1EL,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAE,SAAA,eAA4Q;IAC9QF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAkB;IAAlBN,EAAA,CAAAC,cAAA,cAAkB;IACgBD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAfJ,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAKjDV,EAAA,CAAAC,cAAA,cAAkG;IAG5FD,EAAA,CAAAK,cAAA,EAA4E;IAA5EL,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,SAAA,eAA0L;IAC5LF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAkB;IAAlBN,EAAA,CAAAC,cAAA,cAAkB;IACkBD,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAxBJ,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,iBAAA,CAAAG,MAAA,CAAAC,cAAA,CAAoB;;;;;IAgBpDZ,EAAA,CAAAC,cAAA,SAA2C;IAEvCD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAFDJ,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAa,UAAA,oCAAAC,UAAA,EAAAd,EAAA,CAAAe,aAAA,CAA2C;IAC5Cf,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAgB,kBAAA,MAAAF,UAAA,CAAAG,KAAA,MAAAC,GAAA,QACF;;;;;IANNlB,EAAA,CAAAC,cAAA,cAAsE;IACvCD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAmB,UAAA,IAAAC,sDAAA,iBAIK;IACPpB,EAAA,CAAAI,YAAA,EAAK;;;;IALqBJ,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAa,UAAA,YAAAQ,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;;IAuB7CvB,EAAA,CAAAC,cAAA,eAA+F;IAA5DD,EAAA,CAAAwB,UAAA,sBAAAC,4EAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAA5B,EAAA,CAAA6B,aAAA;MAAA,OAAY7B,EAAA,CAAA8B,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxD/B,EAAA,CAAAC,cAAA,cAA+E;IAEnCD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvEJ,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IACkBD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtEJ,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAG,MAAA,iCAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpEJ,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAG,MAAA,8BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACjEJ,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAI,YAAA,EAAM;IAGRJ,EAAA,CAAAC,cAAA,eAA6B;IACaD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5DJ,EAAA,CAAAE,SAAA,oBAIY;IACdF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAA8B;IAM1BD,EAAA,CAAAG,MAAA,qCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IA5DPJ,EAAA,CAAAa,UAAA,cAAAmB,MAAA,CAAAC,cAAA,CAA4B;IAwD5BjC,EAAA,CAAAO,SAAA,IAAgD;IAAhDP,EAAA,CAAAa,UAAA,aAAAmB,MAAA,CAAAC,cAAA,CAAAC,OAAA,IAAAF,MAAA,CAAAG,SAAA,CAAgD;;;;;;IASpDnC,EAAA,CAAAC,cAAA,UAA2B;IACTD,EAAA,CAAAG,MAAA,0GAA+E;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnGJ,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAG,MAAA,gHAA0F;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE9GJ,EAAA,CAAAC,cAAA,cAA8B;IAE1BD,EAAA,CAAAwB,UAAA,mBAAAY,gFAAA;MAAApC,EAAA,CAAA0B,aAAA,CAAAW,IAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAQ,OAAA,CAAAP,QAAA,EAAU;IAAA,EAAC;IAIpB/B,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAJPJ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAa,UAAA,aAAA0B,OAAA,CAAAC,YAAA,CAAyB;;;;;IAQ/BxC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAA4G;IAC5GF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtDJ,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAG,MAAA,0CAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAnBjFJ,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAmB,UAAA,IAAAsB,uDAAA,kBAaM;IAENzC,EAAA,CAAAmB,UAAA,IAAAuB,uDAAA,kBAIM;IACR1C,EAAA,CAAAI,YAAA,EAAM;;;;IApBEJ,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAa,UAAA,UAAA8B,MAAA,CAAAC,YAAA,CAAmB;IAenB5C,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAa,UAAA,SAAA8B,MAAA,CAAAC,YAAA,CAAkB;;;;;;IAlH9B5C,EAAA,CAAAC,cAAA,UAAiC;IAEUD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrEJ,EAAA,CAAAC,cAAA,QAAG;IAA0BD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACxEJ,EAAA,CAAAC,cAAA,QAAG;IAA0BD,EAAA,CAAAG,MAAA,sBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAAG,MAAA,IAAoD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtGJ,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAAG,MAAA,IAAoD;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChHJ,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAAG,MAAA,IAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE5EJ,EAAA,CAAAmB,UAAA,KAAA0B,iDAAA,kBASM;IACR7C,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAkB;IAEoBD,EAAA,CAAAG,MAAA,8BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxDJ,EAAA,CAAAC,cAAA,eAA+B;IACVD,EAAA,CAAAG,MAAA,IAAmD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7EJ,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAwB,UAAA,mBAAAsB,oEAAA;MAAA9C,EAAA,CAAA0B,aAAA,CAAAqB,IAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAkB,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCjD,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAIbJ,EAAA,CAAAmB,UAAA,KAAA+B,kDAAA,oBA8DO;IAEPlD,EAAA,CAAAmB,UAAA,KAAAgC,iDAAA,kBAqBM;IACRnD,EAAA,CAAAI,YAAA,EAAM;;;;IArHwCJ,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAgB,kBAAA,MAAAoC,MAAA,CAAA9B,KAAA,CAAA+B,MAAA,CAAAC,KAAA,KAAwB;IACtBtD,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAuD,kBAAA,MAAAH,MAAA,CAAA9B,KAAA,CAAAkC,QAAA,CAAAC,GAAA,OAAAL,MAAA,CAAA9B,KAAA,CAAAkC,QAAA,CAAAE,MAAA,KAAoD;IAC1C1D,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAgB,kBAAA,MAAAhB,EAAA,CAAA2D,WAAA,QAAAP,MAAA,CAAA9B,KAAA,CAAAsC,cAAA,0BAAoD;IAC3D5D,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAgB,kBAAA,MAAAoC,MAAA,CAAA9B,KAAA,CAAAuC,WAAA,KAAuB;IAElE7D,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAa,UAAA,SAAAuC,MAAA,CAAA9B,KAAA,CAAAC,QAAA,IAAA6B,MAAA,CAAA9B,KAAA,CAAAC,QAAA,CAAAuC,MAAA,KAAiD;IAgBhC9D,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAQ,iBAAA,CAAA4C,MAAA,CAAAW,cAAA,gCAAmD;IAUd/D,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAa,UAAA,SAAAuC,MAAA,CAAAW,cAAA,cAAiC;IAgEvF/D,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAa,UAAA,SAAAuC,MAAA,CAAAW,cAAA,UAA6B;;;ADpI3C,OAAM,MAAOC,0BAA0B;EAWrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAdvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAhD,KAAK,GAAQ,IAAI;IAEjB,KAAAa,SAAS,GAAY,IAAI;IACzB,KAAAK,YAAY,GAAY,KAAK;IAC7B,KAAA9B,KAAK,GAAW,EAAE;IAClB,KAAAE,cAAc,GAAW,EAAE;IAC3B,KAAAmD,cAAc,GAAoB,QAAQ;IAC1C,KAAAnB,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAACX,cAAc,GAAG,IAAI,CAACiC,EAAE,CAACK,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACN,EAAE,CAACK,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC1E,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAAC4E,GAAG,CAAC,CAAC,CAAC,EAAE5E,UAAU,CAAC6E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC9E,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAAC4E,GAAG,CAAC,CAAC,CAAC,EAAE5E,UAAU,CAAC6E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC/E,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAAC4E,GAAG,CAAC,CAAC,CAAC,EAAE5E,UAAU,CAAC6E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAAChF,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAAC4E,GAAG,CAAC,CAAC,CAAC,EAAE5E,UAAU,CAAC6E,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAEjF,UAAU,CAAC2E,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACZ,OAAO,GAAG,IAAI,CAACH,KAAK,CAACgB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACnB,KAAK,CAACgB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACvB,cAAc,GAAGuB,IAAI;MAC1B,IAAI,CAACrD,cAAc,CAACuD,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC5B,cAAc,GAAG4B,UAAU;QAChC,IAAI,CAAC1D,cAAc,CAACuD,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACrB,OAAO,EAAE;MAChB,IAAI,CAACuB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAACnF,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACyB,SAAS,GAAG,KAAK;;EAE1B;EAEA0D,SAASA,CAAA;IACP,IAAI,CAAC1D,SAAS,GAAG,IAAI;IACrB,IAAI,CAACkC,aAAa,CAACyB,YAAY,CAAC,IAAI,CAACxB,OAAO,CAAC,CAACyB,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAAC3E,KAAK,GAAG2E,IAAI;QACjB,IAAI,CAAC9D,SAAS,GAAG,KAAK;MACxB,CAAC;MACDzB,KAAK,EAAGwF,GAAQ,IAAI;QAClB,IAAI,CAACxF,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACyB,SAAS,GAAG,KAAK;QACtBgE,OAAO,CAACzF,KAAK,CAACwF,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAjD,oBAAoBA,CAAA;IAClB,IAAI,CAACc,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAAC9B,cAAc,CAACuD,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAAClB,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E0B,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC3B,cAAc,CAAC;EAC7D;EAEAhC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACgC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAAC9B,cAAc,CAACC,OAAO,EAAE;MACnE,IAAI,CAACkE,oBAAoB,CAAC,IAAI,CAACnE,cAAc,CAAC;MAC9C,IAAI,CAACvB,KAAK,GAAG,gDAAgD;MAC7D;;IAGF,IAAI,CAAC8B,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC9B,KAAK,GAAG,EAAE;IACf,IAAI,CAACE,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,IAAI,CAACmD,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAAC9B,cAAc,CAACuD,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACrC,YAAY,GAAG,IAAI;;IAG1B,MAAMyD,cAAc,GAAG,IAAI,CAACpE,cAAc,CAACqE,KAAK;IAEhD,IAAI,CAACjC,aAAa,CAACkC,aAAa,CAAC,IAAI,CAACjC,OAAO,EAAE+B,cAAc,CAAC,CAACN,SAAS,CAAC;MACvEC,IAAI,EAAGQ,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAACzC,cAAc,KAAK,IAAI,IAAIyC,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACjC,MAAM;UAC3C,MAAMmC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAACzB,YAAY;UAEvD,IAAI,CAAC/C,cAAc,CAACuD,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEiC,QAAQ,CAACjC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAE6B,QAAQ,CAAC7B,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE4B,QAAQ,CAAC5B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE2B,QAAQ,CAAC3B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE2B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAAC/D,YAAY,GAAG,KAAK;UACzB,IAAI,CAACJ,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAAC9B,KAAK,GAAG,EAAE;UACf,IAAI,CAACE,cAAc,GAAG,mFAAmF;SAC1G,MAAM;UACL;UACA,IAAI,CAAC4B,YAAY,GAAG,KAAK;UACzB,IAAI,CAAC5B,cAAc,GAAG,iCAAiC;UACvDgG,UAAU,CAAC,MAAK;YACd,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;UACvD,CAAC,EAAE,IAAI,CAAC;;MAEZ,CAAC;MACDnG,KAAK,EAAGwF,GAAQ,IAAI;QAClB,IAAI,CAACxF,KAAK,GAAG,yCAAyC,IAAIwF,GAAG,CAACxF,KAAK,EAAEoG,OAAO,IAAIZ,GAAG,CAACY,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAACtE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACI,YAAY,GAAG,KAAK;QACzBuD,OAAO,CAACzF,KAAK,CAACwF,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAa,aAAaA,CAAA;IACX,MAAMvC,MAAM,GAAG,IAAI,CAACvC,cAAc,CAACoD,GAAG,CAAC,QAAQ,CAAC,EAAEiB,KAAK;IACvD,IAAI,CAAC9B,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAiC,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC7C,MAAM,CAACyC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAT,oBAAoBA,CAACc,SAAoB;IACvCC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAC7B,GAAG,CAACkC,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAY1H,SAAS,EAAE;QAChC,IAAI,CAACsG,oBAAoB,CAACoB,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEAE,cAAcA,CAACC,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAAC3F,cAAc,CAACoD,GAAG,CAACsC,SAAS,CAAC;IAChD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAAC1F,OAAO,KAAK0F,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACrE;EAEAC,aAAaA,CAACJ,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC3F,cAAc,CAACoD,GAAG,CAACsC,SAAS,CAAC;IAChD,IAAIC,KAAK,IAAIA,KAAK,CAACI,MAAM,KAAKJ,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,EAAE;MAC3D,IAAIF,KAAK,CAACI,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,0BAA0B;;MAEnC,IAAIJ,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBJ,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,CAACrD,GAAG,EAAE;;MAE3D,IAAIiD,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBJ,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,CAACpD,GAAG,EAAE;;;IAG7D,OAAO,EAAE;EACX;;;uBAtLWZ,0BAA0B,EAAAhE,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnI,EAAA,CAAAiI,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArI,EAAA,CAAAiI,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAtI,EAAA,CAAAiI,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1BxE,0BAA0B;MAAAyE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvC/I,EAAA,CAAAC,cAAA,aAAyC;UAIjCD,EAAA,CAAAK,cAAA,EAAsF;UAAtFL,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAE,SAAA,cAA+H;UACjIF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAM,eAAA,EAA6C;UAA7CN,EAAA,CAAAC,cAAA,YAA6C;UAAAD,EAAA,CAAAG,MAAA,gCAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGxEJ,EAAA,CAAAmB,UAAA,IAAA8H,yCAAA,iBAKM;UAENjJ,EAAA,CAAAmB,UAAA,IAAA+H,yCAAA,iBAWM;UAENlJ,EAAA,CAAAmB,UAAA,KAAAgI,0CAAA,iBAWM;UAENnJ,EAAA,CAAAmB,UAAA,KAAAiI,0CAAA,oBAyHM;UACRpJ,EAAA,CAAAI,YAAA,EAAM;;;UA3JEJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAa,UAAA,SAAAmI,GAAA,CAAA7G,SAAA,CAAe;UAOfnC,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAa,UAAA,SAAAmI,GAAA,CAAAtI,KAAA,CAAW;UAaXV,EAAA,CAAAO,SAAA,GAAoB;UAApBP,EAAA,CAAAa,UAAA,SAAAmI,GAAA,CAAApI,cAAA,CAAoB;UAapBZ,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAa,UAAA,SAAAmI,GAAA,CAAA1H,KAAA,KAAA0H,GAAA,CAAA7G,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}