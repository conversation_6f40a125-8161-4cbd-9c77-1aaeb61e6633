{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let DetailProjectComponent = class DetailProjectComponent {\n  constructor(route, router, projectService, fileService, renduService) {\n    this.route = route;\n    this.router = router;\n    this.projectService = projectService;\n    this.fileService = fileService;\n    this.renduService = renduService;\n    this.projet = null;\n    this.rendus = [];\n    this.totalEtudiants = 0;\n    this.etudiantsRendus = [];\n    this.derniersRendus = [];\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.loadProjectData(id);\n    }\n  }\n  loadProjectData(id) {\n    this.isLoading = true;\n    // Charger les données du projet\n    this.projectService.getProjetById(id).subscribe({\n      next: data => {\n        this.projet = data;\n        this.loadProjectStatistics(id);\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du projet:', err);\n        this.isLoading = false;\n      }\n    });\n  }\n  loadProjectStatistics(projetId) {\n    // Charger les rendus pour ce projet\n    this.renduService.getRendusByProjet(projetId).subscribe({\n      next: rendus => {\n        this.rendus = rendus;\n        this.etudiantsRendus = rendus.filter(rendu => rendu.etudiant);\n        // Trier par date pour avoir les derniers rendus\n        this.derniersRendus = [...this.etudiantsRendus].sort((a, b) => new Date(b.dateRendu).getTime() - new Date(a.dateRendu).getTime()).slice(0, 5); // Prendre les 5 derniers\n        // Pour le total d'étudiants, on peut estimer ou récupérer depuis le backend\n        // Pour l'instant, on utilise le nombre d'étudiants uniques qui ont rendu + estimation\n        const etudiantsUniques = new Set(this.etudiantsRendus.map(r => r.etudiant._id || r.etudiant));\n        this.totalEtudiants = Math.max(etudiantsUniques.size, this.estimateStudentCount());\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des statistiques:', err);\n        this.isLoading = false;\n      }\n    });\n  }\n  estimateStudentCount() {\n    // Estimation basée sur le groupe du projet\n    const groupe = this.projet?.groupe?.toLowerCase();\n    if (groupe?.includes('1c')) return 25; // Première année\n    if (groupe?.includes('2c')) return 20; // Deuxième année\n    if (groupe?.includes('3c')) return 15; // Troisième année\n    return 20; // Valeur par défaut\n  }\n\n  getFileUrl(filePath) {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n  deleteProjet(id) {\n    if (!id) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        }\n      });\n    }\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  getProjectStatus() {\n    if (!this.projet?.dateLimite) return 'En cours';\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    if (deadline < now) {\n      return 'Expiré';\n    } else if (deadline.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000) {\n      return 'Urgent';\n    } else {\n      return 'Actif';\n    }\n  }\n  getStatusClass() {\n    const status = this.getProjectStatus();\n    switch (status) {\n      case 'Actif':\n        return 'bg-success/10 dark:bg-dark-accent-secondary/20 text-success dark:text-dark-accent-secondary border border-success/20 dark:border-dark-accent-secondary/30';\n      case 'Urgent':\n        return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning border border-warning/20 dark:border-warning/30';\n      case 'Expiré':\n        return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark border border-danger/20 dark:border-danger-dark/30';\n      default:\n        return 'bg-info/10 dark:bg-dark-accent-primary/20 text-info dark:text-dark-accent-primary border border-info/20 dark:border-dark-accent-primary/30';\n    }\n  }\n  getProgressPercentage() {\n    if (this.totalEtudiants === 0) return 0;\n    return Math.round(this.etudiantsRendus.length / this.totalEtudiants * 100);\n  }\n  getRemainingDays() {\n    if (!this.projet?.dateLimite) return 0;\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  }\n  getStudentInitials(etudiant) {\n    if (!etudiant) return '??';\n    // Priorité 1: firstName + lastName\n    const firstName = etudiant.firstName || etudiant.prenom || '';\n    const lastName = etudiant.lastName || etudiant.nom || '';\n    if (firstName && lastName) {\n      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n    }\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || '';\n    if (fullName) {\n      const parts = fullName.trim().split(' ');\n      if (parts.length >= 2) {\n        return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n      } else {\n        return fullName.substring(0, 2).toUpperCase();\n      }\n    }\n    // Priorité 3: firstName seul\n    if (firstName) {\n      return firstName.substring(0, 2).toUpperCase();\n    }\n    return '??';\n  }\n  getStudentName(etudiant) {\n    if (!etudiant) return 'Utilisateur inconnu';\n    // Priorité 1: firstName + lastName\n    const firstName = etudiant.firstName || etudiant.prenom || '';\n    const lastName = etudiant.lastName || etudiant.nom || '';\n    if (firstName && lastName) {\n      return `${firstName} ${lastName}`.trim();\n    }\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || '';\n    if (fullName) {\n      return fullName.trim();\n    }\n    // Priorité 3: firstName seul\n    if (firstName) {\n      return firstName.trim();\n    }\n    // Priorité 4: email comme fallback\n    if (etudiant.email) {\n      return etudiant.email;\n    }\n    return 'Utilisateur inconnu';\n  }\n};\nDetailProjectComponent = __decorate([Component({\n  selector: 'app-detail-project',\n  templateUrl: './detail-project.component.html',\n  styleUrls: ['./detail-project.component.css']\n})], DetailProjectComponent);", "map": {"version": 3, "names": ["Component", "DetailProjectComponent", "constructor", "route", "router", "projectService", "fileService", "renduService", "projet", "rendus", "totalEtudiants", "etudiantsRendus", "derniersRendus", "isLoading", "ngOnInit", "id", "snapshot", "paramMap", "get", "loadProjectData", "getProjetById", "subscribe", "next", "data", "loadProjectStatistics", "error", "err", "console", "projetId", "getRendusByProjet", "filter", "rendu", "etudiant", "sort", "a", "b", "Date", "dateRendu", "getTime", "slice", "etudiantsUniques", "Set", "map", "r", "_id", "Math", "max", "size", "estimateStudentCount", "groupe", "toLowerCase", "includes", "getFileUrl", "filePath", "getDownloadUrl", "deleteProjet", "confirm", "alert", "navigate", "formatDate", "date", "d", "getDate", "toString", "padStart", "getMonth", "getFullYear", "getFileName", "parts", "split", "length", "getProjectStatus", "dateLimite", "now", "deadline", "getStatusClass", "status", "getProgressPercentage", "round", "getRemainingDays", "diffTime", "diffDays", "ceil", "getStudentInitials", "firstName", "prenom", "lastName", "nom", "char<PERSON>t", "toUpperCase", "fullName", "name", "trim", "substring", "getStudentName", "email", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { FileService } from 'src/app/services/file.service';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-project',\r\n  templateUrl: './detail-project.component.html',\r\n  styleUrls: ['./detail-project.component.css'],\r\n})\r\nexport class DetailProjectComponent implements OnInit {\r\n  projet: any = null;\r\n  rendus: any[] = [];\r\n  totalEtudiants: number = 0;\r\n  etudiantsRendus: any[] = [];\r\n  derniersRendus: any[] = [];\r\n  isLoading: boolean = true;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private projectService: ProjetService,\r\n    private fileService: FileService,\r\n    private renduService: RenduService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const id = this.route.snapshot.paramMap.get('id');\r\n    if (id) {\r\n      this.loadProjectData(id);\r\n    }\r\n  }\r\n\r\n  loadProjectData(id: string): void {\r\n    this.isLoading = true;\r\n\r\n    // Charger les données du projet\r\n    this.projectService.getProjetById(id).subscribe({\r\n      next: (data: any) => {\r\n        this.projet = data;\r\n        this.loadProjectStatistics(id);\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors du chargement du projet:', err);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadProjectStatistics(projetId: string): void {\r\n    // Charger les rendus pour ce projet\r\n    this.renduService.getRendusByProjet(projetId).subscribe({\r\n      next: (rendus: any[]) => {\r\n        this.rendus = rendus;\r\n        this.etudiantsRendus = rendus.filter(rendu => rendu.etudiant);\r\n\r\n        // Trier par date pour avoir les derniers rendus\r\n        this.derniersRendus = [...this.etudiantsRendus]\r\n          .sort((a, b) => new Date(b.dateRendu).getTime() - new Date(a.dateRendu).getTime())\r\n          .slice(0, 5); // Prendre les 5 derniers\r\n\r\n        // Pour le total d'étudiants, on peut estimer ou récupérer depuis le backend\r\n        // Pour l'instant, on utilise le nombre d'étudiants uniques qui ont rendu + estimation\r\n        const etudiantsUniques = new Set(this.etudiantsRendus.map(r => r.etudiant._id || r.etudiant));\r\n        this.totalEtudiants = Math.max(etudiantsUniques.size, this.estimateStudentCount());\r\n\r\n        this.isLoading = false;\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors du chargement des statistiques:', err);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  estimateStudentCount(): number {\r\n    // Estimation basée sur le groupe du projet\r\n    const groupe = this.projet?.groupe?.toLowerCase();\r\n    if (groupe?.includes('1c')) return 25; // Première année\r\n    if (groupe?.includes('2c')) return 20; // Deuxième année\r\n    if (groupe?.includes('3c')) return 15; // Troisième année\r\n    return 20; // Valeur par défaut\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    return this.fileService.getDownloadUrl(filePath);\r\n  }\r\n\r\n  deleteProjet(id: string | undefined): void {\r\n    if (!id) return;\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\r\n      this.projectService.deleteProjet(id).subscribe({\r\n        next: () => {\r\n          alert('Projet supprimé avec succès');\r\n          this.router.navigate(['/admin/projects']);\r\n        },\r\n        error: (err) => {\r\n          console.error('Erreur lors de la suppression du projet', err);\r\n          alert('Erreur lors de la suppression du projet');\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  formatDate(date: string | Date): string {\r\n    const d = new Date(date);\r\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1)\r\n      .toString()\r\n      .padStart(2, '0')}/${d.getFullYear()}`;\r\n  }\r\n\r\n  getFileName(filePath: string): string {\r\n    if (!filePath) return 'Fichier';\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      return parts[parts.length - 1];\r\n    }\r\n\r\n    return filePath;\r\n  }\r\n\r\n  getProjectStatus(): string {\r\n    if (!this.projet?.dateLimite) return 'En cours';\r\n\r\n    const now = new Date();\r\n    const deadline = new Date(this.projet.dateLimite);\r\n\r\n    if (deadline < now) {\r\n      return 'Expiré';\r\n    } else if (deadline.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000) {\r\n      return 'Urgent';\r\n    } else {\r\n      return 'Actif';\r\n    }\r\n  }\r\n\r\n  getStatusClass(): string {\r\n    const status = this.getProjectStatus();\r\n\r\n    switch (status) {\r\n      case 'Actif':\r\n        return 'bg-success/10 dark:bg-dark-accent-secondary/20 text-success dark:text-dark-accent-secondary border border-success/20 dark:border-dark-accent-secondary/30';\r\n      case 'Urgent':\r\n        return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning border border-warning/20 dark:border-warning/30';\r\n      case 'Expiré':\r\n        return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark border border-danger/20 dark:border-danger-dark/30';\r\n      default:\r\n        return 'bg-info/10 dark:bg-dark-accent-primary/20 text-info dark:text-dark-accent-primary border border-info/20 dark:border-dark-accent-primary/30';\r\n    }\r\n  }\r\n\r\n  getProgressPercentage(): number {\r\n    if (this.totalEtudiants === 0) return 0;\r\n    return Math.round((this.etudiantsRendus.length / this.totalEtudiants) * 100);\r\n  }\r\n\r\n  getRemainingDays(): number {\r\n    if (!this.projet?.dateLimite) return 0;\r\n\r\n    const now = new Date();\r\n    const deadline = new Date(this.projet.dateLimite);\r\n    const diffTime = deadline.getTime() - now.getTime();\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n\r\n    return Math.max(0, diffDays);\r\n  }\r\n\r\n  getStudentInitials(etudiant: any): string {\r\n    if (!etudiant) return '??';\r\n\r\n    // Priorité 1: firstName + lastName\r\n    const firstName = etudiant.firstName || etudiant.prenom || '';\r\n    const lastName = etudiant.lastName || etudiant.nom || '';\r\n\r\n    if (firstName && lastName) {\r\n      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\r\n    }\r\n\r\n    // Priorité 2: fullName\r\n    const fullName = etudiant.fullName || etudiant.name || '';\r\n    if (fullName) {\r\n      const parts = fullName.trim().split(' ');\r\n      if (parts.length >= 2) {\r\n        return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\r\n      } else {\r\n        return fullName.substring(0, 2).toUpperCase();\r\n      }\r\n    }\r\n\r\n    // Priorité 3: firstName seul\r\n    if (firstName) {\r\n      return firstName.substring(0, 2).toUpperCase();\r\n    }\r\n\r\n    return '??';\r\n  }\r\n\r\n  getStudentName(etudiant: any): string {\r\n    if (!etudiant) return 'Utilisateur inconnu';\r\n\r\n    // Priorité 1: firstName + lastName\r\n    const firstName = etudiant.firstName || etudiant.prenom || '';\r\n    const lastName = etudiant.lastName || etudiant.nom || '';\r\n\r\n    if (firstName && lastName) {\r\n      return `${firstName} ${lastName}`.trim();\r\n    }\r\n\r\n    // Priorité 2: fullName\r\n    const fullName = etudiant.fullName || etudiant.name || '';\r\n    if (fullName) {\r\n      return fullName.trim();\r\n    }\r\n\r\n    // Priorité 3: firstName seul\r\n    if (firstName) {\r\n      return firstName.trim();\r\n    }\r\n\r\n    // Priorité 4: email comme fallback\r\n    if (etudiant.email) {\r\n      return etudiant.email;\r\n    }\r\n\r\n    return 'Utilisateur inconnu';\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAW1C,WAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAQjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA6B,EAC7BC,WAAwB,EACxBC,YAA0B;IAJ1B,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IAZtB,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,cAAc,GAAW,CAAC;IAC1B,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,SAAS,GAAY,IAAI;EAQtB;EAEHC,QAAQA,CAAA;IACN,MAAMC,EAAE,GAAG,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACI,eAAe,CAACJ,EAAE,CAAC;;EAE5B;EAEAI,eAAeA,CAACJ,EAAU;IACxB,IAAI,CAACF,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACR,cAAc,CAACe,aAAa,CAACL,EAAE,CAAC,CAACM,SAAS,CAAC;MAC9CC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACf,MAAM,GAAGe,IAAI;QAClB,IAAI,CAACC,qBAAqB,CAACT,EAAE,CAAC;MAChC,CAAC;MACDU,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEC,GAAG,CAAC;QAC1D,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAW,qBAAqBA,CAACI,QAAgB;IACpC;IACA,IAAI,CAACrB,YAAY,CAACsB,iBAAiB,CAACD,QAAQ,CAAC,CAACP,SAAS,CAAC;MACtDC,IAAI,EAAGb,MAAa,IAAI;QACtB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACE,eAAe,GAAGF,MAAM,CAACqB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;QAE7D;QACA,IAAI,CAACpB,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC,CAC5CsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAACC,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC,CAACC,OAAO,EAAE,CAAC,CACjFC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEhB;QACA;QACA,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC9B,eAAe,CAAC+B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACX,QAAQ,CAACY,GAAG,IAAID,CAAC,CAACX,QAAQ,CAAC,CAAC;QAC7F,IAAI,CAACtB,cAAc,GAAGmC,IAAI,CAACC,GAAG,CAACN,gBAAgB,CAACO,IAAI,EAAE,IAAI,CAACC,oBAAoB,EAAE,CAAC;QAElF,IAAI,CAACnC,SAAS,GAAG,KAAK;MACxB,CAAC;MACDY,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,6CAA6C,EAAEC,GAAG,CAAC;QACjE,IAAI,CAACb,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAmC,oBAAoBA,CAAA;IAClB;IACA,MAAMC,MAAM,GAAG,IAAI,CAACzC,MAAM,EAAEyC,MAAM,EAAEC,WAAW,EAAE;IACjD,IAAID,MAAM,EAAEE,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACvC,IAAIF,MAAM,EAAEE,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACvC,IAAIF,MAAM,EAAEE,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACvC,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAC,UAAUA,CAACC,QAAgB;IACzB,OAAO,IAAI,CAAC/C,WAAW,CAACgD,cAAc,CAACD,QAAQ,CAAC;EAClD;EAEAE,YAAYA,CAACxC,EAAsB;IACjC,IAAI,CAACA,EAAE,EAAE;IAET,IAAIyC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAACnD,cAAc,CAACkD,YAAY,CAACxC,EAAE,CAAC,CAACM,SAAS,CAAC;QAC7CC,IAAI,EAAEA,CAAA,KAAK;UACTmC,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC;QACDjC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;UAC7D+B,KAAK,CAAC,yCAAyC,CAAC;QAClD;OACD,CAAC;;EAEN;EAEAE,UAAUA,CAACC,IAAmB;IAC5B,MAAMC,CAAC,GAAG,IAAIzB,IAAI,CAACwB,IAAI,CAAC;IACxB,OAAO,GAAGC,CAAC,CAACC,OAAO,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACH,CAAC,CAACI,QAAQ,EAAE,GAAG,CAAC,EACnEF,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,CAAC,CAACK,WAAW,EAAE,EAAE;EAC1C;EAEAC,WAAWA,CAACd,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACF,QAAQ,CAAC,GAAG,CAAC,IAAIE,QAAQ,CAACF,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMiB,KAAK,GAAGf,QAAQ,CAACgB,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOjB,QAAQ;EACjB;EAEAkB,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC/D,MAAM,EAAEgE,UAAU,EAAE,OAAO,UAAU;IAE/C,MAAMC,GAAG,GAAG,IAAIrC,IAAI,EAAE;IACtB,MAAMsC,QAAQ,GAAG,IAAItC,IAAI,CAAC,IAAI,CAAC5B,MAAM,CAACgE,UAAU,CAAC;IAEjD,IAAIE,QAAQ,GAAGD,GAAG,EAAE;MAClB,OAAO,QAAQ;KAChB,MAAM,IAAIC,QAAQ,CAACpC,OAAO,EAAE,GAAGmC,GAAG,CAACnC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;MACvE,OAAO,QAAQ;KAChB,MAAM;MACL,OAAO,OAAO;;EAElB;EAEAqC,cAAcA,CAAA;IACZ,MAAMC,MAAM,GAAG,IAAI,CAACL,gBAAgB,EAAE;IAEtC,QAAQK,MAAM;MACZ,KAAK,OAAO;QACV,OAAO,2JAA2J;MACpK,KAAK,QAAQ;QACX,OAAO,iHAAiH;MAC1H,KAAK,QAAQ;QACX,OAAO,0HAA0H;MACnI;QACE,OAAO,4IAA4I;;EAEzJ;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACnE,cAAc,KAAK,CAAC,EAAE,OAAO,CAAC;IACvC,OAAOmC,IAAI,CAACiC,KAAK,CAAE,IAAI,CAACnE,eAAe,CAAC2D,MAAM,GAAG,IAAI,CAAC5D,cAAc,GAAI,GAAG,CAAC;EAC9E;EAEAqE,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACvE,MAAM,EAAEgE,UAAU,EAAE,OAAO,CAAC;IAEtC,MAAMC,GAAG,GAAG,IAAIrC,IAAI,EAAE;IACtB,MAAMsC,QAAQ,GAAG,IAAItC,IAAI,CAAC,IAAI,CAAC5B,MAAM,CAACgE,UAAU,CAAC;IACjD,MAAMQ,QAAQ,GAAGN,QAAQ,CAACpC,OAAO,EAAE,GAAGmC,GAAG,CAACnC,OAAO,EAAE;IACnD,MAAM2C,QAAQ,GAAGpC,IAAI,CAACqC,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAOnC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEmC,QAAQ,CAAC;EAC9B;EAEAE,kBAAkBA,CAACnD,QAAa;IAC9B,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;IAE1B;IACA,MAAMoD,SAAS,GAAGpD,QAAQ,CAACoD,SAAS,IAAIpD,QAAQ,CAACqD,MAAM,IAAI,EAAE;IAC7D,MAAMC,QAAQ,GAAGtD,QAAQ,CAACsD,QAAQ,IAAItD,QAAQ,CAACuD,GAAG,IAAI,EAAE;IAExD,IAAIH,SAAS,IAAIE,QAAQ,EAAE;MACzB,OAAO,CAACF,SAAS,CAACI,MAAM,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE;;IAGjE;IACA,MAAMC,QAAQ,GAAG1D,QAAQ,CAAC0D,QAAQ,IAAI1D,QAAQ,CAAC2D,IAAI,IAAI,EAAE;IACzD,IAAID,QAAQ,EAAE;MACZ,MAAMtB,KAAK,GAAGsB,QAAQ,CAACE,IAAI,EAAE,CAACvB,KAAK,CAAC,GAAG,CAAC;MACxC,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;QACrB,OAAO,CAACF,KAAK,CAAC,CAAC,CAAC,CAACoB,MAAM,CAAC,CAAC,CAAC,GAAGpB,KAAK,CAAC,CAAC,CAAC,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE;OAC/D,MAAM;QACL,OAAOC,QAAQ,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,WAAW,EAAE;;;IAIjD;IACA,IAAIL,SAAS,EAAE;MACb,OAAOA,SAAS,CAACS,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,WAAW,EAAE;;IAGhD,OAAO,IAAI;EACb;EAEAK,cAAcA,CAAC9D,QAAa;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,qBAAqB;IAE3C;IACA,MAAMoD,SAAS,GAAGpD,QAAQ,CAACoD,SAAS,IAAIpD,QAAQ,CAACqD,MAAM,IAAI,EAAE;IAC7D,MAAMC,QAAQ,GAAGtD,QAAQ,CAACsD,QAAQ,IAAItD,QAAQ,CAACuD,GAAG,IAAI,EAAE;IAExD,IAAIH,SAAS,IAAIE,QAAQ,EAAE;MACzB,OAAO,GAAGF,SAAS,IAAIE,QAAQ,EAAE,CAACM,IAAI,EAAE;;IAG1C;IACA,MAAMF,QAAQ,GAAG1D,QAAQ,CAAC0D,QAAQ,IAAI1D,QAAQ,CAAC2D,IAAI,IAAI,EAAE;IACzD,IAAID,QAAQ,EAAE;MACZ,OAAOA,QAAQ,CAACE,IAAI,EAAE;;IAGxB;IACA,IAAIR,SAAS,EAAE;MACb,OAAOA,SAAS,CAACQ,IAAI,EAAE;;IAGzB;IACA,IAAI5D,QAAQ,CAAC+D,KAAK,EAAE;MAClB,OAAO/D,QAAQ,CAAC+D,KAAK;;IAGvB,OAAO,qBAAqB;EAC9B;CACD;AA3NY9F,sBAAsB,GAAA+F,UAAA,EALlChG,SAAS,CAAC;EACTiG,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,gCAAgC;CAC7C,CAAC,C,EACWlG,sBAAsB,CA2NlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}