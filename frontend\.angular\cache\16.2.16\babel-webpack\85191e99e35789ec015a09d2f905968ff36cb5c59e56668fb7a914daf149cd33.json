{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_24_li_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fichier_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r7, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (fichier_r7 == null ? null : fichier_r7.split(\"/\").pop()) || \"Fichier\", \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_24_li_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1, \"Fichier non disponible\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_24_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_15_div_24_li_4_a_1_Template, 2, 2, \"a\", 33);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_15_div_24_li_4_span_2_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fichier_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", fichier_r7);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !fichier_r7);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"h3\", 30);\n    i0.ɵɵtext(2, \"Fichiers joints:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 31);\n    i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_15_div_24_li_4_Template, 3, 2, \"li\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_34_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre l'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_34_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 37);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_15_form_34_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"div\", 39)(3, \"label\", 40);\n    i0.ɵɵtext(4, \"Structure du code (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39)(7, \"label\", 40);\n    i0.ɵɵtext(8, \"Bonnes pratiques (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"label\", 40);\n    i0.ɵɵtext(12, \"Fonctionnalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 39)(15, \"label\", 40);\n    i0.ɵɵtext(16, \"Originalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 45)(19, \"label\", 40);\n    i0.ɵɵtext(20, \"Commentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 47)(23, \"p\")(24, \"strong\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 48)(27, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_form_34_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.annuler());\n    });\n    i0.ɵɵtext(28, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 50);\n    i0.ɵɵtemplate(30, ProjectEvaluationComponent_div_15_form_34_span_30_Template, 2, 0, \"span\", 12);\n    i0.ɵɵtemplate(31, ProjectEvaluationComponent_div_15_form_34_span_31_Template, 2, 0, \"span\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.evaluationForm);\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate2(\"Score total: \", ctx_r4.getScoreTotal(), \"/\", ctx_r4.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_35_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancer l'\\u00E9valuation IA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_35_div_1_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancement en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 53);\n    i0.ɵɵtext(2, \"L'\\u00E9valuation sera r\\u00E9alis\\u00E9e automatiquement par notre syst\\u00E8me d'IA (Mistral 7B).\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 21);\n    i0.ɵɵtext(4, \"L'IA analysera le code soumis et fournira une \\u00E9valuation bas\\u00E9e sur les crit\\u00E8res standards.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48)(6, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_div_35_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.annuler());\n    });\n    i0.ɵɵtext(7, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_div_35_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.onSubmit());\n    });\n    i0.ɵɵtemplate(9, ProjectEvaluationComponent_div_15_div_35_div_1_span_9_Template, 2, 0, \"span\", 12);\n    i0.ɵɵtemplate(10, ProjectEvaluationComponent_div_15_div_35_div_1_span_10_Template, 2, 0, \"span\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r16.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_35_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"div\", 56);\n    i0.ɵɵelementStart(2, \"p\", 57);\n    i0.ɵɵtext(3, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 58);\n    i0.ɵɵtext(5, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_15_div_35_div_1_Template, 11, 3, \"div\", 12);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_15_div_35_div_2_Template, 6, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 16)(2, \"h2\", 17);\n    i0.ɵɵtext(3, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 18)(5, \"div\")(6, \"p\")(7, \"span\", 19);\n    i0.ɵɵtext(8, \"Projet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"span\", 19);\n    i0.ɵɵtext(12, \"\\u00C9tudiant:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\")(15, \"p\")(16, \"span\", 19);\n    i0.ɵɵtext(17, \"Date de soumission:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\")(21, \"span\", 19);\n    i0.ɵɵtext(22, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, ProjectEvaluationComponent_div_15_div_24_Template, 5, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 21)(26, \"div\", 22)(27, \"h2\", 23);\n    i0.ɵɵtext(28, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 24)(30, \"span\", 25);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.toggleEvaluationMode());\n    });\n    i0.ɵɵtext(33, \" Changer de mode \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(34, ProjectEvaluationComponent_div_15_form_34_Template, 32, 5, \"form\", 27);\n    i0.ɵɵtemplate(35, ProjectEvaluationComponent_div_15_div_35_Template, 3, 2, \"div\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.projet.titre, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(19, 9, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.description || \"Aucune description\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.evaluationMode === \"manual\" ? \"Manuel\" : \"IA\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        // Filter out null/undefined files\n        if (this.rendu.fichiers) {\n          this.rendu.fichiers = this.rendu.fichiers.filter(fichier => fichier != null && fichier !== '');\n        }\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n    console.log('Form values:', this.evaluationForm.value);\n    this.isSubmitting = true;\n    this.error = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    console.log('Sending evaluation data:', evaluationData);\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/list-rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/list-rendus']);\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 16,\n      vars: 3,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"max-w-5xl\", \"mx-auto\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-blue-500\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"mb-6\", \"p-4\", \"bg-gray-50\", \"rounded-lg\"], [1, \"text-xl\", \"font-semibold\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"font-medium\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-xl\", \"font-semibold\"], [1, \"flex\", \"items-center\"], [1, \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-blue-600\", \"text-white\", \"rounded\", \"hover:bg-blue-700\", \"transition-colors\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gray-50 p-4 rounded-lg\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"font-medium\", \"mb-2\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", \"class\", \"text-blue-600 hover:underline\", 3, \"href\", 4, \"ngIf\"], [\"class\", \"text-gray-500\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", 3, \"href\"], [1, \"text-gray-500\"], [3, \"formGroup\", \"ngSubmit\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\", \"mb-6\"], [1, \"form-group\"], [1, \"block\", \"text-gray-700\", \"mb-2\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [1, \"form-group\", \"mb-6\"], [\"formControlName\", \"commentaires\", \"rows\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [1, \"mb-4\", \"p-3\", \"bg-blue-50\", \"rounded\"], [1, \"flex\", \"justify-between\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"bg-gray-500\", \"text-white\", \"rounded\", \"hover:bg-gray-600\", \"transition-colors\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-6\", \"py-2\", \"bg-green-600\", \"text-white\", \"rounded\", \"hover:bg-green-700\", \"transition-colors\"], [1, \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-blue-600\", \"text-white\", \"rounded\", \"hover:bg-blue-700\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-blue-500\", \"mx-auto\", \"mb-4\"], [1, \"text-gray-700\"], [1, \"text-sm\", \"text-gray-500\", \"mt-2\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 6);\n          i0.ɵɵelement(7, \"path\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n          i0.ɵɵtext(10, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \"Syst\\u00E8me d'\\u00E9valuation intelligent avec IA int\\u00E9gr\\u00E9e\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(13, ProjectEvaluationComponent_div_13_Template, 2, 0, \"div\", 10);\n          i0.ɵɵtemplate(14, ProjectEvaluationComponent_div_14_Template, 2, 1, \"div\", 11);\n          i0.ɵɵtemplate(15, ProjectEvaluationComponent_div_15_Template, 36, 12, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtZXZhbHVhdGlvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG9EQUFvRDtBQUNwRDtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQiIsImZpbGUiOiJwcm9qZWN0LWV2YWx1YXRpb24uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBkJ8OpdmFsdWF0aW9uIGRlIHByb2pldCAqL1xyXG4uY29udGFpbmVyIHtcclxuICBtYXgtd2lkdGg6IDEyMDBweDtcclxuICBtYXJnaW46IDAgYXV0bztcclxufVxyXG5cclxuLmZvcm0tZ3JvdXAge1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5lcnJvci1tZXNzYWdlIHtcclxuICBjb2xvcjogI2RjMzU0NTtcclxuICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xyXG59XHJcblxyXG4ubG9hZGluZy1zcGlubmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIG1hcmdpbjogMnJlbSAwO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvcHJvamVjdC1ldmFsdWF0aW9uL3Byb2plY3QtZXZhbHVhdGlvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG9EQUFvRDtBQUNwRDtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQjtBQUNBLG81QkFBbzVCIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGQnw4PCqXZhbHVhdGlvbiBkZSBwcm9qZXQgKi9cclxuLmNvbnRhaW5lciB7XHJcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbn1cclxuXHJcbi5mb3JtLWdyb3VwIHtcclxuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4uZXJyb3ItbWVzc2FnZSB7XHJcbiAgY29sb3I6ICNkYzM1NDU7XHJcbiAgbWFyZ2luLXRvcDogMC4yNXJlbTtcclxufVxyXG5cclxuLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBtYXJnaW46IDJyZW0gMDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵproperty", "fichier_r7", "ɵɵsanitizeUrl", "split", "pop", "ɵɵtemplate", "ProjectEvaluationComponent_div_15_div_24_li_4_a_1_Template", "ProjectEvaluationComponent_div_15_div_24_li_4_span_2_Template", "ProjectEvaluationComponent_div_15_div_24_li_4_Template", "ctx_r3", "rendu", "fichiers", "ɵɵlistener", "ProjectEvaluationComponent_div_15_form_34_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProjectEvaluationComponent_div_15_form_34_Template_button_click_27_listener", "ctx_r15", "annuler", "ProjectEvaluationComponent_div_15_form_34_span_30_Template", "ProjectEvaluationComponent_div_15_form_34_span_31_Template", "ctx_r4", "evaluationForm", "ɵɵtextInterpolate2", "getScoreTotal", "getScoreMaximum", "isSubmitting", "ProjectEvaluationComponent_div_15_div_35_div_1_Template_button_click_6_listener", "_r21", "ctx_r20", "ProjectEvaluationComponent_div_15_div_35_div_1_Template_button_click_8_listener", "ctx_r22", "ProjectEvaluationComponent_div_15_div_35_div_1_span_9_Template", "ProjectEvaluationComponent_div_15_div_35_div_1_span_10_Template", "ctx_r16", "ProjectEvaluationComponent_div_15_div_35_div_1_Template", "ProjectEvaluationComponent_div_15_div_35_div_2_Template", "ctx_r5", "aiProcessing", "ProjectEvaluationComponent_div_15_div_24_Template", "ProjectEvaluationComponent_div_15_Template_button_click_32_listener", "_r24", "ctx_r23", "toggleEvaluationMode", "ProjectEvaluationComponent_div_15_form_34_Template", "ProjectEvaluationComponent_div_15_div_35_Template", "ctx_r2", "projet", "titre", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "length", "ɵɵtextInterpolate", "evaluationMode", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "isLoading", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "filter", "<PERSON><PERSON><PERSON>", "err", "console", "log", "valid", "value", "evaluationData", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ProjectEvaluationComponent_div_13_Template", "ProjectEvaluationComponent_div_14_Template", "ProjectEvaluationComponent_div_15_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        // Filter out null/undefined files\r\n        if (this.rendu.fichiers) {\r\n          this.rendu.fichiers = this.rendu.fichiers.filter((fichier: any) => fichier != null && fichier !== '');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\r\n    console.log('Form values:', this.evaluationForm.value);\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n    console.log('Sending evaluation data:', evaluationData);\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/list-rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    this.router.navigate(['/admin/projects/list-rendus']);\r\n  }\r\n\r\n\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n    <div class=\"max-w-5xl mx-auto\">\r\n      <!-- Header avec gradient -->\r\n      <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm\">\r\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h1 class=\"text-3xl font-bold text-white mb-2\">Évaluation du projet</h1>\r\n            <p class=\"text-white/80\">Système d'évaluation intelligent avec IA intégrée</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\r\n      <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n    </div>\r\n\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error }}\r\n    </div>\r\n\r\n    <div *ngIf=\"rendu && !isLoading\">\r\n      <div class=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n        <h2 class=\"text-xl font-semibold mb-4\">Informations sur le rendu</h2>\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <p><span class=\"font-medium\">Projet:</span> {{ rendu.projet.titre }}</p>\r\n            <p><span class=\"font-medium\">Étudiant:</span> {{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n          </div>\r\n          <div>\r\n            <p><span class=\"font-medium\">Date de soumission:</span> {{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n            <p><span class=\"font-medium\">Description:</span> {{ rendu.description || 'Aucune description' }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"mt-4\">\r\n          <h3 class=\"font-medium mb-2\">Fichiers joints:</h3>\r\n          <ul class=\"list-disc pl-5\">\r\n            <li *ngFor=\"let fichier of rendu.fichiers\">\r\n              <a *ngIf=\"fichier\" [href]=\"'http://localhost:3000/' + fichier\" target=\"_blank\" class=\"text-blue-600 hover:underline\">\r\n                {{ fichier?.split('/').pop() || 'Fichier' }}\r\n              </a>\r\n              <span *ngIf=\"!fichier\" class=\"text-gray-500\">Fichier non disponible</span>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-6\">\r\n        <div class=\"flex items-center justify-between mb-4\">\r\n          <h2 class=\"text-xl font-semibold\">Mode d'évaluation</h2>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"mr-2\">{{ evaluationMode === 'manual' ? 'Manuel' : 'IA' }}</span>\r\n            <button\r\n              (click)=\"toggleEvaluationMode()\"\r\n              class=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\r\n            >\r\n              Changer de mode\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\">\r\n          <div formGroupName=\"scores\" class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Structure du code (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"structure\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Bonnes pratiques (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"pratiques\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Fonctionnalité (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"fonctionnalite\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Originalité (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"originalite\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-group mb-6\">\r\n            <label class=\"block text-gray-700 mb-2\">Commentaires</label>\r\n            <textarea\r\n              formControlName=\"commentaires\"\r\n              rows=\"5\"\r\n              class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            ></textarea>\r\n          </div>\r\n\r\n          <div class=\"mb-4 p-3 bg-blue-50 rounded\">\r\n            <p><strong>Score total: {{ getScoreTotal() }}/{{ getScoreMaximum() }}</strong></p>\r\n          </div>\r\n\r\n          <div class=\"flex justify-between\">\r\n            <button\r\n              type=\"button\"\r\n              (click)=\"annuler()\"\r\n              class=\"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors\"\r\n            >\r\n              Annuler\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              class=\"px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors\"\r\n            >\r\n              <span *ngIf=\"!isSubmitting\">Soumettre l'évaluation</span>\r\n              <span *ngIf=\"isSubmitting\">Soumission en cours...</span>\r\n            </button>\r\n          </div>\r\n        </form>\r\n\r\n        <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n          <div *ngIf=\"!aiProcessing\">\r\n            <p class=\"mb-4\">L'évaluation sera réalisée automatiquement par notre système d'IA (Mistral 7B).</p>\r\n            <p class=\"mb-6\">L'IA analysera le code soumis et fournira une évaluation basée sur les critères standards.</p>\r\n\r\n            <div class=\"flex justify-between\">\r\n              <button\r\n                type=\"button\"\r\n                (click)=\"annuler()\"\r\n                class=\"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors\"\r\n              >\r\n                Annuler\r\n              </button>\r\n              <button\r\n                (click)=\"onSubmit()\"\r\n                [disabled]=\"isSubmitting\"\r\n                class=\"px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50\"\r\n              >\r\n                <span *ngIf=\"!isSubmitting\">Lancer l'évaluation IA</span>\r\n                <span *ngIf=\"isSubmitting\">Lancement en cours...</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"aiProcessing\" class=\"text-center py-8\">\r\n            <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"></div>\r\n            <p class=\"text-gray-700\">L'IA analyse le projet...</p>\r\n            <p class=\"text-sm text-gray-500 mt-2\">Cela peut prendre quelques instants</p>\r\n          </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICiB/DC,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,cAA6F;IAC/FF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAoBUR,EAAA,CAAAC,cAAA,YAAqH;IACnHD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAFeH,EAAA,CAAAS,UAAA,oCAAAC,UAAA,EAAAV,EAAA,CAAAW,aAAA,CAA2C;IAC5DX,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAI,UAAA,kBAAAA,UAAA,CAAAE,KAAA,MAAAC,GAAA,sBACF;;;;;IACAb,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAJ5EH,EAAA,CAAAC,cAAA,SAA2C;IACzCD,EAAA,CAAAc,UAAA,IAAAC,0DAAA,gBAEI;IACJf,EAAA,CAAAc,UAAA,IAAAE,6DAAA,mBAA0E;IAC5EhB,EAAA,CAAAG,YAAA,EAAK;;;;IAJCH,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAa;IAGVV,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAS,UAAA,UAAAC,UAAA,CAAc;;;;;IAP3BV,EAAA,CAAAC,cAAA,cAAsE;IACvCD,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAc,UAAA,IAAAG,sDAAA,iBAKK;IACPjB,EAAA,CAAAG,YAAA,EAAK;;;;IANqBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAS,UAAA,YAAAS,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;IA6FvCpB,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAI,MAAA,kCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAtE9DH,EAAA,CAAAC,cAAA,eAA+F;IAA5DD,EAAA,CAAAqB,UAAA,sBAAAC,4EAAA;MAAAtB,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAA0B,aAAA;MAAA,OAAY1B,EAAA,CAAA2B,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxD5B,EAAA,CAAAC,cAAA,cAA+E;IAEnCD,EAAA,CAAAI,MAAA,8BAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACvEH,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACkBD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACtEH,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAI,MAAA,iCAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACpEH,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAA6B;IACaD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAE,SAAA,oBAIY;IACdF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAyC;IAC5BD,EAAA,CAAAI,MAAA,IAA0D;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGhFH,EAAA,CAAAC,cAAA,eAAkC;IAG9BD,EAAA,CAAAqB,UAAA,mBAAAQ,4EAAA;MAAA7B,EAAA,CAAAuB,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAA9B,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAG,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnB/B,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IACCD,EAAA,CAAAc,UAAA,KAAAkB,0DAAA,mBAAyD;IACzDhC,EAAA,CAAAc,UAAA,KAAAmB,0DAAA,mBAAwD;IAC1DjC,EAAA,CAAAG,YAAA,EAAS;;;;IAvEPH,EAAA,CAAAS,UAAA,cAAAyB,MAAA,CAAAC,cAAA,CAA4B;IAsDnBnC,EAAA,CAAAK,SAAA,IAA0D;IAA1DL,EAAA,CAAAoC,kBAAA,kBAAAF,MAAA,CAAAG,aAAA,SAAAH,MAAA,CAAAI,eAAA,OAA0D;IAe5DtC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAyB,MAAA,CAAAK,YAAA,CAAmB;IACnBvC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAyB,MAAA,CAAAK,YAAA,CAAkB;;;;;IAuBvBvC,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAI,MAAA,kCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,4BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlB7DH,EAAA,CAAAC,cAAA,UAA2B;IACTD,EAAA,CAAAI,MAAA,0GAA+E;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACnGH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAI,MAAA,gHAA0F;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAE9GH,EAAA,CAAAC,cAAA,cAAkC;IAG9BD,EAAA,CAAAqB,UAAA,mBAAAmB,gFAAA;MAAAxC,EAAA,CAAAuB,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAe,OAAA,CAAAX,OAAA,EAAS;IAAA,EAAC;IAGnB/B,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAIC;IAHCD,EAAA,CAAAqB,UAAA,mBAAAsB,gFAAA;MAAA3C,EAAA,CAAAuB,aAAA,CAAAkB,IAAA;MAAA,MAAAG,OAAA,GAAA5C,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAAiB,OAAA,CAAAhB,QAAA,EAAU;IAAA,EAAC;IAIpB5B,EAAA,CAAAc,UAAA,IAAA+B,8DAAA,mBAAyD;IACzD7C,EAAA,CAAAc,UAAA,KAAAgC,+DAAA,mBAAuD;IACzD9C,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAS,UAAA,aAAAsC,OAAA,CAAAR,YAAA,CAAyB;IAGlBvC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAsC,OAAA,CAAAR,YAAA,CAAmB;IACnBvC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAsC,OAAA,CAAAR,YAAA,CAAkB;;;;;IAK/BvC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAA0G;IAC1GF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAI,MAAA,0CAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IA3BjFH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAc,UAAA,IAAAkC,uDAAA,mBAqBM;IAENhD,EAAA,CAAAc,UAAA,IAAAmC,uDAAA,kBAIM;IACVjD,EAAA,CAAAG,YAAA,EAAM;;;;IA5BIH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAyC,MAAA,CAAAC,YAAA,CAAmB;IAuBnBnD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAyC,MAAA,CAAAC,YAAA,CAAkB;;;;;;IA5I9BnD,EAAA,CAAAC,cAAA,UAAiC;IAEUD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,cAAmD;IAElBD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,GAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,sBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAExGH,EAAA,CAAAC,cAAA,WAAK;IAC0BD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChHH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAA+C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIxGH,EAAA,CAAAc,UAAA,KAAAsC,iDAAA,kBAUM;IACRpD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAEoBD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,eAA+B;IACVD,EAAA,CAAAI,MAAA,IAAmD;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAqB,UAAA,mBAAAgC,oEAAA;MAAArD,EAAA,CAAAuB,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAA0B,aAAA;MAAA,OAAS1B,EAAA,CAAA2B,WAAA,CAAA4B,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCxD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAc,UAAA,KAAA2C,kDAAA,oBAyEO;IAEPzD,EAAA,CAAAc,UAAA,KAAA4C,iDAAA,kBA6BI;IACR1D,EAAA,CAAAG,YAAA,EAAM;;;;IA7I8CH,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,kBAAA,MAAAqD,MAAA,CAAAxC,KAAA,CAAAyC,MAAA,CAAAC,KAAA,KAAwB;IACtB7D,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAoC,kBAAA,MAAAuB,MAAA,CAAAxC,KAAA,CAAA2C,QAAA,CAAAC,GAAA,OAAAJ,MAAA,CAAAxC,KAAA,CAAA2C,QAAA,CAAAE,MAAA,KAAoD;IAG1ChE,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAiE,WAAA,QAAAN,MAAA,CAAAxC,KAAA,CAAA+C,cAAA,0BAAoD;IAC3DlE,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAM,kBAAA,MAAAqD,MAAA,CAAAxC,KAAA,CAAAgD,WAAA,6BAA+C;IAI9FnE,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAS,UAAA,SAAAkD,MAAA,CAAAxC,KAAA,CAAAC,QAAA,IAAAuC,MAAA,CAAAxC,KAAA,CAAAC,QAAA,CAAAgD,MAAA,KAAiD;IAiBhCpE,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAqE,iBAAA,CAAAV,MAAA,CAAAW,cAAA,gCAAmD;IAUdtE,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAS,UAAA,SAAAkD,MAAA,CAAAW,cAAA,cAAiC;IA2EvFtE,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAS,UAAA,SAAAkD,MAAA,CAAAW,cAAA,UAA6B;;;ADpI3C,OAAM,MAAOC,0BAA0B;EAUrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAbvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAA1D,KAAK,GAAQ,IAAI;IAEjB,KAAA2D,SAAS,GAAY,IAAI;IACzB,KAAAvC,YAAY,GAAY,KAAK;IAC7B,KAAA/B,KAAK,GAAW,EAAE;IAClB,KAAA8D,cAAc,GAAoB,QAAQ;IAC1C,KAAAnB,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAAChB,cAAc,GAAG,IAAI,CAACsC,EAAE,CAACM,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACP,EAAE,CAACM,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAClF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,EAAEpF,UAAU,CAACqF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACtF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,EAAEpF,UAAU,CAACqF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACvF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,EAAEpF,UAAU,CAACqF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACxF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,EAAEpF,UAAU,CAACqF,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAEzF,UAAU,CAACmF,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACb,OAAO,GAAG,IAAI,CAACH,KAAK,CAACiB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACpB,KAAK,CAACiB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACxB,cAAc,GAAGwB,IAAI;MAC1B,IAAI,CAAC3D,cAAc,CAAC6D,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC7B,cAAc,GAAG6B,UAAU;QAChC,IAAI,CAAChE,cAAc,CAAC6D,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACtB,OAAO,EAAE;MAChB,IAAI,CAACwB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAC7F,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACsE,SAAS,GAAG,KAAK;;EAE1B;EAEAuB,SAASA,CAAA;IACP,IAAI,CAACvB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAAC0B,YAAY,CAAC,IAAI,CAACzB,OAAO,CAAC,CAAC0B,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACtF,KAAK,GAAGsF,IAAI;QACjB;QACA,IAAI,IAAI,CAACtF,KAAK,CAACC,QAAQ,EAAE;UACvB,IAAI,CAACD,KAAK,CAACC,QAAQ,GAAG,IAAI,CAACD,KAAK,CAACC,QAAQ,CAACsF,MAAM,CAAEC,OAAY,IAAKA,OAAO,IAAI,IAAI,IAAIA,OAAO,KAAK,EAAE,CAAC;;QAEvG,IAAI,CAAC7B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDtE,KAAK,EAAGoG,GAAQ,IAAI;QAClB,IAAI,CAACpG,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACsE,SAAS,GAAG,KAAK;QACtB+B,OAAO,CAACrG,KAAK,CAACoG,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEApD,oBAAoBA,CAAA;IAClB,IAAI,CAACc,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAACnC,cAAc,CAAC6D,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACnB,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E2B,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC5B,cAAc,CAAC;EAC7D;EAEA1C,QAAQA,CAAA;IACNiF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC3E,cAAc,CAAC4E,KAAK,CAAC;IACrEF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC3E,cAAc,CAAC6E,KAAK,CAAC;IAEtD,IAAI,CAACzE,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC/B,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAAC8D,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAACnC,cAAc,CAAC6D,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACtC,YAAY,GAAG,IAAI;;IAG1B,MAAM8D,cAAc,GAAG,IAAI,CAAC9E,cAAc,CAAC6E,KAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEG,cAAc,CAAC;IAEvD,IAAI,CAACrC,aAAa,CAACsC,aAAa,CAAC,IAAI,CAACrC,OAAO,EAAEoC,cAAc,CAAC,CAACV,SAAS,CAAC;MACvEC,IAAI,EAAGW,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC7C,cAAc,KAAK,IAAI,IAAI6C,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACpC,MAAM;UAC3C,MAAMsC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAAC5B,YAAY;UAEvD,IAAI,CAACrD,cAAc,CAAC6D,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEoC,QAAQ,CAACpC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAEgC,QAAQ,CAAChC,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE+B,QAAQ,CAAC/B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE8B,QAAQ,CAAC9B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE8B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAACnE,YAAY,GAAG,KAAK;UACzB,IAAI,CAACZ,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAAC/B,KAAK,GAAG,EAAE;UACf+G,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAAChF,YAAY,GAAG,KAAK;UACzBgF,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;;MAEzD,CAAC;MACDhH,KAAK,EAAGoG,GAAQ,IAAI;QAClB,IAAI,CAACpG,KAAK,GAAG,yCAAyC,IAAIoG,GAAG,CAACpG,KAAK,EAAEiH,OAAO,IAAIb,GAAG,CAACa,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAAClF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACY,YAAY,GAAG,KAAK;QACzB0D,OAAO,CAACrG,KAAK,CAACoG,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAvE,aAAaA,CAAA;IACX,MAAM2C,MAAM,GAAG,IAAI,CAAC7C,cAAc,CAAC0D,GAAG,CAAC,QAAQ,CAAC,EAAEmB,KAAK;IACvD,IAAI,CAAChC,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAjD,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAP,OAAOA,CAAA;IACL,IAAI,CAAC4C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EACvD;;;uBApJWjD,0BAA0B,EAAAvE,EAAA,CAAA0H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5H,EAAA,CAAA0H,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA9H,EAAA,CAAA0H,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA/H,EAAA,CAAA0H,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1B1D,0BAA0B;MAAA2D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvCxI,EAAA,CAAAC,cAAA,aAA8F;UAOlFD,EAAA,CAAA0I,cAAA,EAAsF;UAAtF1I,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAE,SAAA,cAA+H;UACjIF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAA2I,eAAA,EAAK;UAAL3I,EAAA,CAAAC,cAAA,UAAK;UAC4CD,EAAA,CAAAI,MAAA,iCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACxEH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAI,MAAA,6EAAiD;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAKtFH,EAAA,CAAAc,UAAA,KAAA8H,0CAAA,kBAEM;UAEN5I,EAAA,CAAAc,UAAA,KAAA+H,0CAAA,kBAEM;UAEN7I,EAAA,CAAAc,UAAA,KAAAgI,0CAAA,oBAmJI;UACR9I,EAAA,CAAAG,YAAA,EAAM;;;UA5JIH,EAAA,CAAAK,SAAA,IAAe;UAAfL,EAAA,CAAAS,UAAA,SAAAgI,GAAA,CAAA3D,SAAA,CAAe;UAIf9E,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAS,UAAA,SAAAgI,GAAA,CAAAjI,KAAA,CAAW;UAIXR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAS,UAAA,SAAAgI,GAAA,CAAAtH,KAAA,KAAAsH,GAAA,CAAA3D,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}