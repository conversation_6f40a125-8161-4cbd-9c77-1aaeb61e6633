{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_21_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r7, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fichier_r7.split(\"/\").pop(), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h3\", 22);\n    i0.ɵɵtext(2, \"Fichiers joints:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 23);\n    i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_6_div_21_li_4_Template, 3, 2, \"li\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 26);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_6_form_31_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Structure du code (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"label\", 29);\n    i0.ɵɵtext(8, \"Bonnes pratiques (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 28)(11, \"label\", 29);\n    i0.ɵɵtext(12, \"Fonctionnalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 28)(15, \"label\", 29);\n    i0.ɵɵtext(16, \"Originalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 34)(19, \"label\", 29);\n    i0.ɵɵtext(20, \"Commentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 36)(23, \"button\", 37);\n    i0.ɵɵtext(24, \" Soumettre l'\\u00E9valuation \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.evaluationForm);\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.evaluationForm.invalid || ctx_r4.isLoading);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 40);\n    i0.ɵɵtext(2, \"L'\\u00E9valuation sera r\\u00E9alis\\u00E9e automatiquement par notre syst\\u00E8me d'IA (Mistral 7B).\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 13);\n    i0.ɵɵtext(4, \"L'IA analysera le code soumis et fournira une \\u00E9valuation bas\\u00E9e sur les crit\\u00E8res standards.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_div_32_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r12.onSubmit());\n    });\n    i0.ɵɵtext(7, \" Lancer l'\\u00E9valuation IA \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_32_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"div\", 43);\n    i0.ɵɵelementStart(2, \"p\", 44);\n    i0.ɵɵtext(3, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 45);\n    i0.ɵɵtext(5, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_6_div_32_div_1_Template, 8, 1, \"div\", 5);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_6_div_32_div_2_Template, 6, 0, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 9)(2, \"h2\", 10);\n    i0.ɵɵtext(3, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"span\", 11);\n    i0.ɵɵtext(6, \"Projet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\")(9, \"span\", 11);\n    i0.ɵɵtext(10, \"\\u00C9tudiant:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\")(13, \"span\", 11);\n    i0.ɵɵtext(14, \"Date de soumission:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\")(18, \"span\", 11);\n    i0.ɵɵtext(19, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, ProjectEvaluationComponent_div_6_div_21_Template, 5, 1, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 13)(23, \"div\", 14)(24, \"h2\", 15);\n    i0.ɵɵtext(25, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 16)(27, \"span\", 17);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.toggleEvaluationMode());\n    });\n    i0.ɵɵtext(30, \" Changer de mode \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(31, ProjectEvaluationComponent_div_6_form_31_Template, 25, 2, \"form\", 19);\n    i0.ɵɵtemplate(32, ProjectEvaluationComponent_div_6_div_32_Template, 3, 2, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.projet.titre, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 9, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.description, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.evaluationMode === \"manual\" ? \"Manuel\" : \"IA\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.successMessage = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\n      this.markFormGroupTouched(this.evaluationForm);\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\n      return;\n    }\n    this.isSubmitting = true;\n    this.error = '';\n    this.successMessage = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = ''; // Effacer les erreurs précédentes\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/list-rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/rendus']);\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 7,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"mb-6\", \"text-gray-800\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-purple-500\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"mb-6\", \"p-4\", \"bg-gray-50\", \"rounded-lg\"], [1, \"text-xl\", \"font-semibold\", \"mb-2\"], [1, \"font-medium\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-xl\", \"font-semibold\"], [1, \"flex\", \"items-center\"], [1, \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded\", \"hover:bg-purple-700\", \"transition-colors\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gray-50 p-4 rounded-lg\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"font-medium\", \"mb-2\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", 3, \"href\"], [3, \"formGroup\", \"ngSubmit\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\", \"mb-6\"], [1, \"form-group\"], [1, \"block\", \"text-gray-700\", \"mb-2\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [1, \"form-group\", \"mb-6\"], [\"formControlName\", \"commentaires\", \"rows\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-purple-500\"], [1, \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"px-6\", \"py-2\", \"bg-green-600\", \"text-white\", \"rounded\", \"hover:bg-green-700\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-green-600\", \"text-white\", \"rounded\", \"hover:bg-green-700\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-purple-500\", \"mx-auto\", \"mb-4\"], [1, \"text-gray-700\"], [1, \"text-sm\", \"text-gray-500\", \"mt-2\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_4_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ProjectEvaluationComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵtemplate(6, ProjectEvaluationComponent_div_6_Template, 33, 12, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtZXZhbHVhdGlvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG9EQUFvRDtBQUNwRDtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQiIsImZpbGUiOiJwcm9qZWN0LWV2YWx1YXRpb24uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBkJ8OpdmFsdWF0aW9uIGRlIHByb2pldCAqL1xyXG4uY29udGFpbmVyIHtcclxuICBtYXgtd2lkdGg6IDEyMDBweDtcclxuICBtYXJnaW46IDAgYXV0bztcclxufVxyXG5cclxuLmZvcm0tZ3JvdXAge1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5lcnJvci1tZXNzYWdlIHtcclxuICBjb2xvcjogI2RjMzU0NTtcclxuICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xyXG59XHJcblxyXG4ubG9hZGluZy1zcGlubmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIG1hcmdpbjogMnJlbSAwO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvcHJvamVjdC1ldmFsdWF0aW9uL3Byb2plY3QtZXZhbHVhdGlvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLG9EQUFvRDtBQUNwRDtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQjtBQUNBLG81QkFBbzVCIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGQnw4PCqXZhbHVhdGlvbiBkZSBwcm9qZXQgKi9cclxuLmNvbnRhaW5lciB7XHJcbiAgbWF4LXdpZHRoOiAxMjAwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbn1cclxuXHJcbi5mb3JtLWdyb3VwIHtcclxuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4uZXJyb3ItbWVzc2FnZSB7XHJcbiAgY29sb3I6ICNkYzM1NDU7XHJcbiAgbWFyZ2luLXRvcDogMC4yNXJlbTtcclxufVxyXG5cclxuLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBtYXJnaW46IDJyZW0gMDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵproperty", "fichier_r7", "ɵɵsanitizeUrl", "split", "pop", "ɵɵtemplate", "ProjectEvaluationComponent_div_6_div_21_li_4_Template", "ctx_r3", "rendu", "fichiers", "ɵɵlistener", "ProjectEvaluationComponent_div_6_form_31_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ctx_r4", "evaluationForm", "invalid", "isLoading", "ProjectEvaluationComponent_div_6_div_32_div_1_Template_button_click_6_listener", "_r13", "ctx_r12", "ctx_r10", "isSubmitting", "ProjectEvaluationComponent_div_6_div_32_div_1_Template", "ProjectEvaluationComponent_div_6_div_32_div_2_Template", "ctx_r5", "aiProcessing", "ProjectEvaluationComponent_div_6_div_21_Template", "ProjectEvaluationComponent_div_6_Template_button_click_29_listener", "_r15", "ctx_r14", "toggleEvaluationMode", "ProjectEvaluationComponent_div_6_form_31_Template", "ProjectEvaluationComponent_div_6_div_32_Template", "ctx_r2", "projet", "titre", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "length", "ɵɵtextInterpolate", "evaluationMode", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "successMessage", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "err", "console", "markFormGroupTouched", "evaluationData", "value", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "getScoreTotal", "getScoreMaximum", "annuler", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ProjectEvaluationComponent_div_4_Template", "ProjectEvaluationComponent_div_5_Template", "ProjectEvaluationComponent_div_6_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  successMessage: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\r\n      this.markFormGroupTouched(this.evaluationForm);\r\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n    this.successMessage = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = ''; // Effacer les erreurs précédentes\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          // Afficher un message de succès\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/list-rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    this.router.navigate(['/admin/projects/rendus']);\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"container mx-auto px-4 py-8\">\r\n  <div class=\"max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6\">\r\n    <h1 class=\"text-2xl font-bold mb-6 text-gray-800\">Évaluation du projet</h1>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\r\n      <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500\"></div>\r\n    </div>\r\n\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error }}\r\n    </div>\r\n\r\n    <div *ngIf=\"rendu && !isLoading\">\r\n      <div class=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n        <h2 class=\"text-xl font-semibold mb-2\">Informations sur le rendu</h2>\r\n        <p><span class=\"font-medium\">Projet:</span> {{ rendu.projet.titre }}</p>\r\n        <p><span class=\"font-medium\">Étudiant:</span> {{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n        <p><span class=\"font-medium\">Date de soumission:</span> {{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n        <p><span class=\"font-medium\">Description:</span> {{ rendu.description }}</p>\r\n        \r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"mt-4\">\r\n          <h3 class=\"font-medium mb-2\">Fichiers joints:</h3>\r\n          <ul class=\"list-disc pl-5\">\r\n            <li *ngFor=\"let fichier of rendu.fichiers\">\r\n              <a [href]=\"'http://localhost:3000/' + fichier\" target=\"_blank\" class=\"text-blue-600 hover:underline\">\r\n                {{ fichier.split('/').pop() }}\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-6\">\r\n        <div class=\"flex items-center justify-between mb-4\">\r\n          <h2 class=\"text-xl font-semibold\">Mode d'évaluation</h2>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"mr-2\">{{ evaluationMode === 'manual' ? 'Manuel' : 'IA' }}</span>\r\n            <button \r\n              (click)=\"toggleEvaluationMode()\" \r\n              class=\"px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors\"\r\n            >\r\n              Changer de mode\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\">\r\n          <div formGroupName=\"scores\" class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Structure du code (0-5)</label>\r\n              <input \r\n                type=\"number\" \r\n                formControlName=\"structure\" \r\n                min=\"0\" \r\n                max=\"5\" \r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Bonnes pratiques (0-5)</label>\r\n              <input \r\n                type=\"number\" \r\n                formControlName=\"pratiques\" \r\n                min=\"0\" \r\n                max=\"5\" \r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Fonctionnalité (0-5)</label>\r\n              <input \r\n                type=\"number\" \r\n                formControlName=\"fonctionnalite\" \r\n                min=\"0\" \r\n                max=\"5\" \r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Originalité (0-5)</label>\r\n              <input \r\n                type=\"number\" \r\n                formControlName=\"originalite\" \r\n                min=\"0\" \r\n                max=\"5\" \r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-group mb-6\">\r\n            <label class=\"block text-gray-700 mb-2\">Commentaires</label>\r\n            <textarea \r\n              formControlName=\"commentaires\" \r\n              rows=\"5\" \r\n              class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-purple-500\"\r\n            ></textarea>\r\n          </div>\r\n\r\n          <div class=\"flex justify-end\">\r\n            <button \r\n              type=\"submit\" \r\n              [disabled]=\"evaluationForm.invalid || isLoading\" \r\n              class=\"px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50\"\r\n            >\r\n              Soumettre l'évaluation\r\n            </button>\r\n          </div>\r\n        </form>\r\n\r\n        <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n          <div *ngIf=\"!aiProcessing\">\r\n            <p class=\"mb-4\">L'évaluation sera réalisée automatiquement par notre système d'IA (Mistral 7B).</p>\r\n            <p class=\"mb-6\">L'IA analysera le code soumis et fournira une évaluation basée sur les critères standards.</p>\r\n            \r\n            <div class=\"flex justify-end\">\r\n              <button \r\n                (click)=\"onSubmit()\" \r\n                [disabled]=\"isSubmitting\" \r\n                class=\"px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50\"\r\n              >\r\n                Lancer l'évaluation IA\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          <div *ngIf=\"aiProcessing\" class=\"text-center py-8\">\r\n            <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4\"></div>\r\n            <p class=\"text-gray-700\">L'IA analyse le projet...</p>\r\n            <p class=\"text-sm text-gray-500 mt-2\">Cela peut prendre quelques instants</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICG/DC,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,SAAA,aAA+F;IACjGF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAAgG;IAC9FD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAaQR,EAAA,CAAAC,cAAA,SAA2C;IAEvCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAFDH,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAS,UAAA,oCAAAC,UAAA,EAAAV,EAAA,CAAAW,aAAA,CAA2C;IAC5CX,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAI,UAAA,CAAAE,KAAA,MAAAC,GAAA,QACF;;;;;IANNb,EAAA,CAAAC,cAAA,cAAsE;IACvCD,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAc,UAAA,IAAAC,qDAAA,iBAIK;IACPf,EAAA,CAAAG,YAAA,EAAK;;;;IALqBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAS,UAAA,YAAAO,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;;IAuB7ClB,EAAA,CAAAC,cAAA,eAA+F;IAA5DD,EAAA,CAAAmB,UAAA,sBAAAC,2EAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAYxB,EAAA,CAAAyB,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxD1B,EAAA,CAAAC,cAAA,cAA+E;IAEnCD,EAAA,CAAAI,MAAA,8BAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACvEH,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACkBD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACtEH,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAI,MAAA,iCAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACpEH,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAA6B;IACaD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAE,SAAA,oBAIY;IACdF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA8B;IAM1BD,EAAA,CAAAI,MAAA,qCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IA5DPH,EAAA,CAAAS,UAAA,cAAAkB,MAAA,CAAAC,cAAA,CAA4B;IAwD5B5B,EAAA,CAAAK,SAAA,IAAgD;IAAhDL,EAAA,CAAAS,UAAA,aAAAkB,MAAA,CAAAC,cAAA,CAAAC,OAAA,IAAAF,MAAA,CAAAG,SAAA,CAAgD;;;;;;IASpD9B,EAAA,CAAAC,cAAA,UAA2B;IACTD,EAAA,CAAAI,MAAA,0GAA+E;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACnGH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAI,MAAA,gHAA0F;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAE9GH,EAAA,CAAAC,cAAA,cAA8B;IAE1BD,EAAA,CAAAmB,UAAA,mBAAAY,+EAAA;MAAA/B,EAAA,CAAAqB,aAAA,CAAAW,IAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAQ,OAAA,CAAAP,QAAA,EAAU;IAAA,EAAC;IAIpB1B,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAS,UAAA,aAAAyB,OAAA,CAAAC,YAAA,CAAyB;;;;;IAQ/BnC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAA4G;IAC5GF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAI,MAAA,0CAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IAnBjFH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAc,UAAA,IAAAsB,sDAAA,iBAaM;IAENpC,EAAA,CAAAc,UAAA,IAAAuB,sDAAA,kBAIM;IACRrC,EAAA,CAAAG,YAAA,EAAM;;;;IApBEH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAA6B,MAAA,CAAAC,YAAA,CAAmB;IAenBvC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAA6B,MAAA,CAAAC,YAAA,CAAkB;;;;;;IAlH9BvC,EAAA,CAAAC,cAAA,UAAiC;IAEUD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,QAAG;IAA0BD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,GAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,QAAG;IAA0BD,EAAA,CAAAI,MAAA,sBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACtGH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChHH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAE5EH,EAAA,CAAAc,UAAA,KAAA0B,gDAAA,kBASM;IACRxC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAEoBD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,eAA+B;IACVD,EAAA,CAAAI,MAAA,IAAmD;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAmB,UAAA,mBAAAsB,mEAAA;MAAAzC,EAAA,CAAAqB,aAAA,CAAAqB,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAkB,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC5C,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAc,UAAA,KAAA+B,iDAAA,oBA8DO;IAEP7C,EAAA,CAAAc,UAAA,KAAAgC,gDAAA,kBAqBM;IACR9C,EAAA,CAAAG,YAAA,EAAM;;;;IArHwCH,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,kBAAA,MAAAyC,MAAA,CAAA9B,KAAA,CAAA+B,MAAA,CAAAC,KAAA,KAAwB;IACtBjD,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAkD,kBAAA,MAAAH,MAAA,CAAA9B,KAAA,CAAAkC,QAAA,CAAAC,GAAA,OAAAL,MAAA,CAAA9B,KAAA,CAAAkC,QAAA,CAAAE,MAAA,KAAoD;IAC1CrD,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAAsD,WAAA,QAAAP,MAAA,CAAA9B,KAAA,CAAAsC,cAAA,0BAAoD;IAC3DvD,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,kBAAA,MAAAyC,MAAA,CAAA9B,KAAA,CAAAuC,WAAA,KAAuB;IAElExD,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAS,UAAA,SAAAsC,MAAA,CAAA9B,KAAA,CAAAC,QAAA,IAAA6B,MAAA,CAAA9B,KAAA,CAAAC,QAAA,CAAAuC,MAAA,KAAiD;IAgBhCzD,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAA0D,iBAAA,CAAAX,MAAA,CAAAY,cAAA,gCAAmD;IAUd3D,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAS,UAAA,SAAAsC,MAAA,CAAAY,cAAA,cAAiC;IAgEvF3D,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAS,UAAA,SAAAsC,MAAA,CAAAY,cAAA,UAA6B;;;ADpG3C,OAAM,MAAOC,0BAA0B;EAWrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAdvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAjD,KAAK,GAAQ,IAAI;IAEjB,KAAAa,SAAS,GAAY,IAAI;IACzB,KAAAK,YAAY,GAAY,KAAK;IAC7B,KAAA3B,KAAK,GAAW,EAAE;IAClB,KAAA2D,cAAc,GAAW,EAAE;IAC3B,KAAAR,cAAc,GAAoB,QAAQ;IAC1C,KAAApB,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAACX,cAAc,GAAG,IAAI,CAACkC,EAAE,CAACM,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACP,EAAE,CAACM,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAACvE,UAAU,CAACwE,QAAQ,EAAExE,UAAU,CAACyE,GAAG,CAAC,CAAC,CAAC,EAAEzE,UAAU,CAAC0E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC3E,UAAU,CAACwE,QAAQ,EAAExE,UAAU,CAACyE,GAAG,CAAC,CAAC,CAAC,EAAEzE,UAAU,CAAC0E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC5E,UAAU,CAACwE,QAAQ,EAAExE,UAAU,CAACyE,GAAG,CAAC,CAAC,CAAC,EAAEzE,UAAU,CAAC0E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC7E,UAAU,CAACwE,QAAQ,EAAExE,UAAU,CAACyE,GAAG,CAAC,CAAC,CAAC,EAAEzE,UAAU,CAAC0E,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAE9E,UAAU,CAACwE,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACb,OAAO,GAAG,IAAI,CAACH,KAAK,CAACiB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACpB,KAAK,CAACiB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACxB,cAAc,GAAGwB,IAAI;MAC1B,IAAI,CAACvD,cAAc,CAACyD,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC7B,cAAc,GAAG6B,UAAU;QAChC,IAAI,CAAC5D,cAAc,CAACyD,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACtB,OAAO,EAAE;MAChB,IAAI,CAACwB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAClF,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACsB,SAAS,GAAG,KAAK;;EAE1B;EAEA4D,SAASA,CAAA;IACP,IAAI,CAAC5D,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmC,aAAa,CAAC0B,YAAY,CAAC,IAAI,CAACzB,OAAO,CAAC,CAAC0B,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAAC7E,KAAK,GAAG6E,IAAI;QACjB,IAAI,CAAChE,SAAS,GAAG,KAAK;MACxB,CAAC;MACDtB,KAAK,EAAGuF,GAAQ,IAAI;QAClB,IAAI,CAACvF,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACsB,SAAS,GAAG,KAAK;QACtBkE,OAAO,CAACxF,KAAK,CAACuF,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAnD,oBAAoBA,CAAA;IAClB,IAAI,CAACe,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAAC/B,cAAc,CAACyD,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACnB,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E2B,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC5B,cAAc,CAAC;EAC7D;EAEAjC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACiC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAAC/B,cAAc,CAACC,OAAO,EAAE;MACnE,IAAI,CAACoE,oBAAoB,CAAC,IAAI,CAACrE,cAAc,CAAC;MAC9C,IAAI,CAACpB,KAAK,GAAG,gDAAgD;MAC7D;;IAGF,IAAI,CAAC2B,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC3B,KAAK,GAAG,EAAE;IACf,IAAI,CAAC2D,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,IAAI,CAACR,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAAC/B,cAAc,CAACyD,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACvC,YAAY,GAAG,IAAI;;IAG1B,MAAM2D,cAAc,GAAG,IAAI,CAACtE,cAAc,CAACuE,KAAK;IAEhD,IAAI,CAAClC,aAAa,CAACmC,aAAa,CAAC,IAAI,CAAClC,OAAO,EAAEgC,cAAc,CAAC,CAACN,SAAS,CAAC;MACvEC,IAAI,EAAGQ,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC1C,cAAc,KAAK,IAAI,IAAI0C,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACjC,MAAM;UAC3C,MAAMmC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAACzB,YAAY;UAEvD,IAAI,CAACjD,cAAc,CAACyD,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEiC,QAAQ,CAACjC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAE6B,QAAQ,CAAC7B,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE4B,QAAQ,CAAC5B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE2B,QAAQ,CAAC3B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE2B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAACjE,YAAY,GAAG,KAAK;UACzB,IAAI,CAACJ,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAAC3B,KAAK,GAAG,EAAE,CAAC,CAAC;UACjBiG,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAACtE,YAAY,GAAG,KAAK;UACzB;UACAsE,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAACzC,MAAM,CAAC0C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;;MAEzD,CAAC;MACDlG,KAAK,EAAGuF,GAAQ,IAAI;QAClB,IAAI,CAACvF,KAAK,GAAG,yCAAyC,IAAIuF,GAAG,CAACvF,KAAK,EAAEmG,OAAO,IAAIZ,GAAG,CAACY,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAACxE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACI,YAAY,GAAG,KAAK;QACzByD,OAAO,CAACxF,KAAK,CAACuF,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAa,aAAaA,CAAA;IACX,MAAMvC,MAAM,GAAG,IAAI,CAACzC,cAAc,CAACsD,GAAG,CAAC,QAAQ,CAAC,EAAEiB,KAAK;IACvD,IAAI,CAAC9B,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAiC,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC9C,MAAM,CAAC0C,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;;;uBArJW9C,0BAA0B,EAAA5D,EAAA,CAAA+G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjH,EAAA,CAAA+G,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnH,EAAA,CAAA+G,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAApH,EAAA,CAAA+G,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1B1D,0BAA0B;MAAA2D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvC7H,EAAA,CAAAC,cAAA,aAAyC;UAEaD,EAAA,CAAAI,MAAA,gCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAE3EH,EAAA,CAAAc,UAAA,IAAAiH,yCAAA,iBAEM;UAEN/H,EAAA,CAAAc,UAAA,IAAAkH,yCAAA,iBAEM;UAENhI,EAAA,CAAAc,UAAA,IAAAmH,yCAAA,mBAyHM;UACRjI,EAAA,CAAAG,YAAA,EAAM;;;UAlIEH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAS,UAAA,SAAAqH,GAAA,CAAAhG,SAAA,CAAe;UAIf9B,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAS,UAAA,SAAAqH,GAAA,CAAAtH,KAAA,CAAW;UAIXR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAS,UAAA,SAAAqH,GAAA,CAAA7G,KAAA,KAAA6G,GAAA,CAAAhG,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}