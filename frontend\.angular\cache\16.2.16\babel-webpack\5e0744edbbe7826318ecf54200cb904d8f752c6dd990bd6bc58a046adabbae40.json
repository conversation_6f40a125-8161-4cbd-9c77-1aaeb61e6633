{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/file.service\";\nimport * as i4 from \"@angular/common\";\nfunction DetailProjectComponent_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 77)(3, \"div\", 78)(4, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 30);\n    i0.ɵɵelement(6, \"path\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"div\", 80)(8, \"p\", 81);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 82);\n    i0.ɵɵtext(11, \"Document\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"a\", 83)(13, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 84);\n    i0.ɵɵelement(15, \"path\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const file_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getFileName(file_r5), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r4.getFileUrl(file_r5), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailProjectComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, DetailProjectComponent_div_54_div_1_Template, 18, 2, \"div\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projet.fichiers);\n  }\n}\nfunction DetailProjectComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 89);\n    i0.ɵɵelement(4, \"path\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Aucun fichier joint \\u00E0 ce projet\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DetailProjectComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 90);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 91)(4, \"div\", 92);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const etudiant_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", (etudiant_r6.nom == null ? null : etudiant_r6.nom.charAt(0)) || \"\", \"\", (etudiant_r6.prenom == null ? null : etudiant_r6.prenom.charAt(0)) || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", etudiant_r6.prenom, \" \", etudiant_r6.nom, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(etudiant_r6.dateRendu));\n  }\n}\nfunction DetailProjectComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵtext(1, \" Aucun rendu pour le moment \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c1 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c2 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nconst _c3 = function () {\n  return [];\n};\nexport class DetailProjectComponent {\n  constructor(route, router, projectService, fileService) {\n    this.route = route;\n    this.router = router;\n    this.projectService = projectService;\n    this.fileService = fileService;\n    this.projet = null;\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.projectService.getProjetById(id).subscribe(data => {\n        this.projet = data;\n      });\n    }\n  }\n  getFileUrl(filePath) {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n  deleteProjet(id) {\n    if (!id) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        }\n      });\n    }\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n  }\n  static {\n    this.ɵfac = function DetailProjectComponent_Factory(t) {\n      return new (t || DetailProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailProjectComponent,\n      selectors: [[\"app-detail-project\"]],\n      decls: 113,\n      vars: 28,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"prose\", \"prose-gray\", \"dark:prose-invert\", \"max-w-none\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"leading-relaxed\"], [1, \"bg-secondary/10\", \"dark:bg-dark-accent-secondary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-secondary\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-normal\", \"text-text\", \"dark:text-dark-text-secondary\", \"ml-2\"], [\"class\", \"grid grid-cols-1 sm:grid-cols-2 gap-4\", 4, \"ngIf\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-3\", \"gap-4\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"bg-white\", \"rounded-2xl\", \"shadow-md\", \"border\", \"border-[#e4e7ec]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"p-4\", \"text-white\"], [1, \"font-semibold\"], [1, \"p-6\", \"space-y-6\"], [1, \"flex\", \"justify-between\", \"mb-2\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-sm\", \"font-medium\", \"text-[#6C63FF]\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2.5\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"h-2.5\", \"rounded-full\"], [1, \"grid\", \"grid-cols-2\", \"gap-4\"], [1, \"bg-[#f0f7ff]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4A00E0]\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"bg-[#fff5f5]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#E02D6D]\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-600\", \"mb-3\"], [1, \"space-y-3\"], [\"class\", \"flex items-center space-x-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-sm text-gray-500 italic text-center py-2\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [\"class\", \"group\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\", \"border\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"hover:border-primary\", \"dark:hover:border-dark-accent-primary\", \"transition-all\", \"duration-200\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"flex-1\", \"min-w-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [\"download\", \"\", 1, \"ml-3\", \"px-3\", \"py-2\", \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-center\", \"py-8\"], [1, \"bg-gray-100\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-6\"], [1, \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-xs\", \"font-bold\"], [1, \"text-sm\"], [1, \"font-medium\"], [1, \"text-sm\", \"text-gray-500\", \"italic\", \"text-center\", \"py-2\"]],\n      template: function DetailProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n          i0.ɵɵtext(5, \"Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 5);\n          i0.ɵɵelement(7, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 12);\n          i0.ɵɵelement(15, \"path\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(16, \"div\")(17, \"h1\", 14);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 17);\n          i0.ɵɵelement(22, \"path\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"span\", 19);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 20);\n          i0.ɵɵelement(27, \"path\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"span\", 22);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"span\", 24);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 25)(34, \"div\", 26)(35, \"div\", 27)(36, \"div\", 28)(37, \"div\", 29);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 30);\n          i0.ɵɵelement(39, \"path\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(40, \"h3\", 32);\n          i0.ɵɵtext(41, \"Description du projet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 33)(43, \"p\", 34);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 27)(46, \"div\", 28)(47, \"div\", 35);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(48, \"svg\", 36);\n          i0.ɵɵelement(49, \"path\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(50, \"h3\", 32);\n          i0.ɵɵtext(51, \" Fichiers joints \");\n          i0.ɵɵelementStart(52, \"span\", 38);\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(54, DetailProjectComponent_div_54_Template, 2, 1, \"div\", 39);\n          i0.ɵɵtemplate(55, DetailProjectComponent_div_55_Template, 7, 0, \"div\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 27)(57, \"div\", 28)(58, \"div\", 41);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(59, \"svg\", 42);\n          i0.ɵɵelement(60, \"path\", 43)(61, \"path\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(62, \"h3\", 32);\n          i0.ɵɵtext(63, \"Actions disponibles\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"div\", 45)(65, \"a\", 46)(66, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(67, \"svg\", 48);\n          i0.ɵɵelement(68, \"path\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(69, \"span\");\n          i0.ɵɵtext(70, \"Modifier\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"a\", 50)(72, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(73, \"svg\", 48);\n          i0.ɵɵelement(74, \"path\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(75, \"span\");\n          i0.ɵɵtext(76, \"Voir rendus\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(77, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function DetailProjectComponent_Template_button_click_77_listener() {\n            return ctx.deleteProjet(ctx.projet == null ? null : ctx.projet._id);\n          });\n          i0.ɵɵelementStart(78, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(79, \"svg\", 48);\n          i0.ɵɵelement(80, \"path\", 53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(81, \"span\");\n          i0.ɵɵtext(82, \"Supprimer\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(83, \"div\", 54)(84, \"div\", 55)(85, \"h3\", 56);\n          i0.ɵɵtext(86, \"Statistiques de rendu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 57)(88, \"div\")(89, \"div\", 58)(90, \"span\", 59);\n          i0.ɵɵtext(91, \"Progression\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"span\", 60);\n          i0.ɵɵtext(93);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"div\", 61);\n          i0.ɵɵelement(95, \"div\", 62);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"div\", 63)(97, \"div\", 64)(98, \"div\", 65);\n          i0.ɵɵtext(99);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"div\", 66);\n          i0.ɵɵtext(101, \"Rendus\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(102, \"div\", 67)(103, \"div\", 68);\n          i0.ɵɵtext(104);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 66);\n          i0.ɵɵtext(106, \"En attente\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"div\")(108, \"h4\", 69);\n          i0.ɵɵtext(109, \"Derniers rendus\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 70);\n          i0.ɵɵtemplate(111, DetailProjectComponent_div_111_Template, 8, 5, \"div\", 71);\n          i0.ɵɵtemplate(112, DetailProjectComponent_div_112_Template, 2, 0, \"div\", 72);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.dateLimite) ? ctx.formatDate(ctx.projet == null ? null : ctx.projet.dateLimite) : \"Pas de date limite\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getProjectStatus(), \" \");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.description) || \"Aucune description fournie pour ce projet.\", \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate2(\" (\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0, \" fichier\", ((ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0) > 1 ? \"s\" : \"\", \") \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.fichiers) || ctx.projet.fichiers.length === 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(22, _c0, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(24, _c1))(\"queryParams\", i0.ɵɵpureFunction1(25, _c2, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(22);\n          i0.ɵɵtextInterpolate2(\"\", (ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0, \"/\", (ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0) / ((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 1) * 100, \"%\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0) - ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", (ctx.projet == null ? null : ctx.projet.derniersRendus) || i0.ɵɵpureFunction0(27, _c3));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.derniersRendus) || ctx.projet.derniersRendus.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvZGV0YWlsLXByb2plY3QvZGV0YWlsLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r4", "getFileName", "file_r5", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "DetailProjectComponent_div_54_div_1_Template", "ctx_r0", "projet", "fichiers", "ɵɵtextInterpolate2", "etudiant_r6", "nom", "char<PERSON>t", "prenom", "ɵɵtextInterpolate", "ctx_r2", "formatDate", "dateRendu", "DetailProjectComponent", "constructor", "route", "router", "projectService", "fileService", "ngOnInit", "id", "snapshot", "paramMap", "get", "getProjetById", "subscribe", "data", "filePath", "getDownloadUrl", "deleteProjet", "confirm", "next", "alert", "navigate", "error", "err", "console", "date", "d", "Date", "getDate", "toString", "padStart", "getMonth", "getFullYear", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "FileService", "selectors", "decls", "vars", "consts", "template", "DetailProjectComponent_Template", "rf", "ctx", "DetailProjectComponent_div_54_Template", "DetailProjectComponent_div_55_Template", "ɵɵlistener", "DetailProjectComponent_Template_button_click_77_listener", "_id", "DetailProjectComponent_div_111_Template", "DetailProjectComponent_div_112_Template", "titre", "groupe", "dateLimite", "getStatusClass", "getProjectStatus", "description", "length", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction0", "_c1", "_c2", "etudiantsRendus", "totalEtudiants", "ɵɵstyleProp", "derniersRendus", "_c3"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { FileService } from 'src/app/services/file.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-project',\r\n  templateUrl: './detail-project.component.html',\r\n  styleUrls: ['./detail-project.component.css'],\r\n})\r\nexport class DetailProjectComponent implements OnInit {\r\n  projet: any = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private projectService: ProjetService,\r\n    private fileService: FileService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const id = this.route.snapshot.paramMap.get('id');\r\n    if (id) {\r\n      this.projectService.getProjetById(id).subscribe((data: any) => {\r\n        this.projet = data;\r\n      });\r\n    }\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    return this.fileService.getDownloadUrl(filePath);\r\n  }\r\n\r\n  deleteProjet(id: string | undefined): void {\r\n    if (!id) return;\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\r\n      this.projectService.deleteProjet(id).subscribe({\r\n        next: () => {\r\n          alert('Projet supprimé avec succès');\r\n          this.router.navigate(['/admin/projects']);\r\n        },\r\n        error: (err) => {\r\n          console.error('Erreur lors de la suppression du projet', err);\r\n          alert('Erreur lors de la suppression du projet');\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  formatDate(date: string | Date): string {\r\n    const d = new Date(date);\r\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1)\r\n      .toString()\r\n      .padStart(2, '0')}/${d.getFullYear()}`;\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n\r\n    <!-- Header moderne avec breadcrumb -->\r\n    <div class=\"mb-8\">\r\n      <nav class=\"flex items-center space-x-2 text-sm text-text dark:text-dark-text-secondary mb-4\">\r\n        <a routerLink=\"/admin/projects/list-project\" class=\"hover:text-primary dark:hover:text-dark-accent-primary transition-colors\">Projets</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <span class=\"text-primary dark:text-dark-accent-primary font-medium\">{{ projet?.titre || 'Détails du projet' }}</span>\r\n      </nav>\r\n\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\r\n          <div class=\"flex items-center space-x-4 mb-6 lg:mb-0\">\r\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\r\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\r\n                {{ projet?.titre || 'Chargement...' }}\r\n              </h1>\r\n              <div class=\"flex items-center space-x-4 mt-2\">\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">{{ projet?.groupe || 'Tous les groupes' }}</span>\r\n                </div>\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm text-text dark:text-dark-text-secondary\">{{ projet?.dateLimite ? formatDate(projet?.dateLimite) : 'Pas de date limite' }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Badge de statut -->\r\n          <div class=\"flex items-center space-x-3\">\r\n            <span [ngClass]=\"getStatusClass()\" class=\"px-4 py-2 rounded-xl text-sm font-medium\">\r\n              {{ getProjectStatus() }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Contenu principal -->\r\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n      <!-- Carte principale du projet -->\r\n      <div class=\"lg:col-span-2 space-y-6\">\r\n\r\n        <!-- Description du projet -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Description du projet</h3>\r\n          </div>\r\n          <div class=\"prose prose-gray dark:prose-invert max-w-none\">\r\n            <p class=\"text-text dark:text-dark-text-secondary leading-relaxed\">\r\n              {{ projet?.description || 'Aucune description fournie pour ce projet.' }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Fichiers du projet -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-secondary/10 dark:bg-dark-accent-secondary/20 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-secondary dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">\r\n              Fichiers joints\r\n              <span class=\"text-sm font-normal text-text dark:text-dark-text-secondary ml-2\">\r\n                ({{ projet?.fichiers?.length || 0 }} fichier{{ (projet?.fichiers?.length || 0) > 1 ? 's' : '' }})\r\n              </span>\r\n            </h3>\r\n          </div>\r\n\r\n          <div *ngIf=\"projet?.fichiers?.length > 0\" class=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n            <div *ngFor=\"let file of projet.fichiers\" class=\"group\">\r\n              <div class=\"bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4 border border-gray-200 dark:border-dark-bg-tertiary hover:border-primary dark:hover:border-dark-accent-primary transition-all duration-200\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div class=\"flex items-center space-x-3 flex-1 min-w-0\">\r\n                    <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\r\n                      <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <div class=\"flex-1 min-w-0\">\r\n                      <p class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary truncate\">\r\n                        {{ getFileName(file) }}\r\n                      </p>\r\n                      <p class=\"text-xs text-text dark:text-dark-text-secondary\">Document</p>\r\n                    </div>\r\n                  </div>\r\n                  <a [href]=\"getFileUrl(file)\" download\r\n                     class=\"ml-3 px-3 py-2 bg-primary/10 dark:bg-dark-accent-primary/20 text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary rounded-lg transition-all duration-200 text-xs font-medium\">\r\n                    <div class=\"flex items-center space-x-1\">\r\n                      <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\r\n                      </svg>\r\n                      <span>Télécharger</span>\r\n                    </div>\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"!projet?.fichiers || projet.fichiers.length === 0\" class=\"text-center py-8\">\r\n            <div class=\"bg-gray-100 dark:bg-dark-bg-tertiary/50 rounded-xl p-6\">\r\n              <div class=\"bg-gray-200 dark:bg-dark-bg-tertiary p-3 rounded-lg inline-flex items-center justify-center mb-3\">\r\n                <svg class=\"w-6 h-6 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                </svg>\r\n              </div>\r\n              <p class=\"text-sm text-text dark:text-dark-text-secondary\">Aucun fichier joint à ce projet</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions du projet -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-info/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Actions disponibles</h3>\r\n          </div>\r\n\r\n          <div class=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n            <a [routerLink]=\"['/admin/projects/editProjet', projet?._id]\"\r\n               class=\"group px-4 py-3 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\r\n                </svg>\r\n                <span>Modifier</span>\r\n              </div>\r\n            </a>\r\n\r\n            <a [routerLink]=\"['/admin/projects/rendus']\" [queryParams]=\"{projetId: projet?._id}\"\r\n               class=\"group px-4 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>\r\n                </svg>\r\n                <span>Voir rendus</span>\r\n              </div>\r\n            </a>\r\n\r\n            <button (click)=\"deleteProjet(projet?._id)\"\r\n                    class=\"group px-4 py-3 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                </svg>\r\n                <span>Supprimer</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    <!-- Dashboard de rendu -->\r\n    <div class=\"bg-white rounded-2xl shadow-md border border-[#e4e7ec] overflow-hidden\">\r\n      <div class=\"bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] p-4 text-white\">\r\n        <h3 class=\"font-semibold\">Statistiques de rendu</h3>\r\n      </div>\r\n      <div class=\"p-6 space-y-6\">\r\n        <!-- Progression générale -->\r\n        <div>\r\n          <div class=\"flex justify-between mb-2\">\r\n            <span class=\"text-sm font-medium text-gray-700\">Progression</span>\r\n            <span class=\"text-sm font-medium text-[#6C63FF]\">{{ (projet?.etudiantsRendus?.length || 0) }}/{{ projet?.totalEtudiants || 0 }}</span>\r\n          </div>\r\n          <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n            <div class=\"bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] h-2.5 rounded-full\"\r\n                 [style.width.%]=\"((projet?.etudiantsRendus?.length || 0) / (projet?.totalEtudiants || 1) * 100)\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Cartes stats -->\r\n        <div class=\"grid grid-cols-2 gap-4\">\r\n          <div class=\"bg-[#f0f7ff] p-4 rounded-lg border border-[#e4e7ec]\">\r\n            <div class=\"text-2xl font-bold text-[#4A00E0]\">{{ projet?.etudiantsRendus?.length || 0 }}</div>\r\n            <div class=\"text-xs text-gray-500\">Rendus</div>\r\n          </div>\r\n          <div class=\"bg-[#fff5f5] p-4 rounded-lg border border-[#e4e7ec]\">\r\n            <div class=\"text-2xl font-bold text-[#E02D6D]\">{{ (projet?.totalEtudiants || 0) - (projet?.etudiantsRendus?.length || 0) }}</div>\r\n            <div class=\"text-xs text-gray-500\">En attente</div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Liste rapide -->\r\n        <div>\r\n          <h4 class=\"text-sm font-semibold text-gray-600 mb-3\">Derniers rendus</h4>\r\n          <div class=\"space-y-3\">\r\n            <div *ngFor=\"let etudiant of projet?.derniersRendus || []\" class=\"flex items-center space-x-3\">\r\n              <div class=\"h-8 w-8 rounded-full bg-[#6C63FF] flex items-center justify-center text-white text-xs font-bold\">\r\n                {{ etudiant.nom?.charAt(0) || '' }}{{ etudiant.prenom?.charAt(0) || '' }}\r\n              </div>\r\n              <div class=\"text-sm\">\r\n                <div class=\"font-medium\">{{ etudiant.prenom }} {{ etudiant.nom }}</div>\r\n                <div class=\"text-xs text-gray-500\">{{ formatDate(etudiant.dateRendu) }}</div>\r\n              </div>\r\n            </div>\r\n            <div *ngIf=\"!projet?.derniersRendus || projet.derniersRendus.length === 0\" class=\"text-sm text-gray-500 italic text-center py-2\">\r\n              Aucun rendu pour le moment\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;;;;;IC4FYA,EAAA,CAAAC,cAAA,cAAwD;IAK9CD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAA4L;IAC9LH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA4B;IAA5BL,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAG3EJ,EAAA,CAAAC,cAAA,aACiP;IAE7OD,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAG,SAAA,gBAAgJ;IAClJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,6BAAW;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IAXtBJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,OACF;IAIDX,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAY,UAAA,SAAAH,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,EAAA,CAAAc,aAAA,CAAyB;;;;;IAjBpCd,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAe,UAAA,IAAAC,4CAAA,mBA2BM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IA5BkBJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,UAAA,YAAAK,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IA8B1CnB,EAAA,CAAAC,cAAA,cAAwF;IAGlFD,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAsM;IACxMH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA2D;IAA3DL,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAM,MAAA,2CAA+B;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IAqFhGJ,EAAA,CAAAC,cAAA,cAA+F;IAE3FD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAqB;IACMD,EAAA,CAAAM,MAAA,GAAwC;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACvEJ,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAM,MAAA,GAAoC;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;IAJ7EJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAoB,kBAAA,OAAAC,WAAA,CAAAC,GAAA,kBAAAD,WAAA,CAAAC,GAAA,CAAAC,MAAA,iBAAAF,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAD,MAAA,gBACF;IAE2BvB,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAoB,kBAAA,KAAAC,WAAA,CAAAG,MAAA,OAAAH,WAAA,CAAAC,GAAA,KAAwC;IAC9BtB,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAyB,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAN,WAAA,CAAAO,SAAA,EAAoC;;;;;IAG3E5B,EAAA,CAAAC,cAAA,cAAiI;IAC/HD,EAAA,CAAAM,MAAA,mCACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;;;;ADvNlB,OAAM,MAAOyB,sBAAsB;EAGjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA6B,EAC7BC,WAAwB;IAHxB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IANrB,KAAAhB,MAAM,GAAQ,IAAI;EAOf;EAEHiB,QAAQA,CAAA;IACN,MAAMC,EAAE,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACH,cAAc,CAACO,aAAa,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAEC,IAAS,IAAI;QAC5D,IAAI,CAACxB,MAAM,GAAGwB,IAAI;MACpB,CAAC,CAAC;;EAEN;EAEA7B,UAAUA,CAAC8B,QAAgB;IACzB,OAAO,IAAI,CAACT,WAAW,CAACU,cAAc,CAACD,QAAQ,CAAC;EAClD;EAEAE,YAAYA,CAACT,EAAsB;IACjC,IAAI,CAACA,EAAE,EAAE;IAET,IAAIU,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAACb,cAAc,CAACY,YAAY,CAACT,EAAE,CAAC,CAACK,SAAS,CAAC;QAC7CM,IAAI,EAAEA,CAAA,KAAK;UACTC,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC;QACDC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;UAC7DH,KAAK,CAAC,yCAAyC,CAAC;QAClD;OACD,CAAC;;EAEN;EAEArB,UAAUA,CAAC0B,IAAmB;IAC5B,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAO,GAAGC,CAAC,CAACE,OAAO,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACJ,CAAC,CAACK,QAAQ,EAAE,GAAG,CAAC,EACnEF,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,CAAC,CAACM,WAAW,EAAE,EAAE;EAC1C;;;uBA7CW/B,sBAAsB,EAAA7B,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/D,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAhE,EAAA,CAAA6D,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAAlE,EAAA,CAAA6D,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtBvC,sBAAsB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTnC3E,EAAA,CAAAC,cAAA,aAAiK;UAM3BD,EAAA,CAAAM,MAAA,cAAO;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACzIJ,EAAA,CAAAE,cAAA,EAA2E;UAA3EF,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAG,SAAA,cAA8F;UAChGH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAqE;UAArEL,EAAA,CAAAC,cAAA,cAAqE;UAAAD,EAAA,CAAAM,MAAA,GAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAGxHJ,EAAA,CAAAC,cAAA,cAA2J;UAInJD,EAAA,CAAAE,cAAA,EAAsF;UAAtFF,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAAG,SAAA,gBAA4J;UAC9JH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,eAA8C;UAE1CD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwV;UAC1VH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,gBAA6E;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAEhIJ,EAAA,CAAAC,cAAA,eAAyC;UACvCD,EAAA,CAAAE,cAAA,EAA0G;UAA1GF,EAAA,CAAAC,cAAA,eAA0G;UACxGD,EAAA,CAAAG,SAAA,gBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA8D;UAA9DL,EAAA,CAAAC,cAAA,gBAA8D;UAAAD,EAAA,CAAAM,MAAA,IAAgF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAO7JJ,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAOfJ,EAAA,CAAAC,cAAA,eAAmD;UAQzCD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwG;UAC1GH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAEzGJ,EAAA,CAAAC,cAAA,eAA2D;UAEvDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAC,cAAA,eAA2J;UAGrJD,EAAA,CAAAE,cAAA,EAA0H;UAA1HF,EAAA,CAAAC,cAAA,eAA0H;UACxHD,EAAA,CAAAG,SAAA,gBAAsM;UACxMH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAC3ED,EAAA,CAAAM,MAAA,yBACA;UAAAN,EAAA,CAAAC,cAAA,gBAA+E;UAC7ED,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIXJ,EAAA,CAAAe,UAAA,KAAA8D,sCAAA,kBA6BM;UAEN7E,EAAA,CAAAe,UAAA,KAAA+D,sCAAA,kBASM;UACR9E,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAA2J;UAGrJD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAqjB;UAEvjBH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAM,MAAA,2BAAmB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAGvGJ,EAAA,CAAAC,cAAA,eAAmD;UAI7CD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwM;UAC1MH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIzBJ,EAAA,CAAAC,cAAA,aACmO;UAE/ND,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAiN;UACnNH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAI5BJ,EAAA,CAAAC,cAAA,kBAC+M;UADvMD,EAAA,CAAA+E,UAAA,mBAAAC,yDAAA;YAAA,OAASJ,GAAA,CAAA/B,YAAA,CAAA+B,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA,CAAyB;UAAA,EAAC;UAEzCjF,EAAA,CAAAC,cAAA,eAAwD;UACtDD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAA8M;UAChNH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,iBAAS;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAQlCJ,EAAA,CAAAC,cAAA,eAAoF;UAEtDD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAEtDJ,EAAA,CAAAC,cAAA,eAA2B;UAI2BD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAClEJ,EAAA,CAAAC,cAAA,gBAAiD;UAAAD,EAAA,CAAAM,MAAA,IAA8E;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAExIJ,EAAA,CAAAC,cAAA,eAAmD;UACjDD,EAAA,CAAAG,SAAA,eAC4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,eAAoC;UAEeD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAC/FJ,EAAA,CAAAC,cAAA,gBAAmC;UAAAD,EAAA,CAAAM,MAAA,eAAM;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAEjDJ,EAAA,CAAAC,cAAA,gBAAiE;UAChBD,EAAA,CAAAM,MAAA,KAA4E;UAAAN,EAAA,CAAAI,YAAA,EAAM;UACjIJ,EAAA,CAAAC,cAAA,gBAAmC;UAAAD,EAAA,CAAAM,MAAA,mBAAU;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAKvDJ,EAAA,CAAAC,cAAA,YAAK;UACkDD,EAAA,CAAAM,MAAA,wBAAe;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACzEJ,EAAA,CAAAC,cAAA,gBAAuB;UACrBD,EAAA,CAAAe,UAAA,MAAAmE,uCAAA,kBAQM;UACNlF,EAAA,CAAAe,UAAA,MAAAoE,uCAAA,kBAEM;UACRnF,EAAA,CAAAI,YAAA,EAAM;;;UAvN6DJ,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAyB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAkE,KAAA,8BAA0C;UAavGpF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,OAAAoE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAkE,KAAA,0BACF;UAMiFpF,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAyB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAmE,MAAA,wBAA0C;UAMzDrF,EAAA,CAAAO,SAAA,GAAgF;UAAhFP,EAAA,CAAAyB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAoE,UAAA,IAAAV,GAAA,CAAAjD,UAAA,CAAAiD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAoE,UAAA,yBAAgF;UAQ9ItF,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAY,UAAA,YAAAgE,GAAA,CAAAW,cAAA,GAA4B;UAChCvF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,MAAAoE,GAAA,CAAAY,gBAAA,QACF;UAuBExF,EAAA,CAAAO,SAAA,IACF;UADEP,EAAA,CAAAQ,kBAAA,OAAAoE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAuE,WAAA,uDACF;UAeIzF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAoB,kBAAA,QAAAwD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,kBAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAuE,MAAA,sBAAAd,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,kBAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAuE,MAAA,6BACF;UAIE1F,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAY,UAAA,UAAAgE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,kBAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAuE,MAAA,MAAkC;UA+BlC1F,EAAA,CAAAO,SAAA,GAAuD;UAAvDP,EAAA,CAAAY,UAAA,WAAAgE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,KAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAuE,MAAA,OAAuD;UAyBxD1F,EAAA,CAAAO,SAAA,IAA0D;UAA1DP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA2F,eAAA,KAAAC,GAAA,EAAAhB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA,EAA0D;UAU1DjF,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA6F,eAAA,KAAAC,GAAA,EAAyC,gBAAA9F,EAAA,CAAA2F,eAAA,KAAAI,GAAA,EAAAnB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA;UAiCKjF,EAAA,CAAAO,SAAA,IAA8E;UAA9EP,EAAA,CAAAoB,kBAAA,MAAAwD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,cAAAd,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,cAAA,WAA8E;UAI1HjG,EAAA,CAAAO,SAAA,GAAgG;UAAhGP,EAAA,CAAAkG,WAAA,YAAAtB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,YAAAd,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,cAAA,mBAAgG;UAOtDjG,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAyB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,OAA0C;UAI1C1F,EAAA,CAAAO,SAAA,GAA4E;UAA5EP,EAAA,CAAAyB,iBAAA,GAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,cAAA,YAAArB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,kBAAApB,GAAA,CAAA1D,MAAA,CAAA8E,eAAA,CAAAN,MAAA,QAA4E;UASjG1F,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAY,UAAA,aAAAgE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAiF,cAAA,KAAAnG,EAAA,CAAA6F,eAAA,KAAAO,GAAA,EAA+B;UASnDpG,EAAA,CAAAO,SAAA,GAAmE;UAAnEP,EAAA,CAAAY,UAAA,WAAAgE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAiF,cAAA,KAAAvB,GAAA,CAAA1D,MAAA,CAAAiF,cAAA,CAAAT,MAAA,OAAmE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}