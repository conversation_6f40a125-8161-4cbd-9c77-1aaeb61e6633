{"ast": null, "code": "import { catchError, finalize, takeUntil } from 'rxjs/operators';\nimport { Subject, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/rendus.service\";\nimport * as i2 from \"../../../../services/evaluation.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction EvaluationsListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵelement(2, \"div\", 22)(3, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 24);\n    i0.ɵɵtext(5, \"Chargement des \\u00E9valuations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EvaluationsListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 3)(2, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 27);\n    i0.ɵɵelement(4, \"path\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_26_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.loadEvaluations());\n    });\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction EvaluationsListComponent_div_27_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r9);\n  }\n}\nfunction EvaluationsListComponent_div_27_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r10._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r10.titre);\n  }\n}\nfunction EvaluationsListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 34);\n    i0.ɵɵelement(4, \"path\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h2\", 36);\n    i0.ɵɵtext(6, \"Filtres et recherche\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 37)(8, \"div\", 38)(9, \"label\", 39)(10, \"div\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 41);\n    i0.ɵɵelement(12, \"path\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Recherche\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"input\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_27_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.searchTerm = $event);\n    })(\"input\", function EvaluationsListComponent_div_27_Template_input_input_15_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 38)(17, \"label\", 39)(18, \"div\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 41);\n    i0.ɵɵelement(20, \"path\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \"Groupe\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"select\", 45);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_27_Template_select_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.filterGroupe = $event);\n    })(\"change\", function EvaluationsListComponent_div_27_Template_select_change_23_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.applyFilters());\n    });\n    i0.ɵɵelementStart(24, \"option\", 46);\n    i0.ɵɵtext(25, \"Tous les groupes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, EvaluationsListComponent_div_27_option_26_Template, 2, 2, \"option\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 38)(28, \"label\", 39)(29, \"div\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(30, \"svg\", 41);\n    i0.ɵɵelement(31, \"path\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33, \"Projet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"select\", 45);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_27_Template_select_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.filterProjet = $event);\n    })(\"change\", function EvaluationsListComponent_div_27_Template_select_change_34_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.applyFilters());\n    });\n    i0.ɵɵelementStart(35, \"option\", 46);\n    i0.ɵɵtext(36, \"Tous les projets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, EvaluationsListComponent_div_27_option_37_Template, 2, 2, \"option\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 38)(39, \"label\", 39)(40, \"div\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(41, \"svg\", 41);\n    i0.ɵɵelement(42, \"path\", 49)(43, \"path\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(44, \"span\");\n    i0.ɵɵtext(45, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 51)(47, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_27_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.resetFilters());\n    });\n    i0.ɵɵelementStart(48, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(49, \"svg\", 54);\n    i0.ɵɵelement(50, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(51, \"span\");\n    i0.ɵɵtext(52, \"R\\u00E9initialiser\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_27_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.updateMissingGroups());\n    });\n    i0.ɵɵelementStart(54, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(55, \"svg\", 54);\n    i0.ɵɵelement(56, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(57, \"span\");\n    i0.ɵɵtext(58, \"Maj groupes\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filterGroupe);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.groupes);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.filterProjet);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projets);\n  }\n}\nfunction EvaluationsListComponent_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"div\", 4)(3, \"div\", 21)(4, \"div\", 63);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 65);\n    i0.ɵɵelement(8, \"path\", 66);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"div\", 67)(10, \"h3\", 68);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 69);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 70)(15, \"div\", 71);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 41);\n    i0.ɵɵelement(17, \"path\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(18, \"span\", 72);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 71);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 41);\n    i0.ɵɵelement(22, \"path\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"span\", 72);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 73)(26, \"div\", 40)(27, \"div\", 74);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 75);\n    i0.ɵɵelement(29, \"path\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"div\")(31, \"p\", 77);\n    i0.ɵɵtext(32, \"\\u00C9valu\\u00E9e le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 78);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 40)(36, \"div\", 79);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(37, \"svg\", 54);\n    i0.ɵɵelement(38, \"path\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"div\")(40, \"p\", 77);\n    i0.ɵɵtext(41, \"Score total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 81);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 82)(45, \"p\", 83);\n    i0.ɵɵtext(46, \"D\\u00E9tail des scores\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 84)(48, \"div\", 85)(49, \"span\");\n    i0.ɵɵtext(50, \"Structure:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 29);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 85)(54, \"span\");\n    i0.ɵɵtext(55, \"Pratiques:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 29);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 85)(59, \"span\");\n    i0.ɵɵtext(60, \"Fonctionnalit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"span\", 29);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 85)(64, \"span\");\n    i0.ɵɵtext(65, \"Originalit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 29);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(68, \"div\", 86)(69, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_28_div_1_Template_button_click_69_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const evaluation_r21 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.viewEvaluationDetails(evaluation_r21.rendu));\n    });\n    i0.ɵɵelementStart(70, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(71, \"svg\", 88);\n    i0.ɵɵelement(72, \"path\", 50)(73, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(74, \"span\");\n    i0.ɵɵtext(75, \"Voir d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_28_div_1_Template_button_click_76_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const evaluation_r21 = restoredCtx.$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.editEvaluation(evaluation_r21.rendu));\n    });\n    i0.ɵɵelementStart(77, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(78, \"svg\", 88);\n    i0.ɵɵelement(79, \"path\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(80, \"span\");\n    i0.ɵɵtext(81, \"Modifier\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(82, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_28_div_1_Template_button_click_82_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r23);\n      const evaluation_r21 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.deleteEvaluation(evaluation_r21._id));\n    });\n    i0.ɵɵelementStart(83, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(84, \"svg\", 88);\n    i0.ɵɵelement(85, \"path\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(86, \"span\");\n    i0.ɵɵtext(87, \"Supprimer\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const evaluation_r21 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getStudentInitials(evaluation_r21.etudiant), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getStudentName(evaluation_r21.etudiant), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((evaluation_r21.etudiant == null ? null : evaluation_r21.etudiant.email) || \"Email non disponible\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getStudentGroup(evaluation_r21.etudiant), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getProjectTitle(evaluation_r21), \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r20.formatDate(evaluation_r21.dateEvaluation));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getScoreIconClass(ctx_r20.getScoreTotal(evaluation_r21)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r20.getScoreColorClass(ctx_r20.getScoreTotal(evaluation_r21)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.getScoreTotal(evaluation_r21), \"/20 \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((evaluation_r21.scores == null ? null : evaluation_r21.scores.structure) || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((evaluation_r21.scores == null ? null : evaluation_r21.scores.pratiques) || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((evaluation_r21.scores == null ? null : evaluation_r21.scores.fonctionnalite) || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((evaluation_r21.scores == null ? null : evaluation_r21.scores.originalite) || 0);\n  }\n}\nfunction EvaluationsListComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, EvaluationsListComponent_div_28_div_1_Template, 88, 13, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredEvaluations);\n  }\n}\nfunction EvaluationsListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95)(2, \"div\", 96);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 97);\n    i0.ɵɵelement(4, \"path\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 99);\n    i0.ɵɵtext(6, \"Aucune \\u00E9valuation trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 100);\n    i0.ɵɵtext(8, \"Aucune \\u00E9valuation ne correspond \\u00E0 vos crit\\u00E8res de filtrage actuels.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_29_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.resetFilters());\n    });\n    i0.ɵɵelementStart(10, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 54);\n    i0.ɵɵelement(12, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"R\\u00E9initialiser les filtres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class EvaluationsListComponent {\n  constructor(rendusService, evaluationService, router) {\n    this.rendusService = rendusService;\n    this.evaluationService = evaluationService;\n    this.router = router;\n    this.evaluations = [];\n    this.filteredEvaluations = [];\n    this.isLoading = true;\n    this.error = '';\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    this.groupes = [];\n    this.projets = [];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadEvaluations();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadEvaluations() {\n    this.isLoading = true;\n    this.error = '';\n    console.log('Début du chargement des évaluations...');\n    this.evaluationService.getAllEvaluations().pipe(takeUntil(this.destroy$), catchError(err => {\n      console.error('Erreur lors du chargement des évaluations:', err);\n      this.error = 'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\n      this.isLoading = false;\n      return of([]);\n    }), finalize(() => {\n      console.log('Finalisation du chargement des évaluations');\n      this.isLoading = false;\n    })).subscribe({\n      next: evaluations => {\n        console.log('Évaluations reçues:', evaluations);\n        if (!Array.isArray(evaluations)) {\n          console.error('Les données reçues ne sont pas un tableau:', evaluations);\n          this.error = 'Format de données incorrect. Veuillez réessayer plus tard.';\n          return;\n        }\n        // Vérifier et compléter les données manquantes\n        this.evaluations = evaluations.map(evaluation => {\n          const evalWithDetails = evaluation;\n          // Vérifier si les détails du projet sont disponibles\n          if (!evalWithDetails.projetDetails || !evalWithDetails.projetDetails.titre) {\n            console.warn('Détails du projet manquants pour l\\'évaluation:', evalWithDetails._id);\n            // Si le rendu contient des détails de projet, les utiliser\n            if (evalWithDetails.renduDetails && evalWithDetails.renduDetails.projet) {\n              evalWithDetails.projetDetails = evalWithDetails.renduDetails.projet;\n            }\n          }\n          return evalWithDetails;\n        });\n        this.extractGroupesAndProjets();\n        this.applyFilters();\n        // Ajouter cette ligne pour déboguer\n        this.debugEtudiantData();\n      }\n    });\n  }\n  extractGroupesAndProjets() {\n    // Extraire les groupes uniques\n    const groupesSet = new Set();\n    this.evaluations.forEach(evaluation => {\n      const groupe = evaluation.etudiant?.groupe;\n      if (groupe && groupe.trim() !== '') {\n        groupesSet.add(groupe);\n        console.log(`Groupe trouvé: ${groupe}`);\n      } else {\n        console.log(`Évaluation sans groupe: ${evaluation._id}`);\n      }\n    });\n    this.groupes = Array.from(groupesSet).sort();\n    console.log('Groupes extraits:', this.groupes);\n    // Extraire les projets uniques\n    const projetsMap = new Map();\n    this.evaluations.forEach(evaluation => {\n      if (evaluation.projetDetails && evaluation.projetDetails._id) {\n        projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\n      }\n    });\n    this.projets = Array.from(projetsMap.values());\n  }\n  applyFilters() {\n    let results = this.evaluations;\n    // Filtre par terme de recherche\n    if (this.searchTerm.trim() !== '') {\n      const term = this.searchTerm.toLowerCase().trim();\n      results = results.filter(evaluation => evaluation.etudiant?.nom?.toLowerCase().includes(term) || evaluation.etudiant?.prenom?.toLowerCase().includes(term) || evaluation.projetDetails?.titre?.toLowerCase().includes(term));\n    }\n    // Filtre par groupe\n    if (this.filterGroupe) {\n      results = results.filter(evaluation => evaluation.etudiant?.groupe === this.filterGroupe);\n    }\n    // Filtre par projet\n    if (this.filterProjet) {\n      results = results.filter(evaluation => evaluation.projetDetails?._id === this.filterProjet);\n    }\n    this.filteredEvaluations = results;\n  }\n  onSearchChange() {\n    this.applyFilters();\n  }\n  resetFilters() {\n    this.searchTerm = '';\n    this.filterGroupe = '';\n    this.filterProjet = '';\n    this.applyFilters();\n  }\n  editEvaluation(renduId) {\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n  }\n  viewEvaluationDetails(renduId) {\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n  }\n  getScoreTotal(evaluation) {\n    if (!evaluation.scores) return 0;\n    const scores = evaluation.scores;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreClass(score) {\n    if (score >= 16) return 'text-green-600 bg-green-100';\n    if (score >= 12) return 'text-blue-600 bg-blue-100';\n    if (score >= 8) return 'text-yellow-600 bg-yellow-100';\n    return 'text-red-600 bg-red-100';\n  }\n  formatDate(date) {\n    if (!date) return 'Non disponible';\n    return new Date(date).toLocaleDateString();\n  }\n  // Ajouter cette fonction pour déboguer les données d'étudiant\n  debugEtudiantData() {\n    console.group('Débogage données étudiants');\n    this.evaluations.forEach(evaluation => {\n      console.log(`Évaluation ${evaluation._id}:`);\n      console.log('- Étudiant:', evaluation.etudiant);\n      if (evaluation.etudiant) {\n        console.log('- Nom:', evaluation.etudiant.nom);\n        console.log('- Prénom:', evaluation.etudiant.prenom);\n        console.log('- Groupe:', evaluation.etudiant.groupe);\n      }\n      console.log('- Rendu:', evaluation.renduDetails);\n    });\n    console.groupEnd();\n  }\n  // Ajouter cette méthode pour mettre à jour les groupes manquants\n  updateMissingGroups() {\n    if (!confirm('Voulez-vous mettre à jour les groupes manquants des étudiants?')) {\n      return;\n    }\n    this.isLoading = true;\n    this.evaluationService.updateMissingGroups().subscribe({\n      next: response => {\n        console.log('Mise à jour des groupes:', response);\n        alert(`${response.updatedCount} étudiants mis à jour avec leur groupe.`);\n        this.loadEvaluations(); // Recharger les données\n      },\n\n      error: err => {\n        console.error('Erreur lors de la mise à jour des groupes:', err);\n        alert('Erreur lors de la mise à jour des groupes.');\n        this.isLoading = false;\n      }\n    });\n  }\n  // Nouvelles méthodes pour le design moderne\n  getStudentInitials(etudiant) {\n    if (!etudiant) return '??';\n    // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n    const firstName = etudiant.firstName || '';\n    const lastName = etudiant.lastName || '';\n    if (firstName && lastName && lastName.trim()) {\n      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n    }\n    // Priorité 2: fullName (diviser en mots)\n    const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n    if (fullName && fullName.trim()) {\n      const parts = fullName.trim().split(' ');\n      if (parts.length >= 2) {\n        return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n      } else {\n        // Si un seul mot, prendre les 2 premières lettres\n        return fullName.substring(0, 2).toUpperCase();\n      }\n    }\n    // Priorité 3: firstName seul (prendre les 2 premières lettres)\n    if (firstName && firstName.trim()) {\n      return firstName.substring(0, 2).toUpperCase();\n    }\n    return '??';\n  }\n  getStudentName(etudiant) {\n    if (!etudiant) return 'Utilisateur inconnu';\n    // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n    const firstName = etudiant.firstName || '';\n    const lastName = etudiant.lastName || '';\n    if (firstName && lastName && lastName.trim()) {\n      return `${firstName} ${lastName}`.trim();\n    }\n    // Priorité 2: fullName\n    const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n    if (fullName && fullName.trim()) {\n      return fullName.trim();\n    }\n    // Priorité 3: firstName seul\n    if (firstName && firstName.trim()) {\n      return firstName.trim();\n    }\n    // Priorité 4: email comme fallback\n    if (etudiant.email) {\n      return etudiant.email;\n    }\n    return 'Utilisateur inconnu';\n  }\n  getStudentGroup(etudiant) {\n    if (!etudiant) return 'Non spécifié';\n    // Si group est un objet (référence populée avec le modèle Group)\n    if (etudiant.group && typeof etudiant.group === 'object' && etudiant.group.name) {\n      return etudiant.group.name;\n    }\n    // Si group est une chaîne directe (valeur ajoutée manuellement)\n    if (etudiant.group && typeof etudiant.group === 'string' && etudiant.group.trim()) {\n      return etudiant.group.trim();\n    }\n    // Fallback vers d'autres champs possibles\n    if (etudiant.groupe && typeof etudiant.groupe === 'string' && etudiant.groupe.trim()) {\n      return etudiant.groupe.trim();\n    }\n    if (etudiant.groupName && typeof etudiant.groupName === 'string' && etudiant.groupName.trim()) {\n      return etudiant.groupName.trim();\n    }\n    if (etudiant.department && typeof etudiant.department === 'string' && etudiant.department.trim()) {\n      return etudiant.department.trim();\n    }\n    return 'Non spécifié';\n  }\n  getProjectTitle(evaluation) {\n    return evaluation.projetDetails?.titre || evaluation.renduDetails?.projet?.titre || 'Projet inconnu';\n  }\n  getAverageScore() {\n    if (this.evaluations.length === 0) return '0';\n    const totalScore = this.evaluations.reduce((sum, evaluation) => {\n      return sum + this.getScoreTotal(evaluation);\n    }, 0);\n    const average = totalScore / this.evaluations.length;\n    return average.toFixed(1);\n  }\n  getScoreIconClass(score) {\n    if (score >= 16) return 'bg-success/10 dark:bg-dark-accent-secondary/10 text-success dark:text-dark-accent-secondary';\n    if (score >= 12) return 'bg-info/10 dark:bg-dark-accent-primary/10 text-info dark:text-dark-accent-primary';\n    if (score >= 8) return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning';\n    return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark';\n  }\n  getScoreColorClass(score) {\n    if (score >= 16) return 'text-success dark:text-dark-accent-secondary';\n    if (score >= 12) return 'text-info dark:text-dark-accent-primary';\n    if (score >= 8) return 'text-warning dark:text-warning';\n    return 'text-danger dark:text-danger-dark';\n  }\n  // Méthode pour supprimer une évaluation\n  deleteEvaluation(evaluationId) {\n    if (!confirm('Êtes-vous sûr de vouloir supprimer cette évaluation ? Cette action est irréversible.')) {\n      return;\n    }\n    this.evaluationService.deleteEvaluation(evaluationId).subscribe({\n      next: () => {\n        alert('Évaluation supprimée avec succès !');\n        this.loadEvaluations(); // Recharger la liste\n      },\n\n      error: err => {\n        console.error('Erreur lors de la suppression:', err);\n        alert('Erreur lors de la suppression de l\\'évaluation.');\n      }\n    });\n  }\n  static {\n    this.ɵfac = function EvaluationsListComponent_Factory(t) {\n      return new (t || EvaluationsListComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.EvaluationService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EvaluationsListComponent,\n      selectors: [[\"app-evaluations-list\"]],\n      decls: 30,\n      vars: 7,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"space-x-4\", \"text-white/80\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\"], [1, \"text-sm\"], [1, \"w-px\", \"h-12\", \"bg-white/20\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-6 mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\", 4, \"ngIf\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-0\"], [1, \"mt-4\", \"text-text\", \"dark:text-dark-text-secondary\", \"animate-pulse\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"border\", \"border-danger/30\", \"dark:border-danger-dark/40\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-xl\", \"p-6\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-danger\", \"dark:text-danger-dark\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"font-medium\"], [1, \"px-4\", \"py-2\", \"bg-danger/20\", \"dark:bg-danger-dark/20\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-lg\", \"hover:bg-danger/30\", \"dark:hover:bg-danger-dark/30\", \"transition-colors\", \"font-medium\", 3, \"click\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"mb-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-4\", \"gap-6\"], [1, \"space-y-2\"], [1, \"block\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"type\", \"text\", \"placeholder\", \"Nom, pr\\u00E9nom, projet...\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"flex-col\", \"space-y-2\"], [1, \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [1, \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-info\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12\"], [3, \"value\"], [1, \"space-y-6\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"group\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\", \"space-y-4\", \"lg:space-y-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-lg\", \"font-bold\", \"shadow-lg\"], [1, \"absolute\", \"-bottom-1\", \"-right-1\", \"w-6\", \"h-6\", \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"flex-1\"], [1, \"text-lg\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"sm:items-center\", \"space-y-3\", \"sm:space-y-0\", \"sm:space-x-6\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"p-2\", \"rounded-lg\", 3, \"ngClass\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-lg\", \"font-bold\", 3, \"ngClass\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-lg\", \"p-3\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-1\"], [1, \"grid\", \"grid-cols-2\", \"gap-1\", \"text-xs\"], [1, \"flex\", \"justify-between\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-2\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-info\", \"to-primary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover/btn:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"text-center\", \"py-16\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-12\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"max-w-md\", \"mx-auto\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-secondary/10\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-secondary/20\", \"rounded-2xl\", \"p-6\", \"mb-6\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"]],\n      template: function EvaluationsListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 6);\n          i0.ɵɵelement(7, \"path\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n          i0.ɵɵtext(10, \"Liste des \\u00C9valuations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12, \"Gestion et suivi des \\u00E9valuations de projets\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵtext(18, \"Total\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(19, \"div\", 14);\n          i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 13);\n          i0.ɵɵtext(24, \"Moyenne\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(25, EvaluationsListComponent_div_25_Template, 6, 0, \"div\", 15);\n          i0.ɵɵtemplate(26, EvaluationsListComponent_div_26_Template, 9, 1, \"div\", 16);\n          i0.ɵɵtemplate(27, EvaluationsListComponent_div_27_Template, 59, 5, \"div\", 17);\n          i0.ɵɵtemplate(28, EvaluationsListComponent_div_28_Template, 2, 1, \"div\", 18);\n          i0.ɵɵtemplate(29, EvaluationsListComponent_div_29_Template, 15, 0, \"div\", 19);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtextInterpolate(ctx.evaluations.length);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.getAverageScore());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".evaluations-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  padding: 15px;\\n  background-color: #f5f5f5;\\n  border-radius: 5px;\\n}\\n\\n.filter-item[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin-top: 20px;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .evaluations-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  text-align: left;\\n  border-bottom: 1px solid #ddd;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f2f2f2;\\n  font-weight: bold;\\n}\\n\\n.evaluations-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.actions-cell[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 50px 0;\\n}\\n\\n.no-evaluations[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 50px 0;\\n  color: #666;\\n}\\n\\n.score-badge[_ngcontent-%COMP%] {\\n  padding: 5px 10px;\\n  border-radius: 15px;\\n  font-weight: bold;\\n  display: inline-block;\\n}\\n\\n.score-high[_ngcontent-%COMP%] {\\n  background-color: #c8e6c9;\\n  color: #2e7d32;\\n}\\n\\n.score-medium[_ngcontent-%COMP%] {\\n  background-color: #fff9c4;\\n  color: #f57f17;\\n}\\n\\n.score-low[_ngcontent-%COMP%] {\\n  background-color: #ffcdd2;\\n  color: #c62828;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["catchError", "finalize", "takeUntil", "Subject", "of", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵlistener", "EvaluationsListComponent_div_26_Template_button_click_7_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "loadEvaluations", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵproperty", "groupe_r9", "projet_r10", "_id", "titre", "EvaluationsListComponent_div_27_Template_input_ngModelChange_15_listener", "$event", "_r12", "ctx_r11", "searchTerm", "EvaluationsListComponent_div_27_Template_input_input_15_listener", "ctx_r13", "onSearchChange", "EvaluationsListComponent_div_27_Template_select_ngModelChange_23_listener", "ctx_r14", "filterGroupe", "EvaluationsListComponent_div_27_Template_select_change_23_listener", "ctx_r15", "applyFilters", "ɵɵtemplate", "EvaluationsListComponent_div_27_option_26_Template", "EvaluationsListComponent_div_27_Template_select_ngModelChange_34_listener", "ctx_r16", "filterProjet", "EvaluationsListComponent_div_27_Template_select_change_34_listener", "ctx_r17", "EvaluationsListComponent_div_27_option_37_Template", "EvaluationsListComponent_div_27_Template_button_click_47_listener", "ctx_r18", "resetFilters", "EvaluationsListComponent_div_27_Template_button_click_53_listener", "ctx_r19", "updateMissingGroups", "ctx_r2", "groupes", "projets", "EvaluationsListComponent_div_28_div_1_Template_button_click_69_listener", "restoredCtx", "_r23", "evaluation_r21", "$implicit", "ctx_r22", "viewEvaluationDetails", "rendu", "EvaluationsListComponent_div_28_div_1_Template_button_click_76_listener", "ctx_r24", "editEvaluation", "EvaluationsListComponent_div_28_div_1_Template_button_click_82_listener", "ctx_r25", "deleteEvaluation", "ɵɵtextInterpolate1", "ctx_r20", "getStudentInitials", "etudiant", "getStudentName", "email", "getStudentGroup", "getProjectTitle", "formatDate", "dateEvaluation", "getScoreIconClass", "getScoreTotal", "getScoreColorClass", "scores", "structure", "pratiques", "fonctionnalite", "originalite", "EvaluationsListComponent_div_28_div_1_Template", "ctx_r3", "filteredEvaluations", "EvaluationsListComponent_div_29_Template_button_click_9_listener", "_r27", "ctx_r26", "EvaluationsListComponent", "constructor", "rendusService", "evaluationService", "router", "evaluations", "isLoading", "destroy$", "ngOnInit", "ngOnDestroy", "next", "complete", "console", "log", "getAllEvaluations", "pipe", "err", "subscribe", "Array", "isArray", "map", "evaluation", "evalWithDetails", "projetDetails", "warn", "renduDetails", "projet", "extractGroupesAndProjets", "debugEtudiantData", "groupesSet", "Set", "for<PERSON>ach", "groupe", "trim", "add", "from", "sort", "projetsMap", "Map", "set", "values", "results", "term", "toLowerCase", "filter", "nom", "includes", "prenom", "renduId", "navigate", "getScoreClass", "score", "date", "Date", "toLocaleDateString", "group", "groupEnd", "confirm", "response", "alert", "updatedCount", "firstName", "lastName", "char<PERSON>t", "toUpperCase", "fullName", "name", "username", "parts", "split", "length", "substring", "groupName", "department", "getAverageScore", "totalScore", "reduce", "sum", "average", "toFixed", "evaluationId", "ɵɵdirectiveInject", "i1", "RendusService", "i2", "EvaluationService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "EvaluationsListComponent_Template", "rf", "ctx", "EvaluationsListComponent_div_25_Template", "EvaluationsListComponent_div_26_Template", "EvaluationsListComponent_div_27_Template", "EvaluationsListComponent_div_28_Template", "EvaluationsListComponent_div_29_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluations-list\\evaluations-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\evaluations-list\\evaluations-list.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { RendusService } from '../../../../services/rendus.service';\r\nimport { EvaluationService } from '../../../../services/evaluation.service';\r\nimport { catchError, finalize, takeUntil } from 'rxjs/operators';\r\nimport { Subject, of } from 'rxjs';\r\nimport { Evaluation } from '../../../../models/evaluation';\r\nimport { Rendu } from '../../../../models/rendu';\r\n\r\n// Interface pour les évaluations avec détails\r\ninterface EvaluationWithDetails extends Evaluation {\r\n  renduDetails?: Rendu;\r\n  etudiant?: any;\r\n  projetDetails?: any;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-evaluations-list',\r\n  templateUrl: './evaluations-list.component.html',\r\n  styleUrls: ['./evaluations-list.component.css']\r\n})\r\nexport class EvaluationsListComponent implements OnInit, OnDestroy {\r\n  evaluations: EvaluationWithDetails[] = [];\r\n  filteredEvaluations: EvaluationWithDetails[] = [];\r\n  isLoading: boolean = true;\r\n  error: string = '';\r\n  searchTerm: string = '';\r\n  filterGroupe: string = '';\r\n  filterProjet: string = '';\r\n  groupes: string[] = [];\r\n  projets: any[] = [];\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private rendusService: RendusService,\r\n    private evaluationService: EvaluationService,\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loadEvaluations();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  loadEvaluations(): void {\r\n    this.isLoading = true;\r\n    this.error = '';\r\n\r\n    console.log('Début du chargement des évaluations...');\r\n\r\n    this.evaluationService.getAllEvaluations()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        catchError(err => {\r\n          console.error('Erreur lors du chargement des évaluations:', err);\r\n          this.error = 'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\r\n          this.isLoading = false;\r\n          return of([]);\r\n        }),\r\n        finalize(() => {\r\n          console.log('Finalisation du chargement des évaluations');\r\n          this.isLoading = false;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (evaluations) => {\r\n          console.log('Évaluations reçues:', evaluations);\r\n\r\n          if (!Array.isArray(evaluations)) {\r\n            console.error('Les données reçues ne sont pas un tableau:', evaluations);\r\n            this.error = 'Format de données incorrect. Veuillez réessayer plus tard.';\r\n            return;\r\n          }\r\n\r\n          // Vérifier et compléter les données manquantes\r\n          this.evaluations = evaluations.map(evaluation => {\r\n            const evalWithDetails = evaluation as EvaluationWithDetails;\r\n\r\n            // Vérifier si les détails du projet sont disponibles\r\n            if (!evalWithDetails.projetDetails || !evalWithDetails.projetDetails.titre) {\r\n              console.warn('Détails du projet manquants pour l\\'évaluation:', evalWithDetails._id);\r\n\r\n              // Si le rendu contient des détails de projet, les utiliser\r\n              if (evalWithDetails.renduDetails && evalWithDetails.renduDetails.projet) {\r\n                evalWithDetails.projetDetails = evalWithDetails.renduDetails.projet;\r\n              }\r\n            }\r\n\r\n            return evalWithDetails;\r\n          });\r\n\r\n          this.extractGroupesAndProjets();\r\n          this.applyFilters();\r\n\r\n          // Ajouter cette ligne pour déboguer\r\n          this.debugEtudiantData();\r\n        }\r\n      });\r\n  }\r\n\r\n  extractGroupesAndProjets(): void {\r\n    // Extraire les groupes uniques\r\n    const groupesSet = new Set<string>();\r\n\r\n    this.evaluations.forEach(evaluation => {\r\n      const groupe = evaluation.etudiant?.groupe;\r\n      if (groupe && groupe.trim() !== '') {\r\n        groupesSet.add(groupe);\r\n        console.log(`Groupe trouvé: ${groupe}`);\r\n      } else {\r\n        console.log(`Évaluation sans groupe: ${evaluation._id}`);\r\n      }\r\n    });\r\n\r\n    this.groupes = Array.from(groupesSet).sort();\r\n    console.log('Groupes extraits:', this.groupes);\r\n\r\n    // Extraire les projets uniques\r\n    const projetsMap = new Map<string, any>();\r\n    this.evaluations.forEach(evaluation => {\r\n      if (evaluation.projetDetails && evaluation.projetDetails._id) {\r\n        projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\r\n      }\r\n    });\r\n    this.projets = Array.from(projetsMap.values());\r\n  }\r\n\r\n  applyFilters(): void {\r\n    let results = this.evaluations;\r\n\r\n    // Filtre par terme de recherche\r\n    if (this.searchTerm.trim() !== '') {\r\n      const term = this.searchTerm.toLowerCase().trim();\r\n      results = results.filter(evaluation =>\r\n        (evaluation.etudiant?.nom?.toLowerCase().includes(term) ||\r\n         evaluation.etudiant?.prenom?.toLowerCase().includes(term) ||\r\n         evaluation.projetDetails?.titre?.toLowerCase().includes(term))\r\n      );\r\n    }\r\n\r\n    // Filtre par groupe\r\n    if (this.filterGroupe) {\r\n      results = results.filter(evaluation => evaluation.etudiant?.groupe === this.filterGroupe);\r\n    }\r\n\r\n    // Filtre par projet\r\n    if (this.filterProjet) {\r\n      results = results.filter(evaluation => evaluation.projetDetails?._id === this.filterProjet);\r\n    }\r\n\r\n    this.filteredEvaluations = results;\r\n  }\r\n\r\n  onSearchChange(): void {\r\n    this.applyFilters();\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.searchTerm = '';\r\n    this.filterGroupe = '';\r\n    this.filterProjet = '';\r\n    this.applyFilters();\r\n  }\r\n\r\n  editEvaluation(renduId: string): void {\r\n    this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\r\n  }\r\n\r\n  viewEvaluationDetails(renduId: string): void {\r\n    this.router.navigate(['/admin/projects/evaluation-details', renduId]);\r\n  }\r\n\r\n  getScoreTotal(evaluation: EvaluationWithDetails): number {\r\n    if (!evaluation.scores) return 0;\r\n\r\n    const scores = evaluation.scores;\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreClass(score: number): string {\r\n    if (score >= 16) return 'text-green-600 bg-green-100';\r\n    if (score >= 12) return 'text-blue-600 bg-blue-100';\r\n    if (score >= 8) return 'text-yellow-600 bg-yellow-100';\r\n    return 'text-red-600 bg-red-100';\r\n  }\r\n\r\n  formatDate(date: string | Date | undefined): string {\r\n    if (!date) return 'Non disponible';\r\n    return new Date(date).toLocaleDateString();\r\n  }\r\n\r\n  // Ajouter cette fonction pour déboguer les données d'étudiant\r\n  debugEtudiantData(): void {\r\n    console.group('Débogage données étudiants');\r\n\r\n    this.evaluations.forEach(evaluation => {\r\n      console.log(`Évaluation ${evaluation._id}:`);\r\n      console.log('- Étudiant:', evaluation.etudiant);\r\n      if (evaluation.etudiant) {\r\n\r\n        console.log('- Nom:', evaluation.etudiant.nom);\r\n        console.log('- Prénom:', evaluation.etudiant.prenom);\r\n        console.log('- Groupe:', evaluation.etudiant.groupe);\r\n      }\r\n      console.log('- Rendu:', evaluation.renduDetails);\r\n    });\r\n\r\n    console.groupEnd();\r\n  }\r\n\r\n  // Ajouter cette méthode pour mettre à jour les groupes manquants\r\n  updateMissingGroups(): void {\r\n    if (!confirm('Voulez-vous mettre à jour les groupes manquants des étudiants?')) {\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n\r\n    this.evaluationService.updateMissingGroups().subscribe({\r\n      next: (response) => {\r\n        console.log('Mise à jour des groupes:', response);\r\n        alert(`${response.updatedCount} étudiants mis à jour avec leur groupe.`);\r\n        this.loadEvaluations(); // Recharger les données\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors de la mise à jour des groupes:', err);\r\n        alert('Erreur lors de la mise à jour des groupes.');\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // Nouvelles méthodes pour le design moderne\r\n  getStudentInitials(etudiant: any): string {\r\n    if (!etudiant) return '??';\r\n\r\n    // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\r\n    const firstName = etudiant.firstName || '';\r\n    const lastName = etudiant.lastName || '';\r\n\r\n    if (firstName && lastName && lastName.trim()) {\r\n      return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\r\n    }\r\n\r\n    // Priorité 2: fullName (diviser en mots)\r\n    const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\r\n    if (fullName && fullName.trim()) {\r\n      const parts = fullName.trim().split(' ');\r\n      if (parts.length >= 2) {\r\n        return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\r\n      } else {\r\n        // Si un seul mot, prendre les 2 premières lettres\r\n        return fullName.substring(0, 2).toUpperCase();\r\n      }\r\n    }\r\n\r\n    // Priorité 3: firstName seul (prendre les 2 premières lettres)\r\n    if (firstName && firstName.trim()) {\r\n      return firstName.substring(0, 2).toUpperCase();\r\n    }\r\n\r\n    return '??';\r\n  }\r\n\r\n  getStudentName(etudiant: any): string {\r\n    if (!etudiant) return 'Utilisateur inconnu';\r\n\r\n    // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\r\n    const firstName = etudiant.firstName || '';\r\n    const lastName = etudiant.lastName || '';\r\n\r\n    if (firstName && lastName && lastName.trim()) {\r\n      return `${firstName} ${lastName}`.trim();\r\n    }\r\n\r\n    // Priorité 2: fullName\r\n    const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\r\n    if (fullName && fullName.trim()) {\r\n      return fullName.trim();\r\n    }\r\n\r\n    // Priorité 3: firstName seul\r\n    if (firstName && firstName.trim()) {\r\n      return firstName.trim();\r\n    }\r\n\r\n    // Priorité 4: email comme fallback\r\n    if (etudiant.email) {\r\n      return etudiant.email;\r\n    }\r\n\r\n    return 'Utilisateur inconnu';\r\n  }\r\n\r\n  getStudentGroup(etudiant: any): string {\r\n    if (!etudiant) return 'Non spécifié';\r\n\r\n    // Si group est un objet (référence populée avec le modèle Group)\r\n    if (etudiant.group && typeof etudiant.group === 'object' && etudiant.group.name) {\r\n      return etudiant.group.name;\r\n    }\r\n\r\n    // Si group est une chaîne directe (valeur ajoutée manuellement)\r\n    if (etudiant.group && typeof etudiant.group === 'string' && etudiant.group.trim()) {\r\n      return etudiant.group.trim();\r\n    }\r\n\r\n    // Fallback vers d'autres champs possibles\r\n    if (etudiant.groupe && typeof etudiant.groupe === 'string' && etudiant.groupe.trim()) {\r\n      return etudiant.groupe.trim();\r\n    }\r\n\r\n    if (etudiant.groupName && typeof etudiant.groupName === 'string' && etudiant.groupName.trim()) {\r\n      return etudiant.groupName.trim();\r\n    }\r\n\r\n    if (etudiant.department && typeof etudiant.department === 'string' && etudiant.department.trim()) {\r\n      return etudiant.department.trim();\r\n    }\r\n\r\n    return 'Non spécifié';\r\n  }\r\n\r\n  getProjectTitle(evaluation: EvaluationWithDetails): string {\r\n    return evaluation.projetDetails?.titre ||\r\n           evaluation.renduDetails?.projet?.titre ||\r\n           'Projet inconnu';\r\n  }\r\n\r\n  getAverageScore(): string {\r\n    if (this.evaluations.length === 0) return '0';\r\n\r\n    const totalScore = this.evaluations.reduce((sum, evaluation) => {\r\n      return sum + this.getScoreTotal(evaluation);\r\n    }, 0);\r\n\r\n    const average = totalScore / this.evaluations.length;\r\n    return average.toFixed(1);\r\n  }\r\n\r\n  getScoreIconClass(score: number): string {\r\n    if (score >= 16) return 'bg-success/10 dark:bg-dark-accent-secondary/10 text-success dark:text-dark-accent-secondary';\r\n    if (score >= 12) return 'bg-info/10 dark:bg-dark-accent-primary/10 text-info dark:text-dark-accent-primary';\r\n    if (score >= 8) return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning';\r\n    return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark';\r\n  }\r\n\r\n  getScoreColorClass(score: number): string {\r\n    if (score >= 16) return 'text-success dark:text-dark-accent-secondary';\r\n    if (score >= 12) return 'text-info dark:text-dark-accent-primary';\r\n    if (score >= 8) return 'text-warning dark:text-warning';\r\n    return 'text-danger dark:text-danger-dark';\r\n  }\r\n\r\n  // Méthode pour supprimer une évaluation\r\n  deleteEvaluation(evaluationId: string): void {\r\n    if (!confirm('Êtes-vous sûr de vouloir supprimer cette évaluation ? Cette action est irréversible.')) {\r\n      return;\r\n    }\r\n\r\n    this.evaluationService.deleteEvaluation(evaluationId).subscribe({\r\n      next: () => {\r\n        alert('Évaluation supprimée avec succès !');\r\n        this.loadEvaluations(); // Recharger la liste\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors de la suppression:', err);\r\n        alert('Erreur lors de la suppression de l\\'évaluation.');\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n    <!-- Header avec gradient -->\r\n    <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm\">\r\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"></path>\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h1 class=\"text-3xl font-bold text-white mb-2\">Liste des Évaluations</h1>\r\n            <p class=\"text-white/80\">Gestion et suivi des évaluations de projets</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"hidden md:flex items-center space-x-4 text-white/80\">\r\n          <div class=\"text-center\">\r\n            <div class=\"text-2xl font-bold\">{{ evaluations.length }}</div>\r\n            <div class=\"text-sm\">Total</div>\r\n          </div>\r\n          <div class=\"w-px h-12 bg-white/20\"></div>\r\n          <div class=\"text-center\">\r\n            <div class=\"text-2xl font-bold\">{{ getAverageScore() }}</div>\r\n            <div class=\"text-sm\">Moyenne</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading State -->\r\n    <div *ngIf=\"isLoading\" class=\"flex flex-col items-center justify-center py-16\">\r\n      <div class=\"relative\">\r\n        <div class=\"animate-spin rounded-full h-16 w-16 border-4 border-primary/30 dark:border-dark-accent-primary/30\"></div>\r\n        <div class=\"animate-spin rounded-full h-16 w-16 border-4 border-transparent border-t-primary dark:border-t-dark-accent-primary absolute top-0 left-0\"></div>\r\n      </div>\r\n      <p class=\"mt-4 text-text dark:text-dark-text-secondary animate-pulse\">Chargement des évaluations...</p>\r\n    </div>\r\n\r\n    <!-- Error State -->\r\n    <div *ngIf=\"error\" class=\"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-6 mb-6 backdrop-blur-sm\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-3\">\r\n          <svg class=\"w-5 h-5 text-danger dark:text-danger-dark flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <p class=\"font-medium\">{{ error }}</p>\r\n        </div>\r\n        <button (click)=\"loadEvaluations()\"\r\n          class=\"px-4 py-2 bg-danger/20 dark:bg-danger-dark/20 text-danger dark:text-danger-dark rounded-lg hover:bg-danger/30 dark:hover:bg-danger-dark/30 transition-colors font-medium\">\r\n          Réessayer\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Filtres modernes -->\r\n    <div *ngIf=\"!isLoading && !error\" class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n      <div class=\"flex items-center space-x-3 mb-6\">\r\n        <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg\">\r\n          <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"></path>\r\n          </svg>\r\n        </div>\r\n        <h2 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary\">Filtres et recherche</h2>\r\n      </div>\r\n\r\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n        <!-- Recherche -->\r\n        <div class=\"space-y-2\">\r\n          <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\r\n              </svg>\r\n              <span>Recherche</span>\r\n            </div>\r\n          </label>\r\n          <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange()\"\r\n            class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary\"\r\n            placeholder=\"Nom, prénom, projet...\">\r\n        </div>\r\n\r\n        <!-- Filtre par groupe -->\r\n        <div class=\"space-y-2\">\r\n          <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n              </svg>\r\n              <span>Groupe</span>\r\n            </div>\r\n          </label>\r\n          <select [(ngModel)]=\"filterGroupe\" (change)=\"applyFilters()\"\r\n            class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary\">\r\n            <option value=\"\">Tous les groupes</option>\r\n            <option *ngFor=\"let groupe of groupes\" [value]=\"groupe\">{{ groupe }}</option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- Filtre par projet -->\r\n        <div class=\"space-y-2\">\r\n          <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n              <span>Projet</span>\r\n            </div>\r\n          </label>\r\n          <select [(ngModel)]=\"filterProjet\" (change)=\"applyFilters()\"\r\n            class=\"w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary\">\r\n            <option value=\"\">Tous les projets</option>\r\n            <option *ngFor=\"let projet of projets\" [value]=\"projet._id\">{{ projet.titre }}</option>\r\n          </select>\r\n        </div>\r\n\r\n        <!-- Actions -->\r\n        <div class=\"space-y-2\">\r\n          <label class=\"block text-sm font-semibold text-text-dark dark:text-dark-text-primary\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\r\n              </svg>\r\n              <span>Actions</span>\r\n            </div>\r\n          </label>\r\n          <div class=\"flex flex-col space-y-2\">\r\n            <button (click)=\"resetFilters()\"\r\n              class=\"px-4 py-3 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\r\n                </svg>\r\n                <span>Réinitialiser</span>\r\n              </div>\r\n            </button>\r\n            <button (click)=\"updateMissingGroups()\"\r\n              class=\"px-4 py-3 bg-gradient-to-r from-info to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12\"></path>\r\n                </svg>\r\n                <span>Maj groupes</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Liste des évaluations en cartes modernes -->\r\n    <div *ngIf=\"!isLoading && !error && filteredEvaluations.length > 0\" class=\"space-y-6\">\r\n      <div *ngFor=\"let evaluation of filteredEvaluations\" class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group\">\r\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\r\n\r\n          <!-- Informations étudiant -->\r\n          <div class=\"flex items-center space-x-4\">\r\n            <div class=\"relative\">\r\n              <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center text-white text-lg font-bold shadow-lg\">\r\n                {{ getStudentInitials(evaluation.etudiant) }}\r\n              </div>\r\n              <div class=\"absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary rounded-full flex items-center justify-center\">\r\n                <svg class=\"w-3 h-3 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n              </div>\r\n            </div>\r\n            <div class=\"flex-1\">\r\n              <h3 class=\"text-lg font-bold text-text-dark dark:text-dark-text-primary\">\r\n                {{ getStudentName(evaluation.etudiant) }}\r\n              </h3>\r\n              <p class=\"text-sm text-text dark:text-dark-text-secondary\">{{ evaluation.etudiant?.email || 'Email non disponible' }}</p>\r\n              <div class=\"flex items-center space-x-4 mt-2\">\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">\r\n                    {{ getStudentGroup(evaluation.etudiant) }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">\r\n                    {{ getProjectTitle(evaluation) }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Informations de l'évaluation -->\r\n          <div class=\"flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-6\">\r\n            <!-- Date d'évaluation -->\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div class=\"bg-info/10 dark:bg-dark-accent-primary/10 p-2 rounded-lg\">\r\n                <svg class=\"w-4 h-4 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7\"></path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <p class=\"text-xs text-text dark:text-dark-text-secondary\">Évaluée le</p>\r\n                <p class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary\">{{ formatDate(evaluation.dateEvaluation) }}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Score -->\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div [ngClass]=\"getScoreIconClass(getScoreTotal(evaluation))\" class=\"p-2 rounded-lg\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <p class=\"text-xs text-text dark:text-dark-text-secondary\">Score total</p>\r\n                <span [ngClass]=\"getScoreColorClass(getScoreTotal(evaluation))\" class=\"text-lg font-bold\">\r\n                  {{ getScoreTotal(evaluation) }}/20\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Détails des scores -->\r\n            <div class=\"bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-lg p-3\">\r\n              <p class=\"text-xs text-text dark:text-dark-text-secondary mb-1\">Détail des scores</p>\r\n              <div class=\"grid grid-cols-2 gap-1 text-xs\">\r\n                <div class=\"flex justify-between\">\r\n                  <span>Structure:</span>\r\n                  <span class=\"font-medium\">{{ evaluation.scores?.structure || 0 }}</span>\r\n                </div>\r\n                <div class=\"flex justify-between\">\r\n                  <span>Pratiques:</span>\r\n                  <span class=\"font-medium\">{{ evaluation.scores?.pratiques || 0 }}</span>\r\n                </div>\r\n                <div class=\"flex justify-between\">\r\n                  <span>Fonctionnalité:</span>\r\n                  <span class=\"font-medium\">{{ evaluation.scores?.fonctionnalite || 0 }}</span>\r\n                </div>\r\n                <div class=\"flex justify-between\">\r\n                  <span>Originalité:</span>\r\n                  <span class=\"font-medium\">{{ evaluation.scores?.originalite || 0 }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Actions -->\r\n          <div class=\"flex flex-col sm:flex-row gap-2\">\r\n            <button (click)=\"viewEvaluationDetails(evaluation.rendu)\"\r\n                    class=\"group/btn px-4 py-2 bg-gradient-to-r from-info to-primary dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-4 h-4 group-hover/btn:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"></path>\r\n                </svg>\r\n                <span>Voir détails</span>\r\n              </div>\r\n            </button>\r\n            <button (click)=\"editEvaluation(evaluation.rendu)\"\r\n                    class=\"group/btn px-4 py-2 bg-gradient-to-r from-secondary to-primary-dark dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-4 h-4 group-hover/btn:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\r\n                </svg>\r\n                <span>Modifier</span>\r\n              </div>\r\n            </button>\r\n            <button (click)=\"deleteEvaluation(evaluation._id)\"\r\n                    class=\"group/btn px-4 py-2 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-4 h-4 group-hover/btn:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                </svg>\r\n                <span>Supprimer</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Empty State moderne -->\r\n    <div *ngIf=\"!isLoading && !error && filteredEvaluations.length === 0\" class=\"text-center py-16\">\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto\">\r\n        <div class=\"bg-gradient-to-br from-primary/10 to-secondary/10 dark:from-dark-accent-primary/20 dark:to-dark-accent-secondary/20 rounded-2xl p-6 mb-6\">\r\n          <svg class=\"h-16 w-16 mx-auto text-primary dark:text-dark-accent-primary\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\r\n          </svg>\r\n        </div>\r\n        <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2\">Aucune évaluation trouvée</h3>\r\n        <p class=\"text-text dark:text-dark-text-secondary mb-4\">Aucune évaluation ne correspond à vos critères de filtrage actuels.</p>\r\n        <button (click)=\"resetFilters()\" class=\"px-6 py-2 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n          <div class=\"flex items-center justify-center space-x-2\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\r\n            </svg>\r\n            <span>Réinitialiser les filtres</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n"], "mappings": "AAIA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAChE,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;;;;;;;;;IC0B9BC,EAAA,CAAAC,cAAA,cAA+E;IAE3ED,EAAA,CAAAE,SAAA,cAAqH;IAEvHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAI,MAAA,yCAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIzGH,EAAA,CAAAC,cAAA,cAAyL;IAGnLD,EAAA,CAAAK,cAAA,EAA2H;IAA3HL,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,eAAmI;IACrIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAuB;IAAvBN,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAExCH,EAAA,CAAAC,cAAA,iBACmL;IAD3KD,EAAA,CAAAO,UAAA,mBAAAC,iEAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAEjCd,EAAA,CAAAI,MAAA,uBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IALgBH,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAiDhClB,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAI,MAAA,GAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAAtCH,EAAA,CAAAmB,UAAA,UAAAC,SAAA,CAAgB;IAACpB,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAgB,iBAAA,CAAAI,SAAA,CAAY;;;;;IAiBpEpB,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAAhDH,EAAA,CAAAmB,UAAA,UAAAE,UAAA,CAAAC,GAAA,CAAoB;IAACtB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAK,UAAA,CAAAE,KAAA,CAAkB;;;;;;IAxDtFvB,EAAA,CAAAC,cAAA,cAA6L;IAGvLD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAyO;IAC3OF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAAyE;IAAzEN,EAAA,CAAAC,cAAA,aAAyE;IAAAD,EAAA,CAAAI,MAAA,2BAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGpGH,EAAA,CAAAC,cAAA,cAAmD;IAK3CD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAA6H;IAC/HF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG1BH,EAAA,CAAAC,cAAA,iBAEuC;IAFpBD,EAAA,CAAAO,UAAA,2BAAAiB,yEAAAC,MAAA;MAAAzB,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAAc,OAAA,CAAAC,UAAA,GAAAH,MAAA;IAAA,EAAwB,mBAAAI,iEAAA;MAAA7B,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAI,OAAA,GAAA9B,EAAA,CAAAY,aAAA;MAAA,OAAUZ,EAAA,CAAAa,WAAA,CAAAiB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAA1B;IAA3C/B,EAAA,CAAAG,YAAA,EAEuC;IAIzCH,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAAwV;IAC1VF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGvBH,EAAA,CAAAC,cAAA,kBAC8V;IADtVD,EAAA,CAAAO,UAAA,2BAAAyB,0EAAAP,MAAA;MAAAzB,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAO,OAAA,GAAAjC,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAAoB,OAAA,CAAAC,YAAA,GAAAT,MAAA;IAAA,EAA0B,oBAAAU,mEAAA;MAAAnC,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAU,OAAA,GAAApC,EAAA,CAAAY,aAAA;MAAA,OAAWZ,EAAA,CAAAa,WAAA,CAAAuB,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAzB;IAEhCrC,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAsC,UAAA,KAAAC,kDAAA,qBAA6E;IAC/EvC,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAA4J;IAC9JF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGvBH,EAAA,CAAAC,cAAA,kBAC8V;IADtVD,EAAA,CAAAO,UAAA,2BAAAiC,0EAAAf,MAAA;MAAAzB,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAe,OAAA,GAAAzC,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAA4B,OAAA,CAAAC,YAAA,GAAAjB,MAAA;IAAA,EAA0B,oBAAAkB,mEAAA;MAAA3C,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAkB,OAAA,GAAA5C,EAAA,CAAAY,aAAA;MAAA,OAAWZ,EAAA,CAAAa,WAAA,CAAA+B,OAAA,CAAAP,YAAA,EAAc;IAAA,EAAzB;IAEhCrC,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAI,MAAA,wBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAsC,UAAA,KAAAO,kDAAA,qBAAuF;IACzF7C,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAAqjB;IAEvjBF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGxBH,EAAA,CAAAC,cAAA,eAAqC;IAC3BD,EAAA,CAAAO,UAAA,mBAAAuC,kEAAA;MAAA9C,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAqB,OAAA,GAAA/C,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAkC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAE9BhD,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAK,cAAA,EAA2E;IAA3EL,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA6L;IAC/LF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,0BAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG9BH,EAAA,CAAAC,cAAA,kBACyN;IADjND,EAAA,CAAAO,UAAA,mBAAA0C,kEAAA;MAAAjD,EAAA,CAAAS,aAAA,CAAAiB,IAAA;MAAA,MAAAwB,OAAA,GAAAlD,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAqC,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAErCnD,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAK,cAAA,EAA2E;IAA3EL,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAAgJ;IAClJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAlEXH,EAAA,CAAAe,SAAA,IAAwB;IAAxBf,EAAA,CAAAmB,UAAA,YAAAiC,MAAA,CAAAxB,UAAA,CAAwB;IAenC5B,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAmB,UAAA,YAAAiC,MAAA,CAAAlB,YAAA,CAA0B;IAGLlC,EAAA,CAAAe,SAAA,GAAU;IAAVf,EAAA,CAAAmB,UAAA,YAAAiC,MAAA,CAAAC,OAAA,CAAU;IAc/BrD,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAmB,UAAA,YAAAiC,MAAA,CAAAV,YAAA,CAA0B;IAGL1C,EAAA,CAAAe,SAAA,GAAU;IAAVf,EAAA,CAAAmB,UAAA,YAAAiC,MAAA,CAAAE,OAAA,CAAU;;;;;;IAyC3CtD,EAAA,CAAAC,cAAA,cAA4P;IAOlPD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyM;IACvMD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAM,eAAA,EAAoB;IAApBN,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAI,MAAA,IAA0D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACzHH,EAAA,CAAAC,cAAA,eAA8C;IAE1CD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAAwV;IAC1VF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAA6E;IAA7EN,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAK,cAAA,EAAsH;IAAtHL,EAAA,CAAAC,cAAA,eAAsH;IACpHD,EAAA,CAAAE,SAAA,gBAA4J;IAC9JF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAA6E;IAA7EN,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAOfH,EAAA,CAAAC,cAAA,eAA2F;IAIrFD,EAAA,CAAAK,cAAA,EAAmH;IAAnHL,EAAA,CAAAC,cAAA,eAAmH;IACjHD,EAAA,CAAAE,SAAA,gBAAqK;IACvKF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAAK;IAALN,EAAA,CAAAC,cAAA,WAAK;IACwDD,EAAA,CAAAI,MAAA,4BAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACzEH,EAAA,CAAAC,cAAA,aAA4E;IAAAD,EAAA,CAAAI,MAAA,IAA2C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAK/HH,EAAA,CAAAC,cAAA,eAAyC;IAErCD,EAAA,CAAAK,cAAA,EAA2E;IAA3EL,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAAsR;IACxRF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAAK;IAALN,EAAA,CAAAC,cAAA,WAAK;IACwDD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,gBAA0F;IACxFD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKXH,EAAA,CAAAC,cAAA,eAAmE;IACDD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACrFH,EAAA,CAAAC,cAAA,eAA4C;IAElCD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACvBH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAuC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE1EH,EAAA,CAAAC,cAAA,eAAkC;IAC1BD,EAAA,CAAAI,MAAA,kBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACvBH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAuC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE1EH,EAAA,CAAAC,cAAA,eAAkC;IAC1BD,EAAA,CAAAI,MAAA,4BAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAA4C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE/EH,EAAA,CAAAC,cAAA,eAAkC;IAC1BD,EAAA,CAAAI,MAAA,yBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzBH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAyC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAOlFH,EAAA,CAAAC,cAAA,eAA6C;IACnCD,EAAA,CAAAO,UAAA,mBAAAgD,wEAAA;MAAA,MAAAC,WAAA,GAAAxD,EAAA,CAAAS,aAAA,CAAAgD,IAAA;MAAA,MAAAC,cAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAA+C,OAAA,CAAAC,qBAAA,CAAAH,cAAA,CAAAI,KAAA,CAAuC;IAAA,EAAC;IAEvD9D,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAK,cAAA,EAA0H;IAA1HL,EAAA,CAAAC,cAAA,eAA0H;IACxHD,EAAA,CAAAE,SAAA,gBAAkH;IAEpHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,yBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG7BH,EAAA,CAAAC,cAAA,kBAC8O;IADtOD,EAAA,CAAAO,UAAA,mBAAAwD,wEAAA;MAAA,MAAAP,WAAA,GAAAxD,EAAA,CAAAS,aAAA,CAAAgD,IAAA;MAAA,MAAAC,cAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAK,OAAA,GAAAhE,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAmD,OAAA,CAAAC,cAAA,CAAAP,cAAA,CAAAI,KAAA,CAAgC;IAAA,EAAC;IAEhD9D,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAK,cAAA,EAA0H;IAA1HL,EAAA,CAAAC,cAAA,eAA0H;IACxHD,EAAA,CAAAE,SAAA,gBAAwM;IAC1MF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGzBH,EAAA,CAAAC,cAAA,kBACmN;IAD3MD,EAAA,CAAAO,UAAA,mBAAA2D,wEAAA;MAAA,MAAAV,WAAA,GAAAxD,EAAA,CAAAS,aAAA,CAAAgD,IAAA;MAAA,MAAAC,cAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAQ,OAAA,GAAAnE,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAsD,OAAA,CAAAC,gBAAA,CAAAV,cAAA,CAAApC,GAAA,CAAgC;IAAA,EAAC;IAEhDtB,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAK,cAAA,EAA0H;IAA1HL,EAAA,CAAAC,cAAA,eAA0H;IACxHD,EAAA,CAAAE,SAAA,gBAA8M;IAChNF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAnHtBH,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAqE,kBAAA,MAAAC,OAAA,CAAAC,kBAAA,CAAAb,cAAA,CAAAc,QAAA,OACF;IASExE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAqE,kBAAA,MAAAC,OAAA,CAAAG,cAAA,CAAAf,cAAA,CAAAc,QAAA,OACF;IAC2DxE,EAAA,CAAAe,SAAA,GAA0D;IAA1Df,EAAA,CAAAgB,iBAAA,EAAA0C,cAAA,CAAAc,QAAA,kBAAAd,cAAA,CAAAc,QAAA,CAAAE,KAAA,4BAA0D;IAO/G1E,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAqE,kBAAA,MAAAC,OAAA,CAAAK,eAAA,CAAAjB,cAAA,CAAAc,QAAA,OACF;IAOExE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAqE,kBAAA,MAAAC,OAAA,CAAAM,eAAA,CAAAlB,cAAA,OACF;IAiB0E1D,EAAA,CAAAe,SAAA,IAA2C;IAA3Cf,EAAA,CAAAgB,iBAAA,CAAAsD,OAAA,CAAAO,UAAA,CAAAnB,cAAA,CAAAoB,cAAA,EAA2C;IAMpH9E,EAAA,CAAAe,SAAA,GAAwD;IAAxDf,EAAA,CAAAmB,UAAA,YAAAmD,OAAA,CAAAS,iBAAA,CAAAT,OAAA,CAAAU,aAAA,CAAAtB,cAAA,GAAwD;IAOrD1D,EAAA,CAAAe,SAAA,GAAyD;IAAzDf,EAAA,CAAAmB,UAAA,YAAAmD,OAAA,CAAAW,kBAAA,CAAAX,OAAA,CAAAU,aAAA,CAAAtB,cAAA,GAAyD;IAC7D1D,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAqE,kBAAA,MAAAC,OAAA,CAAAU,aAAA,CAAAtB,cAAA,UACF;IAU4B1D,EAAA,CAAAe,SAAA,GAAuC;IAAvCf,EAAA,CAAAgB,iBAAA,EAAA0C,cAAA,CAAAwB,MAAA,kBAAAxB,cAAA,CAAAwB,MAAA,CAAAC,SAAA,OAAuC;IAIvCnF,EAAA,CAAAe,SAAA,GAAuC;IAAvCf,EAAA,CAAAgB,iBAAA,EAAA0C,cAAA,CAAAwB,MAAA,kBAAAxB,cAAA,CAAAwB,MAAA,CAAAE,SAAA,OAAuC;IAIvCpF,EAAA,CAAAe,SAAA,GAA4C;IAA5Cf,EAAA,CAAAgB,iBAAA,EAAA0C,cAAA,CAAAwB,MAAA,kBAAAxB,cAAA,CAAAwB,MAAA,CAAAG,cAAA,OAA4C;IAI5CrF,EAAA,CAAAe,SAAA,GAAyC;IAAzCf,EAAA,CAAAgB,iBAAA,EAAA0C,cAAA,CAAAwB,MAAA,kBAAAxB,cAAA,CAAAwB,MAAA,CAAAI,WAAA,OAAyC;;;;;IA1FjFtF,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAsC,UAAA,IAAAiD,8CAAA,oBA+HM;IACRvF,EAAA,CAAAG,YAAA,EAAM;;;;IAhIwBH,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAmB,UAAA,YAAAqE,MAAA,CAAAC,mBAAA,CAAsB;;;;;;IAmIpDzF,EAAA,CAAAC,cAAA,cAAgG;IAG1FD,EAAA,CAAAK,cAAA,EAAgI;IAAhIL,EAAA,CAAAC,cAAA,cAAgI;IAC9HD,EAAA,CAAAE,SAAA,eAA8O;IAChPF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAM,eAAA,EAA8E;IAA9EN,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAI,MAAA,0CAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5GH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAI,MAAA,yFAAmE;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC/HH,EAAA,CAAAC,cAAA,kBAA2P;IAAnPD,EAAA,CAAAO,UAAA,mBAAAmF,iEAAA;MAAA1F,EAAA,CAAAS,aAAA,CAAAkF,IAAA;MAAA,MAAAC,OAAA,GAAA5F,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAA+E,OAAA,CAAA5C,YAAA,EAAc;IAAA,EAAC;IAC9BhD,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAK,cAAA,EAA2E;IAA3EL,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA6L;IAC/LF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAM,eAAA,EAAM;IAANN,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,sCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;ADrRlD,OAAM,MAAO0F,wBAAwB;EAanCC,YACUC,aAA4B,EAC5BC,iBAAoC,EACpCC,MAAc;IAFd,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IAfhB,KAAAC,WAAW,GAA4B,EAAE;IACzC,KAAAT,mBAAmB,GAA4B,EAAE;IACjD,KAAAU,SAAS,GAAY,IAAI;IACzB,KAAAjF,KAAK,GAAW,EAAE;IAClB,KAAAU,UAAU,GAAW,EAAE;IACvB,KAAAM,YAAY,GAAW,EAAE;IACzB,KAAAQ,YAAY,GAAW,EAAE;IACzB,KAAAW,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAU,EAAE;IAEX,KAAA8C,QAAQ,GAAG,IAAItG,OAAO,EAAQ;EAMlC;EAEJuG,QAAQA,CAAA;IACN,IAAI,CAACvF,eAAe,EAAE;EACxB;EAEAwF,WAAWA,CAAA;IACT,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;IACpB,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;EAC1B;EAEA1F,eAAeA,CAAA;IACb,IAAI,CAACqF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACjF,KAAK,GAAG,EAAE;IAEfuF,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAErD,IAAI,CAACV,iBAAiB,CAACW,iBAAiB,EAAE,CACvCC,IAAI,CACH/G,SAAS,CAAC,IAAI,CAACuG,QAAQ,CAAC,EACxBzG,UAAU,CAACkH,GAAG,IAAG;MACfJ,OAAO,CAACvF,KAAK,CAAC,4CAA4C,EAAE2F,GAAG,CAAC;MAChE,IAAI,CAAC3F,KAAK,GAAG,sEAAsE;MACnF,IAAI,CAACiF,SAAS,GAAG,KAAK;MACtB,OAAOpG,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,EACFH,QAAQ,CAAC,MAAK;MACZ6G,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACP,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH,CACAW,SAAS,CAAC;MACTP,IAAI,EAAGL,WAAW,IAAI;QACpBO,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAER,WAAW,CAAC;QAE/C,IAAI,CAACa,KAAK,CAACC,OAAO,CAACd,WAAW,CAAC,EAAE;UAC/BO,OAAO,CAACvF,KAAK,CAAC,4CAA4C,EAAEgF,WAAW,CAAC;UACxE,IAAI,CAAChF,KAAK,GAAG,4DAA4D;UACzE;;QAGF;QACA,IAAI,CAACgF,WAAW,GAAGA,WAAW,CAACe,GAAG,CAACC,UAAU,IAAG;UAC9C,MAAMC,eAAe,GAAGD,UAAmC;UAE3D;UACA,IAAI,CAACC,eAAe,CAACC,aAAa,IAAI,CAACD,eAAe,CAACC,aAAa,CAAC7F,KAAK,EAAE;YAC1EkF,OAAO,CAACY,IAAI,CAAC,iDAAiD,EAAEF,eAAe,CAAC7F,GAAG,CAAC;YAEpF;YACA,IAAI6F,eAAe,CAACG,YAAY,IAAIH,eAAe,CAACG,YAAY,CAACC,MAAM,EAAE;cACvEJ,eAAe,CAACC,aAAa,GAAGD,eAAe,CAACG,YAAY,CAACC,MAAM;;;UAIvE,OAAOJ,eAAe;QACxB,CAAC,CAAC;QAEF,IAAI,CAACK,wBAAwB,EAAE;QAC/B,IAAI,CAACnF,YAAY,EAAE;QAEnB;QACA,IAAI,CAACoF,iBAAiB,EAAE;MAC1B;KACD,CAAC;EACN;EAEAD,wBAAwBA,CAAA;IACtB;IACA,MAAME,UAAU,GAAG,IAAIC,GAAG,EAAU;IAEpC,IAAI,CAACzB,WAAW,CAAC0B,OAAO,CAACV,UAAU,IAAG;MACpC,MAAMW,MAAM,GAAGX,UAAU,CAAC1C,QAAQ,EAAEqD,MAAM;MAC1C,IAAIA,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QAClCJ,UAAU,CAACK,GAAG,CAACF,MAAM,CAAC;QACtBpB,OAAO,CAACC,GAAG,CAAC,kBAAkBmB,MAAM,EAAE,CAAC;OACxC,MAAM;QACLpB,OAAO,CAACC,GAAG,CAAC,2BAA2BQ,UAAU,CAAC5F,GAAG,EAAE,CAAC;;IAE5D,CAAC,CAAC;IAEF,IAAI,CAAC+B,OAAO,GAAG0D,KAAK,CAACiB,IAAI,CAACN,UAAU,CAAC,CAACO,IAAI,EAAE;IAC5CxB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACrD,OAAO,CAAC;IAE9C;IACA,MAAM6E,UAAU,GAAG,IAAIC,GAAG,EAAe;IACzC,IAAI,CAACjC,WAAW,CAAC0B,OAAO,CAACV,UAAU,IAAG;MACpC,IAAIA,UAAU,CAACE,aAAa,IAAIF,UAAU,CAACE,aAAa,CAAC9F,GAAG,EAAE;QAC5D4G,UAAU,CAACE,GAAG,CAAClB,UAAU,CAACE,aAAa,CAAC9F,GAAG,EAAE4F,UAAU,CAACE,aAAa,CAAC;;IAE1E,CAAC,CAAC;IACF,IAAI,CAAC9D,OAAO,GAAGyD,KAAK,CAACiB,IAAI,CAACE,UAAU,CAACG,MAAM,EAAE,CAAC;EAChD;EAEAhG,YAAYA,CAAA;IACV,IAAIiG,OAAO,GAAG,IAAI,CAACpC,WAAW;IAE9B;IACA,IAAI,IAAI,CAACtE,UAAU,CAACkG,IAAI,EAAE,KAAK,EAAE,EAAE;MACjC,MAAMS,IAAI,GAAG,IAAI,CAAC3G,UAAU,CAAC4G,WAAW,EAAE,CAACV,IAAI,EAAE;MACjDQ,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACvB,UAAU,IAChCA,UAAU,CAAC1C,QAAQ,EAAEkE,GAAG,EAAEF,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACtDrB,UAAU,CAAC1C,QAAQ,EAAEoE,MAAM,EAAEJ,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACzDrB,UAAU,CAACE,aAAa,EAAE7F,KAAK,EAAEiH,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAE,CAChE;;IAGH;IACA,IAAI,IAAI,CAACrG,YAAY,EAAE;MACrBoG,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACvB,UAAU,IAAIA,UAAU,CAAC1C,QAAQ,EAAEqD,MAAM,KAAK,IAAI,CAAC3F,YAAY,CAAC;;IAG3F;IACA,IAAI,IAAI,CAACQ,YAAY,EAAE;MACrB4F,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACvB,UAAU,IAAIA,UAAU,CAACE,aAAa,EAAE9F,GAAG,KAAK,IAAI,CAACoB,YAAY,CAAC;;IAG7F,IAAI,CAAC+C,mBAAmB,GAAG6C,OAAO;EACpC;EAEAvG,cAAcA,CAAA;IACZ,IAAI,CAACM,YAAY,EAAE;EACrB;EAEAW,YAAYA,CAAA;IACV,IAAI,CAACpB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACM,YAAY,GAAG,EAAE;IACtB,IAAI,CAACQ,YAAY,GAAG,EAAE;IACtB,IAAI,CAACL,YAAY,EAAE;EACrB;EAEA4B,cAAcA,CAAC4E,OAAe;IAC5B,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,iCAAiC,EAAED,OAAO,CAAC,CAAC;EACpE;EAEAhF,qBAAqBA,CAACgF,OAAe;IACnC,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,oCAAoC,EAAED,OAAO,CAAC,CAAC;EACvE;EAEA7D,aAAaA,CAACkC,UAAiC;IAC7C,IAAI,CAACA,UAAU,CAAChC,MAAM,EAAE,OAAO,CAAC;IAEhC,MAAMA,MAAM,GAAGgC,UAAU,CAAChC,MAAM;IAChC,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACE,SAAS,GAAGF,MAAM,CAACG,cAAc,GAAGH,MAAM,CAACI,WAAW;EACzF;EAEAyD,aAAaA,CAACC,KAAa;IACzB,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,6BAA6B;IACrD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,2BAA2B;IACnD,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,+BAA+B;IACtD,OAAO,yBAAyB;EAClC;EAEAnE,UAAUA,CAACoE,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE,OAAO,gBAAgB;IAClC,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,EAAE;EAC5C;EAEA;EACA1B,iBAAiBA,CAAA;IACfhB,OAAO,CAAC2C,KAAK,CAAC,4BAA4B,CAAC;IAE3C,IAAI,CAAClD,WAAW,CAAC0B,OAAO,CAACV,UAAU,IAAG;MACpCT,OAAO,CAACC,GAAG,CAAC,cAAcQ,UAAU,CAAC5F,GAAG,GAAG,CAAC;MAC5CmF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEQ,UAAU,CAAC1C,QAAQ,CAAC;MAC/C,IAAI0C,UAAU,CAAC1C,QAAQ,EAAE;QAEvBiC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEQ,UAAU,CAAC1C,QAAQ,CAACkE,GAAG,CAAC;QAC9CjC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEQ,UAAU,CAAC1C,QAAQ,CAACoE,MAAM,CAAC;QACpDnC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEQ,UAAU,CAAC1C,QAAQ,CAACqD,MAAM,CAAC;;MAEtDpB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEQ,UAAU,CAACI,YAAY,CAAC;IAClD,CAAC,CAAC;IAEFb,OAAO,CAAC4C,QAAQ,EAAE;EACpB;EAEA;EACAlG,mBAAmBA,CAAA;IACjB,IAAI,CAACmG,OAAO,CAAC,gEAAgE,CAAC,EAAE;MAC9E;;IAGF,IAAI,CAACnD,SAAS,GAAG,IAAI;IAErB,IAAI,CAACH,iBAAiB,CAAC7C,mBAAmB,EAAE,CAAC2D,SAAS,CAAC;MACrDP,IAAI,EAAGgD,QAAQ,IAAI;QACjB9C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6C,QAAQ,CAAC;QACjDC,KAAK,CAAC,GAAGD,QAAQ,CAACE,YAAY,yCAAyC,CAAC;QACxE,IAAI,CAAC3I,eAAe,EAAE,CAAC,CAAC;MAC1B,CAAC;;MACDI,KAAK,EAAG2F,GAAG,IAAI;QACbJ,OAAO,CAACvF,KAAK,CAAC,4CAA4C,EAAE2F,GAAG,CAAC;QAChE2C,KAAK,CAAC,4CAA4C,CAAC;QACnD,IAAI,CAACrD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACA5B,kBAAkBA,CAACC,QAAa;IAC9B,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;IAE1B;IACA,MAAMkF,SAAS,GAAGlF,QAAQ,CAACkF,SAAS,IAAI,EAAE;IAC1C,MAAMC,QAAQ,GAAGnF,QAAQ,CAACmF,QAAQ,IAAI,EAAE;IAExC,IAAID,SAAS,IAAIC,QAAQ,IAAIA,QAAQ,CAAC7B,IAAI,EAAE,EAAE;MAC5C,OAAO,CAAC4B,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE;;IAGjE;IACA,MAAMC,QAAQ,GAAGtF,QAAQ,CAACsF,QAAQ,IAAItF,QAAQ,CAACuF,IAAI,IAAIvF,QAAQ,CAACwF,QAAQ,IAAI,EAAE;IAC9E,IAAIF,QAAQ,IAAIA,QAAQ,CAAChC,IAAI,EAAE,EAAE;MAC/B,MAAMmC,KAAK,GAAGH,QAAQ,CAAChC,IAAI,EAAE,CAACoC,KAAK,CAAC,GAAG,CAAC;MACxC,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;QACrB,OAAO,CAACF,KAAK,CAAC,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,EAAE;OAC/D,MAAM;QACL;QACA,OAAOC,QAAQ,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACP,WAAW,EAAE;;;IAIjD;IACA,IAAIH,SAAS,IAAIA,SAAS,CAAC5B,IAAI,EAAE,EAAE;MACjC,OAAO4B,SAAS,CAACU,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACP,WAAW,EAAE;;IAGhD,OAAO,IAAI;EACb;EAEApF,cAAcA,CAACD,QAAa;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,qBAAqB;IAE3C;IACA,MAAMkF,SAAS,GAAGlF,QAAQ,CAACkF,SAAS,IAAI,EAAE;IAC1C,MAAMC,QAAQ,GAAGnF,QAAQ,CAACmF,QAAQ,IAAI,EAAE;IAExC,IAAID,SAAS,IAAIC,QAAQ,IAAIA,QAAQ,CAAC7B,IAAI,EAAE,EAAE;MAC5C,OAAO,GAAG4B,SAAS,IAAIC,QAAQ,EAAE,CAAC7B,IAAI,EAAE;;IAG1C;IACA,MAAMgC,QAAQ,GAAGtF,QAAQ,CAACsF,QAAQ,IAAItF,QAAQ,CAACuF,IAAI,IAAIvF,QAAQ,CAACwF,QAAQ,IAAI,EAAE;IAC9E,IAAIF,QAAQ,IAAIA,QAAQ,CAAChC,IAAI,EAAE,EAAE;MAC/B,OAAOgC,QAAQ,CAAChC,IAAI,EAAE;;IAGxB;IACA,IAAI4B,SAAS,IAAIA,SAAS,CAAC5B,IAAI,EAAE,EAAE;MACjC,OAAO4B,SAAS,CAAC5B,IAAI,EAAE;;IAGzB;IACA,IAAItD,QAAQ,CAACE,KAAK,EAAE;MAClB,OAAOF,QAAQ,CAACE,KAAK;;IAGvB,OAAO,qBAAqB;EAC9B;EAEAC,eAAeA,CAACH,QAAa;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,cAAc;IAEpC;IACA,IAAIA,QAAQ,CAAC4E,KAAK,IAAI,OAAO5E,QAAQ,CAAC4E,KAAK,KAAK,QAAQ,IAAI5E,QAAQ,CAAC4E,KAAK,CAACW,IAAI,EAAE;MAC/E,OAAOvF,QAAQ,CAAC4E,KAAK,CAACW,IAAI;;IAG5B;IACA,IAAIvF,QAAQ,CAAC4E,KAAK,IAAI,OAAO5E,QAAQ,CAAC4E,KAAK,KAAK,QAAQ,IAAI5E,QAAQ,CAAC4E,KAAK,CAACtB,IAAI,EAAE,EAAE;MACjF,OAAOtD,QAAQ,CAAC4E,KAAK,CAACtB,IAAI,EAAE;;IAG9B;IACA,IAAItD,QAAQ,CAACqD,MAAM,IAAI,OAAOrD,QAAQ,CAACqD,MAAM,KAAK,QAAQ,IAAIrD,QAAQ,CAACqD,MAAM,CAACC,IAAI,EAAE,EAAE;MACpF,OAAOtD,QAAQ,CAACqD,MAAM,CAACC,IAAI,EAAE;;IAG/B,IAAItD,QAAQ,CAAC6F,SAAS,IAAI,OAAO7F,QAAQ,CAAC6F,SAAS,KAAK,QAAQ,IAAI7F,QAAQ,CAAC6F,SAAS,CAACvC,IAAI,EAAE,EAAE;MAC7F,OAAOtD,QAAQ,CAAC6F,SAAS,CAACvC,IAAI,EAAE;;IAGlC,IAAItD,QAAQ,CAAC8F,UAAU,IAAI,OAAO9F,QAAQ,CAAC8F,UAAU,KAAK,QAAQ,IAAI9F,QAAQ,CAAC8F,UAAU,CAACxC,IAAI,EAAE,EAAE;MAChG,OAAOtD,QAAQ,CAAC8F,UAAU,CAACxC,IAAI,EAAE;;IAGnC,OAAO,cAAc;EACvB;EAEAlD,eAAeA,CAACsC,UAAiC;IAC/C,OAAOA,UAAU,CAACE,aAAa,EAAE7F,KAAK,IAC/B2F,UAAU,CAACI,YAAY,EAAEC,MAAM,EAAEhG,KAAK,IACtC,gBAAgB;EACzB;EAEAgJ,eAAeA,CAAA;IACb,IAAI,IAAI,CAACrE,WAAW,CAACiE,MAAM,KAAK,CAAC,EAAE,OAAO,GAAG;IAE7C,MAAMK,UAAU,GAAG,IAAI,CAACtE,WAAW,CAACuE,MAAM,CAAC,CAACC,GAAG,EAAExD,UAAU,KAAI;MAC7D,OAAOwD,GAAG,GAAG,IAAI,CAAC1F,aAAa,CAACkC,UAAU,CAAC;IAC7C,CAAC,EAAE,CAAC,CAAC;IAEL,MAAMyD,OAAO,GAAGH,UAAU,GAAG,IAAI,CAACtE,WAAW,CAACiE,MAAM;IACpD,OAAOQ,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;EAC3B;EAEA7F,iBAAiBA,CAACiE,KAAa;IAC7B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,6FAA6F;IACrH,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,mFAAmF;IAC3G,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iEAAiE;IACxF,OAAO,uEAAuE;EAChF;EAEA/D,kBAAkBA,CAAC+D,KAAa;IAC9B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,8CAA8C;IACtE,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,yCAAyC;IACjE,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,gCAAgC;IACvD,OAAO,mCAAmC;EAC5C;EAEA;EACA5E,gBAAgBA,CAACyG,YAAoB;IACnC,IAAI,CAACvB,OAAO,CAAC,sFAAsF,CAAC,EAAE;MACpG;;IAGF,IAAI,CAACtD,iBAAiB,CAAC5B,gBAAgB,CAACyG,YAAY,CAAC,CAAC/D,SAAS,CAAC;MAC9DP,IAAI,EAAEA,CAAA,KAAK;QACTiD,KAAK,CAAC,oCAAoC,CAAC;QAC3C,IAAI,CAAC1I,eAAe,EAAE,CAAC,CAAC;MAC1B,CAAC;;MACDI,KAAK,EAAG2F,GAAG,IAAI;QACbJ,OAAO,CAACvF,KAAK,CAAC,gCAAgC,EAAE2F,GAAG,CAAC;QACpD2C,KAAK,CAAC,iDAAiD,CAAC;MAC1D;KACD,CAAC;EACJ;;;uBAlWW3D,wBAAwB,EAAA7F,EAAA,CAAA8K,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAhL,EAAA,CAAA8K,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAlL,EAAA,CAAA8K,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBvF,wBAAwB;MAAAwF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBrC3L,EAAA,CAAAC,cAAA,aAA8F;UAOlFD,EAAA,CAAAK,cAAA,EAAsF;UAAtFL,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAE,SAAA,cAAiP;UACnPF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAM,eAAA,EAAK;UAALN,EAAA,CAAAC,cAAA,UAAK;UAC4CD,EAAA,CAAAI,MAAA,kCAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzEH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAI,MAAA,wDAA2C;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAG5EH,EAAA,CAAAC,cAAA,eAAiE;UAE7BD,EAAA,CAAAI,MAAA,IAAwB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAC9DH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAElCH,EAAA,CAAAE,SAAA,eAAyC;UACzCF,EAAA,CAAAC,cAAA,eAAyB;UACSD,EAAA,CAAAI,MAAA,IAAuB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAC7DH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAO1CH,EAAA,CAAAsC,UAAA,KAAAuJ,wCAAA,kBAMM;UAGN7L,EAAA,CAAAsC,UAAA,KAAAwJ,wCAAA,kBAaM;UAGN9L,EAAA,CAAAsC,UAAA,KAAAyJ,wCAAA,mBA6FM;UAGN/L,EAAA,CAAAsC,UAAA,KAAA0J,wCAAA,kBAiIM;UAGNhM,EAAA,CAAAsC,UAAA,KAAA2J,wCAAA,mBAkBM;UACRjM,EAAA,CAAAG,YAAA,EAAM;;;UA7RoCH,EAAA,CAAAe,SAAA,IAAwB;UAAxBf,EAAA,CAAAgB,iBAAA,CAAA4K,GAAA,CAAA1F,WAAA,CAAAiE,MAAA,CAAwB;UAKxBnK,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAAgB,iBAAA,CAAA4K,GAAA,CAAArB,eAAA,GAAuB;UAQzDvK,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAmB,UAAA,SAAAyK,GAAA,CAAAzF,SAAA,CAAe;UASfnG,EAAA,CAAAe,SAAA,GAAW;UAAXf,EAAA,CAAAmB,UAAA,SAAAyK,GAAA,CAAA1K,KAAA,CAAW;UAgBXlB,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAmB,UAAA,UAAAyK,GAAA,CAAAzF,SAAA,KAAAyF,GAAA,CAAA1K,KAAA,CAA0B;UAgG1BlB,EAAA,CAAAe,SAAA,GAA4D;UAA5Df,EAAA,CAAAmB,UAAA,UAAAyK,GAAA,CAAAzF,SAAA,KAAAyF,GAAA,CAAA1K,KAAA,IAAA0K,GAAA,CAAAnG,mBAAA,CAAA0E,MAAA,KAA4D;UAoI5DnK,EAAA,CAAAe,SAAA,GAA8D;UAA9Df,EAAA,CAAAmB,UAAA,UAAAyK,GAAA,CAAAzF,SAAA,KAAAyF,GAAA,CAAA1K,KAAA,IAAA0K,GAAA,CAAAnG,mBAAA,CAAA0E,MAAA,OAA8D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}