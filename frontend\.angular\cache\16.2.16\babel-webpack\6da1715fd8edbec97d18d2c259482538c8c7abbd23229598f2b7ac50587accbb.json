{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/file.service\";\nimport * as i4 from \"@angular/common\";\nfunction DetailProjectComponent_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94)(2, \"div\", 71)(3, \"div\", 95)(4, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 30);\n    i0.ɵɵelement(6, \"path\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"div\", 97)(8, \"p\", 98);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 99);\n    i0.ɵɵtext(11, \"Document\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"a\", 100)(13, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 101);\n    i0.ɵɵelement(15, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const file_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getFileName(file_r5), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r4.getFileUrl(file_r5), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailProjectComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtemplate(1, DetailProjectComponent_div_54_div_1_Template, 18, 2, \"div\", 92);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projet.fichiers);\n  }\n}\nfunction DetailProjectComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104)(2, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 106);\n    i0.ɵɵelement(4, \"path\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Aucun fichier joint \\u00E0 ce projet\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DetailProjectComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\", 108);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 109)(4, \"div\", 19);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 99);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 110);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 111);\n    i0.ɵɵelement(10, \"path\", 76);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const etudiant_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getStudentInitials(etudiant_r6), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getStudentName(etudiant_r6), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(etudiant_r6.dateRendu), \" \");\n  }\n}\nfunction DetailProjectComponent_div_150_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104)(2, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 106);\n    i0.ɵɵelement(4, \"path\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Aucun rendu pour le moment\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c1 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c2 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nconst _c3 = function () {\n  return [];\n};\nexport class DetailProjectComponent {\n  constructor(route, router, projectService, fileService) {\n    this.route = route;\n    this.router = router;\n    this.projectService = projectService;\n    this.fileService = fileService;\n    this.projet = null;\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.projectService.getProjetById(id).subscribe(data => {\n        this.projet = data;\n      });\n    }\n  }\n  getFileUrl(filePath) {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n  deleteProjet(id) {\n    if (!id) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        }\n      });\n    }\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n  }\n  static {\n    this.ɵfac = function DetailProjectComponent_Factory(t) {\n      return new (t || DetailProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailProjectComponent,\n      selectors: [[\"app-detail-project\"]],\n      decls: 151,\n      vars: 31,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"prose\", \"prose-gray\", \"dark:prose-invert\", \"max-w-none\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"leading-relaxed\"], [1, \"bg-secondary/10\", \"dark:bg-dark-accent-secondary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-secondary\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-normal\", \"text-text\", \"dark:text-dark-text-secondary\", \"ml-2\"], [\"class\", \"grid grid-cols-1 sm:grid-cols-2 gap-4\", 4, \"ngIf\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-3\", \"gap-4\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-6\", \"text-white\"], [1, \"bg-white/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-lg\", \"font-semibold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"p-6\", \"space-y-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-3\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-sm\", \"font-bold\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded-full\", \"h-3\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"h-3\", \"rounded-full\", \"transition-all\", \"duration-500\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-2\"], [1, \"grid\", \"grid-cols-1\", \"gap-4\"], [1, \"bg-gradient-to-br\", \"from-success/10\", \"to-success/5\", \"dark:from-dark-accent-secondary/20\", \"dark:to-dark-accent-secondary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-success/20\", \"dark:border-dark-accent-secondary/30\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"font-medium\", \"text-success\", \"dark:text-dark-accent-secondary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-success\", \"dark:text-dark-accent-secondary\"], [1, \"bg-success/20\", \"dark:bg-dark-accent-secondary/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-success\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-gradient-to-br\", \"from-warning/10\", \"to-warning/5\", \"dark:from-warning/20\", \"dark:to-warning/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-warning/20\", \"dark:border-warning/30\"], [1, \"text-xs\", \"font-medium\", \"text-warning\", \"dark:text-warning\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-warning/20\", \"dark:bg-warning/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-gradient-to-br\", \"from-info/10\", \"to-info/5\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-info/20\", \"dark:border-dark-accent-primary/30\"], [1, \"text-xs\", \"font-medium\", \"text-info\", \"dark:text-dark-accent-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-info\", \"dark:text-dark-accent-primary\"], [1, \"bg-info/20\", \"dark:bg-dark-accent-primary/30\", \"p-2\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"p-6\"], [1, \"space-y-3\"], [\"class\", \"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [\"class\", \"group\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\", \"border\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"hover:border-primary\", \"dark:hover:border-dark-accent-primary\", \"transition-all\", \"duration-200\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"flex-1\", \"min-w-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [\"download\", \"\", 1, \"ml-3\", \"px-3\", \"py-2\", \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-center\", \"py-8\"], [1, \"bg-gray-100\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-6\"], [1, \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"p-3\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\"], [1, \"h-10\", \"w-10\", \"rounded-xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-sm\", \"font-bold\", \"shadow-lg\"], [1, \"flex-1\"], [1, \"bg-success/10\", \"dark:bg-dark-accent-secondary/20\", \"p-1.5\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-success\", \"dark:text-dark-accent-secondary\"]],\n      template: function DetailProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n          i0.ɵɵtext(5, \"Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 5);\n          i0.ɵɵelement(7, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 12);\n          i0.ɵɵelement(15, \"path\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(16, \"div\")(17, \"h1\", 14);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 17);\n          i0.ɵɵelement(22, \"path\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"span\", 19);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 20);\n          i0.ɵɵelement(27, \"path\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"span\", 22);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"span\", 24);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 25)(34, \"div\", 26)(35, \"div\", 27)(36, \"div\", 28)(37, \"div\", 29);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 30);\n          i0.ɵɵelement(39, \"path\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(40, \"h3\", 32);\n          i0.ɵɵtext(41, \"Description du projet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 33)(43, \"p\", 34);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 27)(46, \"div\", 28)(47, \"div\", 35);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(48, \"svg\", 36);\n          i0.ɵɵelement(49, \"path\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(50, \"h3\", 32);\n          i0.ɵɵtext(51, \" Fichiers joints \");\n          i0.ɵɵelementStart(52, \"span\", 38);\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(54, DetailProjectComponent_div_54_Template, 2, 1, \"div\", 39);\n          i0.ɵɵtemplate(55, DetailProjectComponent_div_55_Template, 7, 0, \"div\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 27)(57, \"div\", 28)(58, \"div\", 41);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(59, \"svg\", 42);\n          i0.ɵɵelement(60, \"path\", 43)(61, \"path\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(62, \"h3\", 32);\n          i0.ɵɵtext(63, \"Actions disponibles\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"div\", 45)(65, \"a\", 46)(66, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(67, \"svg\", 48);\n          i0.ɵɵelement(68, \"path\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(69, \"span\");\n          i0.ɵɵtext(70, \"Modifier\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"a\", 50)(72, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(73, \"svg\", 48);\n          i0.ɵɵelement(74, \"path\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(75, \"span\");\n          i0.ɵɵtext(76, \"Voir rendus\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(77, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function DetailProjectComponent_Template_button_click_77_listener() {\n            return ctx.deleteProjet(ctx.projet == null ? null : ctx.projet._id);\n          });\n          i0.ɵɵelementStart(78, \"div\", 47);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(79, \"svg\", 48);\n          i0.ɵɵelement(80, \"path\", 53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(81, \"span\");\n          i0.ɵɵtext(82, \"Supprimer\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(83, \"div\", 54)(84, \"div\", 55)(85, \"div\", 56)(86, \"div\", 23)(87, \"div\", 57);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(88, \"svg\", 58);\n          i0.ɵɵelement(89, \"path\", 59);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(90, \"div\")(91, \"h3\", 60);\n          i0.ɵɵtext(92, \"Statistiques\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"p\", 61);\n          i0.ɵɵtext(94, \"Suivi des rendus\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(95, \"div\", 62)(96, \"div\")(97, \"div\", 63)(98, \"span\", 64);\n          i0.ɵɵtext(99, \"Progression globale\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"span\", 65);\n          i0.ɵɵtext(101);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(102, \"div\", 66);\n          i0.ɵɵelement(103, \"div\", 67);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 68)(105, \"span\");\n          i0.ɵɵtext(106);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"span\");\n          i0.ɵɵtext(108);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(109, \"div\", 69)(110, \"div\", 70)(111, \"div\", 71)(112, \"div\")(113, \"p\", 72);\n          i0.ɵɵtext(114, \"Rendus\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"p\", 73);\n          i0.ɵɵtext(116);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(117, \"div\", 74);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(118, \"svg\", 75);\n          i0.ɵɵelement(119, \"path\", 76);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(120, \"div\", 77)(121, \"div\", 71)(122, \"div\")(123, \"p\", 78);\n          i0.ɵɵtext(124, \"En attente\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"p\", 79);\n          i0.ɵɵtext(126);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(127, \"div\", 80);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(128, \"svg\", 81);\n          i0.ɵɵelement(129, \"path\", 21);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(130, \"div\", 82)(131, \"div\", 71)(132, \"div\")(133, \"p\", 83);\n          i0.ɵɵtext(134, \"Taux\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"p\", 84);\n          i0.ɵɵtext(136);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(137, \"div\", 85);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(138, \"svg\", 42);\n          i0.ɵɵelement(139, \"path\", 86);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(140, \"div\", 87)(141, \"div\", 88)(142, \"div\", 28)(143, \"div\", 35);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(144, \"svg\", 36);\n          i0.ɵɵelement(145, \"path\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(146, \"h3\", 32);\n          i0.ɵɵtext(147, \"Derniers rendus\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(148, \"div\", 89);\n          i0.ɵɵtemplate(149, DetailProjectComponent_div_149_Template, 11, 3, \"div\", 90);\n          i0.ɵɵtemplate(150, DetailProjectComponent_div_150_Template, 7, 0, \"div\", 40);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.dateLimite) ? ctx.formatDate(ctx.projet == null ? null : ctx.projet.dateLimite) : \"Pas de date limite\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getProjectStatus(), \" \");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.description) || \"Aucune description fournie pour ce projet.\", \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate2(\" (\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0, \" fichier\", ((ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0) > 1 ? \"s\" : \"\", \") \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.fichiers) || ctx.projet.fichiers.length === 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(25, _c0, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(27, _c1))(\"queryParams\", i0.ɵɵpureFunction1(28, _c2, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(30);\n          i0.ɵɵtextInterpolate2(\" \", (ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0, \"/\", (ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ctx.getProgressPercentage(), \"%\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getProgressPercentage(), \"% compl\\u00E9t\\u00E9\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getRemainingDays(), \" jours restants\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0) - ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0));\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getProgressPercentage(), \"%\");\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngForOf\", (ctx.projet == null ? null : ctx.projet.derniersRendus) || i0.ɵɵpureFunction0(30, _c3));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.derniersRendus) || ctx.projet.derniersRendus.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvZGV0YWlsLXByb2plY3QvZGV0YWlsLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r4", "getFileName", "file_r5", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "DetailProjectComponent_div_54_div_1_Template", "ctx_r0", "projet", "fichiers", "ctx_r2", "getStudentInitials", "etudiant_r6", "getStudentName", "formatDate", "dateRendu", "DetailProjectComponent", "constructor", "route", "router", "projectService", "fileService", "ngOnInit", "id", "snapshot", "paramMap", "get", "getProjetById", "subscribe", "data", "filePath", "getDownloadUrl", "deleteProjet", "confirm", "next", "alert", "navigate", "error", "err", "console", "date", "d", "Date", "getDate", "toString", "padStart", "getMonth", "getFullYear", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "FileService", "selectors", "decls", "vars", "consts", "template", "DetailProjectComponent_Template", "rf", "ctx", "DetailProjectComponent_div_54_Template", "DetailProjectComponent_div_55_Template", "ɵɵlistener", "DetailProjectComponent_Template_button_click_77_listener", "_id", "DetailProjectComponent_div_149_Template", "DetailProjectComponent_div_150_Template", "ɵɵtextInterpolate", "titre", "groupe", "dateLimite", "getStatusClass", "getProjectStatus", "description", "ɵɵtextInterpolate2", "length", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction0", "_c1", "_c2", "etudiantsRendus", "totalEtudiants", "ɵɵstyleProp", "getProgressPercentage", "getRemainingDays", "derniersRendus", "_c3"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { FileService } from 'src/app/services/file.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-project',\r\n  templateUrl: './detail-project.component.html',\r\n  styleUrls: ['./detail-project.component.css'],\r\n})\r\nexport class DetailProjectComponent implements OnInit {\r\n  projet: any = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private projectService: ProjetService,\r\n    private fileService: FileService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const id = this.route.snapshot.paramMap.get('id');\r\n    if (id) {\r\n      this.projectService.getProjetById(id).subscribe((data: any) => {\r\n        this.projet = data;\r\n      });\r\n    }\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    return this.fileService.getDownloadUrl(filePath);\r\n  }\r\n\r\n  deleteProjet(id: string | undefined): void {\r\n    if (!id) return;\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\r\n      this.projectService.deleteProjet(id).subscribe({\r\n        next: () => {\r\n          alert('Projet supprimé avec succès');\r\n          this.router.navigate(['/admin/projects']);\r\n        },\r\n        error: (err) => {\r\n          console.error('Erreur lors de la suppression du projet', err);\r\n          alert('Erreur lors de la suppression du projet');\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  formatDate(date: string | Date): string {\r\n    const d = new Date(date);\r\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1)\r\n      .toString()\r\n      .padStart(2, '0')}/${d.getFullYear()}`;\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n\r\n    <!-- Header moderne avec breadcrumb -->\r\n    <div class=\"mb-8\">\r\n      <nav class=\"flex items-center space-x-2 text-sm text-text dark:text-dark-text-secondary mb-4\">\r\n        <a routerLink=\"/admin/projects/list-project\" class=\"hover:text-primary dark:hover:text-dark-accent-primary transition-colors\">Projets</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <span class=\"text-primary dark:text-dark-accent-primary font-medium\">{{ projet?.titre || 'Détails du projet' }}</span>\r\n      </nav>\r\n\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\r\n          <div class=\"flex items-center space-x-4 mb-6 lg:mb-0\">\r\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\r\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\r\n                {{ projet?.titre || 'Chargement...' }}\r\n              </h1>\r\n              <div class=\"flex items-center space-x-4 mt-2\">\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">{{ projet?.groupe || 'Tous les groupes' }}</span>\r\n                </div>\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm text-text dark:text-dark-text-secondary\">{{ projet?.dateLimite ? formatDate(projet?.dateLimite) : 'Pas de date limite' }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Badge de statut -->\r\n          <div class=\"flex items-center space-x-3\">\r\n            <span [ngClass]=\"getStatusClass()\" class=\"px-4 py-2 rounded-xl text-sm font-medium\">\r\n              {{ getProjectStatus() }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Contenu principal -->\r\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n      <!-- Carte principale du projet -->\r\n      <div class=\"lg:col-span-2 space-y-6\">\r\n\r\n        <!-- Description du projet -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Description du projet</h3>\r\n          </div>\r\n          <div class=\"prose prose-gray dark:prose-invert max-w-none\">\r\n            <p class=\"text-text dark:text-dark-text-secondary leading-relaxed\">\r\n              {{ projet?.description || 'Aucune description fournie pour ce projet.' }}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Fichiers du projet -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-secondary/10 dark:bg-dark-accent-secondary/20 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-secondary dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">\r\n              Fichiers joints\r\n              <span class=\"text-sm font-normal text-text dark:text-dark-text-secondary ml-2\">\r\n                ({{ projet?.fichiers?.length || 0 }} fichier{{ (projet?.fichiers?.length || 0) > 1 ? 's' : '' }})\r\n              </span>\r\n            </h3>\r\n          </div>\r\n\r\n          <div *ngIf=\"projet?.fichiers?.length > 0\" class=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n            <div *ngFor=\"let file of projet.fichiers\" class=\"group\">\r\n              <div class=\"bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4 border border-gray-200 dark:border-dark-bg-tertiary hover:border-primary dark:hover:border-dark-accent-primary transition-all duration-200\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div class=\"flex items-center space-x-3 flex-1 min-w-0\">\r\n                    <div class=\"bg-primary/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\r\n                      <svg class=\"w-5 h-5 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <div class=\"flex-1 min-w-0\">\r\n                      <p class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary truncate\">\r\n                        {{ getFileName(file) }}\r\n                      </p>\r\n                      <p class=\"text-xs text-text dark:text-dark-text-secondary\">Document</p>\r\n                    </div>\r\n                  </div>\r\n                  <a [href]=\"getFileUrl(file)\" download\r\n                     class=\"ml-3 px-3 py-2 bg-primary/10 dark:bg-dark-accent-primary/20 text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary rounded-lg transition-all duration-200 text-xs font-medium\">\r\n                    <div class=\"flex items-center space-x-1\">\r\n                      <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"></path>\r\n                      </svg>\r\n                      <span>Télécharger</span>\r\n                    </div>\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"!projet?.fichiers || projet.fichiers.length === 0\" class=\"text-center py-8\">\r\n            <div class=\"bg-gray-100 dark:bg-dark-bg-tertiary/50 rounded-xl p-6\">\r\n              <div class=\"bg-gray-200 dark:bg-dark-bg-tertiary p-3 rounded-lg inline-flex items-center justify-center mb-3\">\r\n                <svg class=\"w-6 h-6 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                </svg>\r\n              </div>\r\n              <p class=\"text-sm text-text dark:text-dark-text-secondary\">Aucun fichier joint à ce projet</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions du projet -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-info/10 dark:bg-dark-accent-primary/20 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Actions disponibles</h3>\r\n          </div>\r\n\r\n          <div class=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n            <a [routerLink]=\"['/admin/projects/editProjet', projet?._id]\"\r\n               class=\"group px-4 py-3 bg-gradient-to-r from-secondary to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\r\n                </svg>\r\n                <span>Modifier</span>\r\n              </div>\r\n            </a>\r\n\r\n            <a [routerLink]=\"['/admin/projects/rendus']\" [queryParams]=\"{projetId: projet?._id}\"\r\n               class=\"group px-4 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>\r\n                </svg>\r\n                <span>Voir rendus</span>\r\n              </div>\r\n            </a>\r\n\r\n            <button (click)=\"deleteProjet(projet?._id)\"\r\n                    class=\"group px-4 py-3 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n              <div class=\"flex items-center justify-center space-x-2\">\r\n                <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                </svg>\r\n                <span>Supprimer</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Sidebar avec statistiques -->\r\n      <div class=\"space-y-6\">\r\n\r\n        <!-- Statistiques de rendu -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 overflow-hidden\">\r\n          <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-6 text-white\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"bg-white/20 p-2 rounded-lg\">\r\n                <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h3 class=\"text-lg font-semibold\">Statistiques</h3>\r\n                <p class=\"text-sm text-white/80\">Suivi des rendus</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"p-6 space-y-6\">\r\n            <!-- Progression générale -->\r\n            <div>\r\n              <div class=\"flex justify-between items-center mb-3\">\r\n                <span class=\"text-sm font-semibold text-text-dark dark:text-dark-text-primary\">Progression globale</span>\r\n                <span class=\"text-sm font-bold text-primary dark:text-dark-accent-primary\">\r\n                  {{ (projet?.etudiantsRendus?.length || 0) }}/{{ projet?.totalEtudiants || 0 }}\r\n                </span>\r\n              </div>\r\n              <div class=\"w-full bg-gray-200 dark:bg-dark-bg-tertiary rounded-full h-3\">\r\n                <div class=\"bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary h-3 rounded-full transition-all duration-500\"\r\n                     [style.width.%]=\"getProgressPercentage()\"></div>\r\n              </div>\r\n              <div class=\"flex justify-between text-xs text-text dark:text-dark-text-secondary mt-2\">\r\n                <span>{{ getProgressPercentage() }}% complété</span>\r\n                <span>{{ getRemainingDays() }} jours restants</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Cartes statistiques -->\r\n            <div class=\"grid grid-cols-1 gap-4\">\r\n              <!-- Rendus soumis -->\r\n              <div class=\"bg-gradient-to-br from-success/10 to-success/5 dark:from-dark-accent-secondary/20 dark:to-dark-accent-secondary/10 rounded-xl p-4 border border-success/20 dark:border-dark-accent-secondary/30\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p class=\"text-xs font-medium text-success dark:text-dark-accent-secondary uppercase tracking-wider\">Rendus</p>\r\n                    <p class=\"text-2xl font-bold text-success dark:text-dark-accent-secondary\">{{ projet?.etudiantsRendus?.length || 0 }}</p>\r\n                  </div>\r\n                  <div class=\"bg-success/20 dark:bg-dark-accent-secondary/30 p-2 rounded-lg\">\r\n                    <svg class=\"w-5 h-5 text-success dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- En attente -->\r\n              <div class=\"bg-gradient-to-br from-warning/10 to-warning/5 dark:from-warning/20 dark:to-warning/10 rounded-xl p-4 border border-warning/20 dark:border-warning/30\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p class=\"text-xs font-medium text-warning dark:text-warning uppercase tracking-wider\">En attente</p>\r\n                    <p class=\"text-2xl font-bold text-warning dark:text-warning\">{{ (projet?.totalEtudiants || 0) - (projet?.etudiantsRendus?.length || 0) }}</p>\r\n                  </div>\r\n                  <div class=\"bg-warning/20 dark:bg-warning/30 p-2 rounded-lg\">\r\n                    <svg class=\"w-5 h-5 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Taux de réussite -->\r\n              <div class=\"bg-gradient-to-br from-info/10 to-info/5 dark:from-dark-accent-primary/20 dark:to-dark-accent-primary/10 rounded-xl p-4 border border-info/20 dark:border-dark-accent-primary/30\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p class=\"text-xs font-medium text-info dark:text-dark-accent-primary uppercase tracking-wider\">Taux</p>\r\n                    <p class=\"text-2xl font-bold text-info dark:text-dark-accent-primary\">{{ getProgressPercentage() }}%</p>\r\n                  </div>\r\n                  <div class=\"bg-info/20 dark:bg-dark-accent-primary/30 p-2 rounded-lg\">\r\n                    <svg class=\"w-5 h-5 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\r\n                    </svg>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Derniers rendus -->\r\n        <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n          <div class=\"p-6\">\r\n            <div class=\"flex items-center space-x-3 mb-4\">\r\n              <div class=\"bg-secondary/10 dark:bg-dark-accent-secondary/20 p-2 rounded-lg\">\r\n                <svg class=\"w-5 h-5 text-secondary dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h3 class=\"text-lg font-semibold text-text-dark dark:text-dark-text-primary\">Derniers rendus</h3>\r\n            </div>\r\n\r\n            <div class=\"space-y-3\">\r\n              <div *ngFor=\"let etudiant of projet?.derniersRendus || []\" class=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl\">\r\n                <div class=\"h-10 w-10 rounded-xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center text-white text-sm font-bold shadow-lg\">\r\n                  {{ getStudentInitials(etudiant) }}\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                  <div class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">\r\n                    {{ getStudentName(etudiant) }}\r\n                  </div>\r\n                  <div class=\"text-xs text-text dark:text-dark-text-secondary\">\r\n                    {{ formatDate(etudiant.dateRendu) }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"bg-success/10 dark:bg-dark-accent-secondary/20 p-1.5 rounded-lg\">\r\n                  <svg class=\"w-4 h-4 text-success dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n\r\n              <div *ngIf=\"!projet?.derniersRendus || projet.derniersRendus.length === 0\" class=\"text-center py-8\">\r\n                <div class=\"bg-gray-100 dark:bg-dark-bg-tertiary/50 rounded-xl p-6\">\r\n                  <div class=\"bg-gray-200 dark:bg-dark-bg-tertiary p-3 rounded-lg inline-flex items-center justify-center mb-3\">\r\n                    <svg class=\"w-6 h-6 text-gray-400 dark:text-dark-text-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <p class=\"text-sm text-text dark:text-dark-text-secondary\">Aucun rendu pour le moment</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;;;;;IC4FYA,EAAA,CAAAC,cAAA,cAAwD;IAK9CD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAA4L;IAC9LH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA4B;IAA5BL,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,aAA2D;IAAAD,EAAA,CAAAM,MAAA,gBAAQ;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAG3EJ,EAAA,CAAAC,cAAA,cACiP;IAE7OD,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,gBAA2E;IACzED,EAAA,CAAAG,SAAA,iBAAgJ;IAClJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,6BAAW;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IAXtBJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,OACF;IAIDX,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAY,UAAA,SAAAH,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,EAAA,CAAAc,aAAA,CAAyB;;;;;IAjBpCd,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAe,UAAA,IAAAC,4CAAA,mBA2BM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IA5BkBJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,UAAA,YAAAK,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IA8B1CnB,EAAA,CAAAC,cAAA,eAAwF;IAGlFD,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,eAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAsM;IACxMH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA2D;IAA3DL,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAM,MAAA,2CAA+B;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IAwJ9FJ,EAAA,CAAAC,cAAA,eAAqJ;IAEjJD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAoB;IAEhBD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAE,cAAA,EAAwH;IAAxHF,EAAA,CAAAC,cAAA,eAAwH;IACtHD,EAAA,CAAAG,SAAA,gBAA+H;IACjIH,EAAA,CAAAI,YAAA,EAAM;;;;;IAbNJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAY,MAAA,CAAAC,kBAAA,CAAAC,WAAA,OACF;IAGItB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAY,MAAA,CAAAG,cAAA,CAAAD,WAAA,OACF;IAEEtB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAY,MAAA,CAAAI,UAAA,CAAAF,WAAA,CAAAG,SAAA,OACF;;;;;IASJzB,EAAA,CAAAC,cAAA,eAAoG;IAG9FD,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,eAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAiN;IACnNH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAA2D;IAA3DL,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAM,MAAA,iCAA0B;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;;;;;;;;;;;;;ADzS3G,OAAM,MAAOsB,sBAAsB;EAGjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA6B,EAC7BC,WAAwB;IAHxB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IANrB,KAAAb,MAAM,GAAQ,IAAI;EAOf;EAEHc,QAAQA,CAAA;IACN,MAAMC,EAAE,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACH,cAAc,CAACO,aAAa,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAEC,IAAS,IAAI;QAC5D,IAAI,CAACrB,MAAM,GAAGqB,IAAI;MACpB,CAAC,CAAC;;EAEN;EAEA1B,UAAUA,CAAC2B,QAAgB;IACzB,OAAO,IAAI,CAACT,WAAW,CAACU,cAAc,CAACD,QAAQ,CAAC;EAClD;EAEAE,YAAYA,CAACT,EAAsB;IACjC,IAAI,CAACA,EAAE,EAAE;IAET,IAAIU,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAACb,cAAc,CAACY,YAAY,CAACT,EAAE,CAAC,CAACK,SAAS,CAAC;QAC7CM,IAAI,EAAEA,CAAA,KAAK;UACTC,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC;QACDC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;UAC7DH,KAAK,CAAC,yCAAyC,CAAC;QAClD;OACD,CAAC;;EAEN;EAEArB,UAAUA,CAAC0B,IAAmB;IAC5B,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAO,GAAGC,CAAC,CAACE,OAAO,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACJ,CAAC,CAACK,QAAQ,EAAE,GAAG,CAAC,EACnEF,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,CAAC,CAACM,WAAW,EAAE,EAAE;EAC1C;;;uBA7CW/B,sBAAsB,EAAA1B,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7D,EAAA,CAAA0D,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAA/D,EAAA,CAAA0D,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtBvC,sBAAsB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTnCxE,EAAA,CAAAC,cAAA,aAAiK;UAM3BD,EAAA,CAAAM,MAAA,cAAO;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACzIJ,EAAA,CAAAE,cAAA,EAA2E;UAA3EF,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAG,SAAA,cAA8F;UAChGH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAqE;UAArEL,EAAA,CAAAC,cAAA,cAAqE;UAAAD,EAAA,CAAAM,MAAA,GAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAGxHJ,EAAA,CAAAC,cAAA,cAA2J;UAInJD,EAAA,CAAAE,cAAA,EAAsF;UAAtFF,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAAG,SAAA,gBAA4J;UAC9JH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,eAA8C;UAE1CD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwV;UAC1VH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,gBAA6E;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAEhIJ,EAAA,CAAAC,cAAA,eAAyC;UACvCD,EAAA,CAAAE,cAAA,EAA0G;UAA1GF,EAAA,CAAAC,cAAA,eAA0G;UACxGD,EAAA,CAAAG,SAAA,gBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA8D;UAA9DL,EAAA,CAAAC,cAAA,gBAA8D;UAAAD,EAAA,CAAAM,MAAA,IAAgF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAO7JJ,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAOfJ,EAAA,CAAAC,cAAA,eAAmD;UAQzCD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwG;UAC1GH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAEzGJ,EAAA,CAAAC,cAAA,eAA2D;UAEvDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAC,cAAA,eAA2J;UAGrJD,EAAA,CAAAE,cAAA,EAA0H;UAA1HF,EAAA,CAAAC,cAAA,eAA0H;UACxHD,EAAA,CAAAG,SAAA,gBAAsM;UACxMH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAC3ED,EAAA,CAAAM,MAAA,yBACA;UAAAN,EAAA,CAAAC,cAAA,gBAA+E;UAC7ED,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIXJ,EAAA,CAAAe,UAAA,KAAA2D,sCAAA,kBA6BM;UAEN1E,EAAA,CAAAe,UAAA,KAAA4D,sCAAA,kBASM;UACR3E,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAA2J;UAGrJD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAqjB;UAEvjBH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAM,MAAA,2BAAmB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAGvGJ,EAAA,CAAAC,cAAA,eAAmD;UAI7CD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwM;UAC1MH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIzBJ,EAAA,CAAAC,cAAA,aACmO;UAE/ND,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAiN;UACnNH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAI5BJ,EAAA,CAAAC,cAAA,kBAC+M;UADvMD,EAAA,CAAA4E,UAAA,mBAAAC,yDAAA;YAAA,OAASJ,GAAA,CAAA/B,YAAA,CAAA+B,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA4D,GAAA,CAAyB;UAAA,EAAC;UAEzC9E,EAAA,CAAAC,cAAA,eAAwD;UACtDD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAA8M;UAChNH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,iBAAS;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAQhCJ,EAAA,CAAAC,cAAA,eAAuB;UAObD,EAAA,CAAAE,cAAA,EAA2E;UAA3EF,EAAA,CAAAC,cAAA,eAA2E;UACzED,EAAA,CAAAG,SAAA,gBAAsR;UACxRH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAC+BD,EAAA,CAAAM,MAAA,oBAAY;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACnDJ,EAAA,CAAAC,cAAA,aAAiC;UAAAD,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAK3DJ,EAAA,CAAAC,cAAA,eAA2B;UAI0DD,EAAA,CAAAM,MAAA,2BAAmB;UAAAN,EAAA,CAAAI,YAAA,EAAO;UACzGJ,EAAA,CAAAC,cAAA,iBAA2E;UACzED,EAAA,CAAAM,MAAA,KACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAETJ,EAAA,CAAAC,cAAA,gBAA0E;UACxED,EAAA,CAAAG,SAAA,gBACqD;UACvDH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,gBAAuF;UAC/ED,EAAA,CAAAM,MAAA,KAAuC;UAAAN,EAAA,CAAAI,YAAA,EAAO;UACpDJ,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAM,MAAA,KAAuC;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAKxDJ,EAAA,CAAAC,cAAA,gBAAoC;UAKyED,EAAA,CAAAM,MAAA,eAAM;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAC/GJ,EAAA,CAAAC,cAAA,cAA2E;UAAAD,EAAA,CAAAM,MAAA,KAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAE3HJ,EAAA,CAAAC,cAAA,gBAA2E;UACzED,EAAA,CAAAE,cAAA,EAAwH;UAAxHF,EAAA,CAAAC,cAAA,gBAAwH;UACtHD,EAAA,CAAAG,SAAA,iBAA+H;UACjIH,EAAA,CAAAI,YAAA,EAAM;UAMZJ,EAAA,CAAAK,eAAA,EAAmK;UAAnKL,EAAA,CAAAC,cAAA,gBAAmK;UAGtED,EAAA,CAAAM,MAAA,mBAAU;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACrGJ,EAAA,CAAAC,cAAA,cAA6D;UAAAD,EAAA,CAAAM,MAAA,KAA4E;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAE/IJ,EAAA,CAAAC,cAAA,gBAA6D;UAC3DD,EAAA,CAAAE,cAAA,EAA0G;UAA1GF,EAAA,CAAAC,cAAA,gBAA0G;UACxGD,EAAA,CAAAG,SAAA,iBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UAMZJ,EAAA,CAAAK,eAAA,EAA8L;UAA9LL,EAAA,CAAAC,cAAA,gBAA8L;UAGxFD,EAAA,CAAAM,MAAA,aAAI;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACxGJ,EAAA,CAAAC,cAAA,cAAsE;UAAAD,EAAA,CAAAM,MAAA,KAA8B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAE1GJ,EAAA,CAAAC,cAAA,gBAAsE;UACpED,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,gBAAmH;UACjHD,EAAA,CAAAG,SAAA,iBAAgH;UAClHH,EAAA,CAAAI,YAAA,EAAM;UASlBJ,EAAA,CAAAK,eAAA,EAAuJ;UAAvJL,EAAA,CAAAC,cAAA,gBAAuJ;UAI/ID,EAAA,CAAAE,cAAA,EAA0H;UAA1HF,EAAA,CAAAC,cAAA,gBAA0H;UACxHD,EAAA,CAAAG,SAAA,iBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,eAA6E;UAAAD,EAAA,CAAAM,MAAA,wBAAe;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAGnGJ,EAAA,CAAAC,cAAA,gBAAuB;UACrBD,EAAA,CAAAe,UAAA,MAAAgE,uCAAA,mBAiBM;UAEN/E,EAAA,CAAAe,UAAA,MAAAiE,uCAAA,kBASM;UACRhF,EAAA,CAAAI,YAAA,EAAM;;;UA3S2DJ,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAiF,iBAAA,EAAAR,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAgE,KAAA,8BAA0C;UAavGlF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,OAAAiE,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAgE,KAAA,0BACF;UAMiFlF,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAiF,iBAAA,EAAAR,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAiE,MAAA,wBAA0C;UAMzDnF,EAAA,CAAAO,SAAA,GAAgF;UAAhFP,EAAA,CAAAiF,iBAAA,EAAAR,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAkE,UAAA,IAAAX,GAAA,CAAAjD,UAAA,CAAAiD,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAkE,UAAA,yBAAgF;UAQ9IpF,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAY,UAAA,YAAA6D,GAAA,CAAAY,cAAA,GAA4B;UAChCrF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,MAAAiE,GAAA,CAAAa,gBAAA,QACF;UAuBEtF,EAAA,CAAAO,SAAA,IACF;UADEP,EAAA,CAAAQ,kBAAA,OAAAiE,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAqE,WAAA,uDACF;UAeIvF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAwF,kBAAA,QAAAf,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,kBAAAsD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,CAAAsE,MAAA,sBAAAhB,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,kBAAAsD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,CAAAsE,MAAA,6BACF;UAIEzF,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAY,UAAA,UAAA6D,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,kBAAAsD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,CAAAsE,MAAA,MAAkC;UA+BlCzF,EAAA,CAAAO,SAAA,GAAuD;UAAvDP,EAAA,CAAAY,UAAA,WAAA6D,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,KAAAsD,GAAA,CAAAvD,MAAA,CAAAC,QAAA,CAAAsE,MAAA,OAAuD;UAyBxDzF,EAAA,CAAAO,SAAA,IAA0D;UAA1DP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA0F,eAAA,KAAAC,GAAA,EAAAlB,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA4D,GAAA,EAA0D;UAU1D9E,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA4F,eAAA,KAAAC,GAAA,EAAyC,gBAAA7F,EAAA,CAAA0F,eAAA,KAAAI,GAAA,EAAArB,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA4D,GAAA;UAgDtC9E,EAAA,CAAAO,SAAA,IACF;UADEP,EAAA,CAAAwF,kBAAA,OAAAf,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA6E,eAAA,kBAAAtB,GAAA,CAAAvD,MAAA,CAAA6E,eAAA,CAAAN,MAAA,cAAAhB,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA8E,cAAA,YACF;UAIKhG,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAiG,WAAA,UAAAxB,GAAA,CAAAyB,qBAAA,QAAyC;UAGxClG,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,kBAAA,KAAAiE,GAAA,CAAAyB,qBAAA,2BAAuC;UACvClG,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAAQ,kBAAA,KAAAiE,GAAA,CAAA0B,gBAAA,sBAAuC;UAWkCnG,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAiF,iBAAA,EAAAR,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA6E,eAAA,kBAAAtB,GAAA,CAAAvD,MAAA,CAAA6E,eAAA,CAAAN,MAAA,OAA0C;UAexDzF,EAAA,CAAAO,SAAA,IAA4E;UAA5EP,EAAA,CAAAiF,iBAAA,GAAAR,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA8E,cAAA,YAAAvB,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAA6E,eAAA,kBAAAtB,GAAA,CAAAvD,MAAA,CAAA6E,eAAA,CAAAN,MAAA,QAA4E;UAenEzF,EAAA,CAAAO,SAAA,IAA8B;UAA9BP,EAAA,CAAAQ,kBAAA,KAAAiE,GAAA,CAAAyB,qBAAA,QAA8B;UA0BhFlG,EAAA,CAAAO,SAAA,IAA+B;UAA/BP,EAAA,CAAAY,UAAA,aAAA6D,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAkF,cAAA,KAAApG,EAAA,CAAA4F,eAAA,KAAAS,GAAA,EAA+B;UAmBnDrG,EAAA,CAAAO,SAAA,GAAmE;UAAnEP,EAAA,CAAAY,UAAA,WAAA6D,GAAA,CAAAvD,MAAA,kBAAAuD,GAAA,CAAAvD,MAAA,CAAAkF,cAAA,KAAA3B,GAAA,CAAAvD,MAAA,CAAAkF,cAAA,CAAAX,MAAA,OAAmE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}