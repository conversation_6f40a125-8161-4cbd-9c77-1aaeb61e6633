{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let AddProjectComponent = class AddProjectComponent {\n  constructor(fb, projetService, router, authService) {\n    this.fb = fb;\n    this.projetService = projetService;\n    this.router = router;\n    this.authService = authService;\n    this.selectedFiles = [];\n    this.isSubmitting = false;\n    this.projetForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      dateLimite: ['', Validators.required],\n      fichiers: [null],\n      groupe: ['', Validators.required] // ← champ pour l'ID du groupe\n    });\n  }\n\n  onFileChange(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = Array.from(input.files);\n    }\n  }\n  onSubmit() {\n    if (this.projetForm.invalid) return;\n    this.isSubmitting = true;\n    console.log('Soumission du formulaire de projet');\n    const formData = new FormData();\n    formData.append('titre', this.projetForm.value.titre);\n    formData.append('description', this.projetForm.value.description || '');\n    formData.append('dateLimite', this.projetForm.value.dateLimite);\n    formData.append('groupe', this.projetForm.value.groupe);\n    // Méthode 1: Via le service d'authentification (recommandée)\n    const userIdFromService = this.authService.getCurrentUserId();\n    // Méthode 2: Via le currentUser du service\n    const currentUser = this.authService.getCurrentUser();\n    // Méthode 3: Vérification localStorage\n    const user = localStorage.getItem('user');\n    // Utiliser l'ID du service d'authentification en priorité\n    let userId = userIdFromService;\n    if (!userId && currentUser) {\n      userId = currentUser._id || currentUser.id;\n    }\n    if (!userId && user) {\n      userId = JSON.parse(user).id;\n    }\n    if (userId) {\n      formData.append('professeur', userId);\n    } else {\n      alert(\"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\");\n      return;\n    }\n    this.selectedFiles.forEach(file => {\n      formData.append('fichiers', file);\n    });\n    console.log('Données du formulaire:', {\n      titre: this.projetForm.value.titre,\n      description: this.projetForm.value.description,\n      dateLimite: this.projetForm.value.dateLimite,\n      groupe: this.projetForm.value.groupe,\n      fichiers: this.selectedFiles.map(f => f.name)\n    });\n    this.projetService.addProjet(formData).subscribe({\n      next: () => {\n        console.log('Projet ajouté avec succès');\n        alert('Projet ajouté avec succès');\n        this.router.navigate(['/admin/projects']);\n      },\n      error: err => {\n        console.error(\"Erreur lors de l'ajout du projet:\", err);\n        alert(\"Erreur lors de l'ajout du projet: \" + (err.error?.message || err.message || 'Erreur inconnue'));\n        this.isSubmitting = false;\n      },\n      complete: () => {\n        this.isSubmitting = false;\n      }\n    });\n  }\n};\nAddProjectComponent = __decorate([Component({\n  selector: 'app-add-project',\n  templateUrl: './add-project.component.html',\n  styleUrls: ['./add-project.component.css']\n})], AddProjectComponent);", "map": {"version": 3, "names": ["Component", "Validators", "AddProjectComponent", "constructor", "fb", "projetService", "router", "authService", "selectedFiles", "isSubmitting", "projetForm", "group", "titre", "required", "description", "dateLimite", "fichiers", "groupe", "onFileChange", "event", "input", "target", "files", "Array", "from", "onSubmit", "invalid", "console", "log", "formData", "FormData", "append", "value", "userIdFromService", "getCurrentUserId", "currentUser", "getCurrentUser", "user", "localStorage", "getItem", "userId", "_id", "id", "JSON", "parse", "alert", "for<PERSON>ach", "file", "map", "f", "name", "addProjet", "subscribe", "next", "navigate", "error", "err", "message", "complete", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\add-project\\add-project.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { AuthuserService } from '@app/services/authuser.service';\r\n\r\n@Component({\r\n  selector: 'app-add-project',\r\n  templateUrl: './add-project.component.html',\r\n  styleUrls: ['./add-project.component.css'],\r\n})\r\nexport class AddProjectComponent {\r\n  projetForm: FormGroup;\r\n  selectedFiles: File[] = [];\r\n  isSubmitting = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private projetService: ProjetService,\r\n    private router: Router,\r\n    private authService: AuthuserService\r\n  ) {\r\n    this.projetForm = this.fb.group({\r\n      titre: ['', Validators.required],\r\n      description: [''],\r\n      dateLimite: ['', Validators.required],\r\n      fichiers: [null],\r\n      groupe: ['', Validators.required], // ← champ pour l'ID du groupe\r\n    });\r\n  }\r\n\r\n  onFileChange(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files) {\r\n      this.selectedFiles = Array.from(input.files);\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.projetForm.invalid) return;\r\n\r\n    this.isSubmitting = true;\r\n    console.log('Soumission du formulaire de projet');\r\n\r\n    const formData = new FormData();\r\n    formData.append('titre', this.projetForm.value.titre);\r\n    formData.append('description', this.projetForm.value.description || '');\r\n    formData.append('dateLimite', this.projetForm.value.dateLimite);\r\n    formData.append('groupe', this.projetForm.value.groupe);\r\n\r\n    // Méthode 1: Via le service d'authentification (recommandée)\r\n    const userIdFromService = this.authService.getCurrentUserId();\r\n    // Méthode 2: Via le currentUser du service\r\n    const currentUser = this.authService.getCurrentUser();\r\n\r\n    // Méthode 3: Vérification localStorage\r\n    const user = localStorage.getItem('user');\r\n    // Utiliser l'ID du service d'authentification en priorité\r\n    let userId = userIdFromService;\r\n    if (!userId && currentUser) {\r\n      userId = currentUser._id || currentUser.id;\r\n    }\r\n    if (!userId && user) {\r\n      userId = JSON.parse(user).id;\r\n    }\r\n\r\n    if (userId) {\r\n      formData.append('professeur', userId);\r\n    } else {\r\n      alert(\r\n        \"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    this.selectedFiles.forEach((file) => {\r\n      formData.append('fichiers', file);\r\n    });\r\n\r\n    console.log('Données du formulaire:', {\r\n      titre: this.projetForm.value.titre,\r\n      description: this.projetForm.value.description,\r\n      dateLimite: this.projetForm.value.dateLimite,\r\n      groupe: this.projetForm.value.groupe,\r\n      fichiers: this.selectedFiles.map((f) => f.name),\r\n    });\r\n\r\n    this.projetService.addProjet(formData).subscribe({\r\n      next: () => {\r\n        console.log('Projet ajouté avec succès');\r\n        alert('Projet ajouté avec succès');\r\n        this.router.navigate(['/admin/projects']);\r\n      },\r\n      error: (err) => {\r\n        console.error(\"Erreur lors de l'ajout du projet:\", err);\r\n        alert(\r\n          \"Erreur lors de l'ajout du projet: \" +\r\n            (err.error?.message || err.message || 'Erreur inconnue')\r\n        );\r\n        this.isSubmitting = false;\r\n      },\r\n      complete: () => {\r\n        this.isSubmitting = false;\r\n      },\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAAiCC,UAAU,QAAQ,gBAAgB;AAU5D,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAK9BC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,MAAc,EACdC,WAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAPrB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAG,KAAK;IAQlB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAEX,UAAU,CAACY,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC,EAAE,EAAEd,UAAU,CAACY,QAAQ,CAAC;MACrCG,QAAQ,EAAE,CAAC,IAAI,CAAC;MAChBC,MAAM,EAAE,CAAC,EAAE,EAAEhB,UAAU,CAACY,QAAQ,CAAC,CAAE;KACpC,CAAC;EACJ;;EAEAK,YAAYA,CAACC,KAAY;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE;MACf,IAAI,CAACd,aAAa,GAAGe,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC;;EAEhD;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,UAAU,CAACgB,OAAO,EAAE;IAE7B,IAAI,CAACjB,YAAY,GAAG,IAAI;IACxBkB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACpB,KAAK,CAAC;IACrDiB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAAClB,WAAW,IAAI,EAAE,CAAC;IACvEe,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACjB,UAAU,CAAC;IAC/Dc,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACf,MAAM,CAAC;IAEvD;IACA,MAAMgB,iBAAiB,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,gBAAgB,EAAE;IAC7D;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,cAAc,EAAE;IAErD;IACA,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACzC;IACA,IAAIC,MAAM,GAAGP,iBAAiB;IAC9B,IAAI,CAACO,MAAM,IAAIL,WAAW,EAAE;MAC1BK,MAAM,GAAGL,WAAW,CAACM,GAAG,IAAIN,WAAW,CAACO,EAAE;;IAE5C,IAAI,CAACF,MAAM,IAAIH,IAAI,EAAE;MACnBG,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACP,IAAI,CAAC,CAACK,EAAE;;IAG9B,IAAIF,MAAM,EAAE;MACVX,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAES,MAAM,CAAC;KACtC,MAAM;MACLK,KAAK,CACH,8EAA8E,CAC/E;MACD;;IAGF,IAAI,CAACrC,aAAa,CAACsC,OAAO,CAAEC,IAAI,IAAI;MAClClB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEgB,IAAI,CAAC;IACnC,CAAC,CAAC;IAEFpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpChB,KAAK,EAAE,IAAI,CAACF,UAAU,CAACsB,KAAK,CAACpB,KAAK;MAClCE,WAAW,EAAE,IAAI,CAACJ,UAAU,CAACsB,KAAK,CAAClB,WAAW;MAC9CC,UAAU,EAAE,IAAI,CAACL,UAAU,CAACsB,KAAK,CAACjB,UAAU;MAC5CE,MAAM,EAAE,IAAI,CAACP,UAAU,CAACsB,KAAK,CAACf,MAAM;MACpCD,QAAQ,EAAE,IAAI,CAACR,aAAa,CAACwC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI;KAC/C,CAAC;IAEF,IAAI,CAAC7C,aAAa,CAAC8C,SAAS,CAACtB,QAAQ,CAAC,CAACuB,SAAS,CAAC;MAC/CC,IAAI,EAAEA,CAAA,KAAK;QACT1B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCiB,KAAK,CAAC,2BAA2B,CAAC;QAClC,IAAI,CAACvC,MAAM,CAACgD,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MAC3C,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb7B,OAAO,CAAC4B,KAAK,CAAC,mCAAmC,EAAEC,GAAG,CAAC;QACvDX,KAAK,CACH,oCAAoC,IACjCW,GAAG,CAACD,KAAK,EAAEE,OAAO,IAAID,GAAG,CAACC,OAAO,IAAI,iBAAiB,CAAC,CAC3D;QACD,IAAI,CAAChD,YAAY,GAAG,KAAK;MAC3B,CAAC;MACDiD,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACjD,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;CACD;AA/FYP,mBAAmB,GAAAyD,UAAA,EAL/B3D,SAAS,CAAC;EACT4D,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,6BAA6B;CAC1C,CAAC,C,EACW5D,mBAAmB,CA+F/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}