{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction AddProjectComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" Titre est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProjectComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" Description est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProjectComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" Date limite est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddProjectComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" Groupe est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"opacity-50 cursor-not-allowed\": a0\n  };\n};\nexport class AddProjectComponent {\n  constructor(fb, projetService, router, authService) {\n    this.fb = fb;\n    this.projetService = projetService;\n    this.router = router;\n    this.authService = authService;\n    this.selectedFiles = [];\n    this.isSubmitting = false;\n    this.projetForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      dateLimite: ['', Validators.required],\n      fichiers: [null],\n      groupe: ['', Validators.required] // ← champ pour l'ID du groupe\n    });\n  }\n\n  onFileChange(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = Array.from(input.files);\n    }\n  }\n  onSubmit() {\n    if (this.projetForm.invalid) return;\n    this.isSubmitting = true;\n    console.log('Soumission du formulaire de projet');\n    const formData = new FormData();\n    formData.append('titre', this.projetForm.value.titre);\n    formData.append('description', this.projetForm.value.description || '');\n    formData.append('dateLimite', this.projetForm.value.dateLimite);\n    formData.append('groupe', this.projetForm.value.groupe);\n    // Méthode 1: Via le service d'authentification (recommandée)\n    const userIdFromService = this.authService.getCurrentUserId();\n    // Méthode 2: Via le currentUser du service\n    const currentUser = this.authService.getCurrentUser();\n    // Méthode 3: Vérification localStorage\n    const user = localStorage.getItem('user');\n    // Utiliser l'ID du service d'authentification en priorité\n    let userId = userIdFromService;\n    if (!userId && currentUser) {\n      userId = currentUser._id || currentUser.id;\n    }\n    if (!userId && user) {\n      userId = JSON.parse(user).id;\n    }\n    if (userId) {\n      formData.append('professeur', userId);\n    } else {\n      alert(\"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\");\n      return;\n    }\n    this.selectedFiles.forEach(file => {\n      formData.append('fichiers', file);\n    });\n    console.log('Données du formulaire:', {\n      titre: this.projetForm.value.titre,\n      description: this.projetForm.value.description,\n      dateLimite: this.projetForm.value.dateLimite,\n      groupe: this.projetForm.value.groupe,\n      fichiers: this.selectedFiles.map(f => f.name)\n    });\n    this.projetService.addProjet(formData).subscribe({\n      next: () => {\n        console.log('Projet ajouté avec succès');\n        alert('Projet ajouté avec succès');\n        this.router.navigate(['/admin/projects']);\n      },\n      error: err => {\n        console.error(\"Erreur lors de l'ajout du projet:\", err);\n        alert(\"Erreur lors de l'ajout du projet: \" + (err.error?.message || err.message || 'Erreur inconnue'));\n        this.isSubmitting = false;\n      },\n      complete: () => {\n        this.isSubmitting = false;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AddProjectComponent_Factory(t) {\n      return new (t || AddProjectComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddProjectComponent,\n      selectors: [[\"app-add-project\"]],\n      decls: 59,\n      vars: 9,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"overflow-hidden\"], [1, \"p-6\", \"md:p-8\"], [\"enctype\", \"multipart/form-data\", 1, \"space-y-6\", 3, \"formGroup\", \"ngSubmit\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"type\", \"text\", \"id\", \"titre\", \"formControlName\", \"titre\", \"placeholder\", \"Titre du projet\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"transition-all\"], [\"class\", \"text-[#ff6b69] text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"id\", \"description\", \"formControlName\", \"description\", \"placeholder\", \"Description du projet\", \"rows\", \"4\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"transition-all\"], [\"for\", \"dateLimite\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"type\", \"date\", \"id\", \"dateLimite\", \"formControlName\", \"dateLimite\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"transition-all\"], [\"for\", \"fichiers\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"w-full\", \"px-4\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"bg-white\", \"focus:outline-none\", 3, \"change\"], [\"for\", \"groupe\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"mb-1\"], [\"id\", \"groupe\", \"formControlName\", \"groupe\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"focus:border-[#7826b5]\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"bg-white\", \"transition-all\"], [\"value\", \"\"], [\"value\", \"2cinfo1\"], [\"value\", \"2cinfo2\"], [\"value\", \"2cinfo3\"], [\"type\", \"submit\", 1, \"w-full\", \"bg-[#7826b5]\", \"hover:bg-[#4f5fad]\", \"text-white\", \"font-bold\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"focus:ring-offset-2\", 3, \"disabled\", \"ngClass\"], [1, \"text-[#ff6b69]\", \"text-sm\", \"mt-1\"]],\n      template: function AddProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n          i0.ɵɵtext(5, \"Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 5);\n          i0.ɵɵelement(7, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9, \"Nouveau projet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(13, \"svg\", 11);\n          i0.ɵɵelement(14, \"path\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(15, \"div\")(16, \"h1\", 13);\n          i0.ɵɵtext(17, \" Cr\\u00E9er un nouveau projet \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"p\", 14);\n          i0.ɵɵtext(19, \" Ajoutez un projet pour organiser le travail de vos \\u00E9tudiants \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(20, \"div\", 15)(21, \"div\", 16)(22, \"div\", 17)(23, \"form\", 18);\n          i0.ɵɵlistener(\"ngSubmit\", function AddProjectComponent_Template_form_ngSubmit_23_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(24, \"div\")(25, \"label\", 19);\n          i0.ɵɵtext(26, \"Titre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"input\", 20);\n          i0.ɵɵtemplate(28, AddProjectComponent_div_28_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\")(30, \"label\", 22);\n          i0.ɵɵtext(31, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"textarea\", 23);\n          i0.ɵɵtemplate(33, AddProjectComponent_div_33_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\")(35, \"label\", 24);\n          i0.ɵɵtext(36, \"Date limite\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 25);\n          i0.ɵɵtemplate(38, AddProjectComponent_div_38_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\")(40, \"label\", 26);\n          i0.ɵɵtext(41, \"Fichiers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"input\", 27);\n          i0.ɵɵlistener(\"change\", function AddProjectComponent_Template_input_change_42_listener($event) {\n            return ctx.onFileChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\")(44, \"label\", 28);\n          i0.ɵɵtext(45, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"select\", 29)(47, \"option\", 30);\n          i0.ɵɵtext(48, \"-- Choisir un groupe --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 31);\n          i0.ɵɵtext(50, \"2cinfo1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 32);\n          i0.ɵɵtext(52, \"2cinfo2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"option\", 33);\n          i0.ɵɵtext(54, \"2cinfo3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(55, AddProjectComponent_div_55_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\")(57, \"button\", 34);\n          i0.ɵɵtext(58, \" Ajouter \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"formGroup\", ctx.projetForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.projetForm.invalid)(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.projetForm.invalid));\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJhZGQtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvYWRkLXByb2plY3QvYWRkLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0Esd0tBQXdLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AddProjectComponent", "constructor", "fb", "projetService", "router", "authService", "selectedFiles", "isSubmitting", "projetForm", "group", "titre", "required", "description", "dateLimite", "fichiers", "groupe", "onFileChange", "event", "input", "target", "files", "Array", "from", "onSubmit", "invalid", "console", "log", "formData", "FormData", "append", "value", "userIdFromService", "getCurrentUserId", "currentUser", "getCurrentUser", "user", "localStorage", "getItem", "userId", "_id", "id", "JSON", "parse", "alert", "for<PERSON>ach", "file", "map", "f", "name", "addProjet", "subscribe", "next", "navigate", "error", "err", "message", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProjetService", "i3", "Router", "i4", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "AddProjectComponent_Template", "rf", "ctx", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵnamespaceHTML", "ɵɵlistener", "AddProjectComponent_Template_form_ngSubmit_23_listener", "ɵɵtemplate", "AddProjectComponent_div_28_Template", "AddProjectComponent_div_33_Template", "AddProjectComponent_div_38_Template", "AddProjectComponent_Template_input_change_42_listener", "$event", "AddProjectComponent_div_55_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "get", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "ɵɵpureFunction1", "_c0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\add-project\\add-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\add-project\\add-project.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { AuthuserService } from '@app/services/authuser.service';\r\n\r\n@Component({\r\n  selector: 'app-add-project',\r\n  templateUrl: './add-project.component.html',\r\n  styleUrls: ['./add-project.component.css'],\r\n})\r\nexport class AddProjectComponent {\r\n  projetForm: FormGroup;\r\n  selectedFiles: File[] = [];\r\n  isSubmitting = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private projetService: ProjetService,\r\n    private router: Router,\r\n    private authService: AuthuserService\r\n  ) {\r\n    this.projetForm = this.fb.group({\r\n      titre: ['', Validators.required],\r\n      description: [''],\r\n      dateLimite: ['', Validators.required],\r\n      fichiers: [null],\r\n      groupe: ['', Validators.required], // ← champ pour l'ID du groupe\r\n    });\r\n  }\r\n\r\n  onFileChange(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files) {\r\n      this.selectedFiles = Array.from(input.files);\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.projetForm.invalid) return;\r\n\r\n    this.isSubmitting = true;\r\n    console.log('Soumission du formulaire de projet');\r\n\r\n    const formData = new FormData();\r\n    formData.append('titre', this.projetForm.value.titre);\r\n    formData.append('description', this.projetForm.value.description || '');\r\n    formData.append('dateLimite', this.projetForm.value.dateLimite);\r\n    formData.append('groupe', this.projetForm.value.groupe);\r\n\r\n    // Méthode 1: Via le service d'authentification (recommandée)\r\n    const userIdFromService = this.authService.getCurrentUserId();\r\n    // Méthode 2: Via le currentUser du service\r\n    const currentUser = this.authService.getCurrentUser();\r\n\r\n    // Méthode 3: Vérification localStorage\r\n    const user = localStorage.getItem('user');\r\n    // Utiliser l'ID du service d'authentification en priorité\r\n    let userId = userIdFromService;\r\n    if (!userId && currentUser) {\r\n      userId = currentUser._id || currentUser.id;\r\n    }\r\n    if (!userId && user) {\r\n      userId = JSON.parse(user).id;\r\n    }\r\n\r\n    if (userId) {\r\n      formData.append('professeur', userId);\r\n    } else {\r\n      alert(\r\n        \"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    this.selectedFiles.forEach((file) => {\r\n      formData.append('fichiers', file);\r\n    });\r\n\r\n    console.log('Données du formulaire:', {\r\n      titre: this.projetForm.value.titre,\r\n      description: this.projetForm.value.description,\r\n      dateLimite: this.projetForm.value.dateLimite,\r\n      groupe: this.projetForm.value.groupe,\r\n      fichiers: this.selectedFiles.map((f) => f.name),\r\n    });\r\n\r\n    this.projetService.addProjet(formData).subscribe({\r\n      next: () => {\r\n        console.log('Projet ajouté avec succès');\r\n        alert('Projet ajouté avec succès');\r\n        this.router.navigate(['/admin/projects']);\r\n      },\r\n      error: (err) => {\r\n        console.error(\"Erreur lors de l'ajout du projet:\", err);\r\n        alert(\r\n          \"Erreur lors de l'ajout du projet: \" +\r\n            (err.error?.message || err.message || 'Erreur inconnue')\r\n        );\r\n        this.isSubmitting = false;\r\n      },\r\n      complete: () => {\r\n        this.isSubmitting = false;\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n\r\n    <!-- Header moderne avec breadcrumb -->\r\n    <div class=\"mb-8\">\r\n      <nav class=\"flex items-center space-x-2 text-sm text-text dark:text-dark-text-secondary mb-4\">\r\n        <a routerLink=\"/admin/projects/list-project\" class=\"hover:text-primary dark:hover:text-dark-accent-primary transition-colors\">Projets</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <span class=\"text-primary dark:text-dark-accent-primary font-medium\">Nouveau projet</span>\r\n      </nav>\r\n\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\r\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\r\n              Créer un nouveau projet\r\n            </h1>\r\n            <p class=\"text-text dark:text-dark-text-secondary\">\r\n              Ajoutez un projet pour organiser le travail de vos étudiants\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Formulaire moderne -->\r\n    <div class=\"max-w-4xl mx-auto\">\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 overflow-hidden\">\r\n\r\n    <!-- Form -->\r\n    <div class=\"p-6 md:p-8\">\r\n      <form\r\n        [formGroup]=\"projetForm\"\r\n        (ngSubmit)=\"onSubmit()\"\r\n        enctype=\"multipart/form-data\"\r\n        class=\"space-y-6\"\r\n      >\r\n        <!-- Titre -->\r\n        <div>\r\n          <label\r\n            for=\"titre\"\r\n            class=\"block text-sm font-medium text-[#6d6870] mb-1\"\r\n            >Titre</label\r\n          >\r\n          <input\r\n            type=\"text\"\r\n            id=\"titre\"\r\n            formControlName=\"titre\"\r\n            placeholder=\"Titre du projet\"\r\n            class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] focus:border-[#7826b5] focus:ring-2 focus:ring-[#dac4ea] transition-all\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              projetForm.get('titre')?.invalid &&\r\n              projetForm.get('titre')?.touched\r\n            \"\r\n            class=\"text-[#ff6b69] text-sm mt-1\"\r\n          >\r\n            Titre est requis\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Description -->\r\n        <div>\r\n          <label\r\n            for=\"description\"\r\n            class=\"block text-sm font-medium text-[#6d6870] mb-1\"\r\n            >Description</label\r\n          >\r\n          <textarea\r\n            id=\"description\"\r\n            formControlName=\"description\"\r\n            placeholder=\"Description du projet\"\r\n            rows=\"4\"\r\n            class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] focus:border-[#7826b5] focus:ring-2 focus:ring-[#dac4ea] transition-all\"\r\n          ></textarea>\r\n          <div\r\n            *ngIf=\"\r\n              projetForm.get('description')?.invalid &&\r\n              projetForm.get('description')?.touched\r\n            \"\r\n            class=\"text-[#ff6b69] text-sm mt-1\"\r\n          >\r\n            Description est requise\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Date limite -->\r\n        <div>\r\n          <label\r\n            for=\"dateLimite\"\r\n            class=\"block text-sm font-medium text-[#6d6870] mb-1\"\r\n            >Date limite</label\r\n          >\r\n          <input\r\n            type=\"date\"\r\n            id=\"dateLimite\"\r\n            formControlName=\"dateLimite\"\r\n            class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] focus:border-[#7826b5] focus:ring-2 focus:ring-[#dac4ea] transition-all\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              projetForm.get('dateLimite')?.invalid &&\r\n              projetForm.get('dateLimite')?.touched\r\n            \"\r\n            class=\"text-[#ff6b69] text-sm mt-1\"\r\n          >\r\n            Date limite est requise\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Fichiers -->\r\n        <div>\r\n          <label\r\n            for=\"fichiers\"\r\n            class=\"block text-sm font-medium text-[#6d6870] mb-1\"\r\n            >Fichiers</label\r\n          >\r\n          <input\r\n            type=\"file\"\r\n            id=\"fichiers\"\r\n            (change)=\"onFileChange($event)\"\r\n            multiple\r\n            class=\"w-full px-4 py-2 rounded-lg border border-[#bdc6cc] bg-white focus:outline-none\"\r\n          />\r\n        </div>\r\n\r\n        <!-- Groupe -->\r\n        <div>\r\n          <label\r\n            for=\"groupe\"\r\n            class=\"block text-sm font-medium text-[#6d6870] mb-1\"\r\n            >Groupe</label\r\n          >\r\n          <select\r\n            id=\"groupe\"\r\n            formControlName=\"groupe\"\r\n            class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] focus:border-[#7826b5] focus:ring-2 focus:ring-[#dac4ea] bg-white transition-all\"\r\n          >\r\n            <option value=\"\">-- Choisir un groupe --</option>\r\n            <option value=\"2cinfo1\">2cinfo1</option>\r\n            <option value=\"2cinfo2\">2cinfo2</option>\r\n            <option value=\"2cinfo3\">2cinfo3</option>\r\n          </select>\r\n          <div\r\n            *ngIf=\"\r\n              projetForm.get('groupe')?.invalid &&\r\n              projetForm.get('groupe')?.touched\r\n            \"\r\n            class=\"text-[#ff6b69] text-sm mt-1\"\r\n          >\r\n            Groupe est requis\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Submit Button -->\r\n        <div>\r\n          <button\r\n            type=\"submit\"\r\n            class=\"w-full bg-[#7826b5] hover:bg-[#4f5fad] text-white font-bold py-3 px-4 rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-[#dac4ea] focus:ring-offset-2\"\r\n            [disabled]=\"projetForm.invalid\"\r\n            [ngClass]=\"{ 'opacity-50 cursor-not-allowed': projetForm.invalid }\"\r\n          >\r\n            Ajouter\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;IC0DzDC,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBNH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoCNH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;ADrJhB,OAAM,MAAOC,mBAAmB;EAK9BC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,MAAc,EACdC,WAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAPrB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAG,KAAK;IAQlB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAEf,UAAU,CAACgB,QAAQ,CAAC;MAChCC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC,EAAE,EAAElB,UAAU,CAACgB,QAAQ,CAAC;MACrCG,QAAQ,EAAE,CAAC,IAAI,CAAC;MAChBC,MAAM,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACgB,QAAQ,CAAC,CAAE;KACpC,CAAC;EACJ;;EAEAK,YAAYA,CAACC,KAAY;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE;MACf,IAAI,CAACd,aAAa,GAAGe,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC;;EAEhD;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,UAAU,CAACgB,OAAO,EAAE;IAE7B,IAAI,CAACjB,YAAY,GAAG,IAAI;IACxBkB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACpB,KAAK,CAAC;IACrDiB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAAClB,WAAW,IAAI,EAAE,CAAC;IACvEe,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACjB,UAAU,CAAC;IAC/Dc,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACrB,UAAU,CAACsB,KAAK,CAACf,MAAM,CAAC;IAEvD;IACA,MAAMgB,iBAAiB,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,gBAAgB,EAAE;IAC7D;IACA,MAAMC,WAAW,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,cAAc,EAAE;IAErD;IACA,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACzC;IACA,IAAIC,MAAM,GAAGP,iBAAiB;IAC9B,IAAI,CAACO,MAAM,IAAIL,WAAW,EAAE;MAC1BK,MAAM,GAAGL,WAAW,CAACM,GAAG,IAAIN,WAAW,CAACO,EAAE;;IAE5C,IAAI,CAACF,MAAM,IAAIH,IAAI,EAAE;MACnBG,MAAM,GAAGG,IAAI,CAACC,KAAK,CAACP,IAAI,CAAC,CAACK,EAAE;;IAG9B,IAAIF,MAAM,EAAE;MACVX,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAES,MAAM,CAAC;KACtC,MAAM;MACLK,KAAK,CACH,8EAA8E,CAC/E;MACD;;IAGF,IAAI,CAACrC,aAAa,CAACsC,OAAO,CAAEC,IAAI,IAAI;MAClClB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEgB,IAAI,CAAC;IACnC,CAAC,CAAC;IAEFpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpChB,KAAK,EAAE,IAAI,CAACF,UAAU,CAACsB,KAAK,CAACpB,KAAK;MAClCE,WAAW,EAAE,IAAI,CAACJ,UAAU,CAACsB,KAAK,CAAClB,WAAW;MAC9CC,UAAU,EAAE,IAAI,CAACL,UAAU,CAACsB,KAAK,CAACjB,UAAU;MAC5CE,MAAM,EAAE,IAAI,CAACP,UAAU,CAACsB,KAAK,CAACf,MAAM;MACpCD,QAAQ,EAAE,IAAI,CAACR,aAAa,CAACwC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI;KAC/C,CAAC;IAEF,IAAI,CAAC7C,aAAa,CAAC8C,SAAS,CAACtB,QAAQ,CAAC,CAACuB,SAAS,CAAC;MAC/CC,IAAI,EAAEA,CAAA,KAAK;QACT1B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCiB,KAAK,CAAC,2BAA2B,CAAC;QAClC,IAAI,CAACvC,MAAM,CAACgD,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MAC3C,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb7B,OAAO,CAAC4B,KAAK,CAAC,mCAAmC,EAAEC,GAAG,CAAC;QACvDX,KAAK,CACH,oCAAoC,IACjCW,GAAG,CAACD,KAAK,EAAEE,OAAO,IAAID,GAAG,CAACC,OAAO,IAAI,iBAAiB,CAAC,CAC3D;QACD,IAAI,CAAChD,YAAY,GAAG,KAAK;MAC3B,CAAC;MACDiD,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACjD,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;;;uBA9FWP,mBAAmB,EAAAJ,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAA6D,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAjE,EAAA,CAAA6D,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAnE,EAAA,CAAA6D,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAnBjE,mBAAmB;MAAAkE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVhC5E,EAAA,CAAAC,cAAA,aAAiK;UAM3BD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzIH,EAAA,CAAA8E,cAAA,EAA2E;UAA3E9E,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAA+E,SAAA,cAA8F;UAChG/E,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAgF,eAAA,EAAqE;UAArEhF,EAAA,CAAAC,cAAA,cAAqE;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG5FH,EAAA,CAAAC,cAAA,cAA2J;UAGrJD,EAAA,CAAA8E,cAAA,EAAsF;UAAtF9E,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAA+E,SAAA,gBAA4G;UAC9G/E,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAgF,eAAA,EAAK;UAALhF,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAE,MAAA,sCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAmD;UACjDD,EAAA,CAAAE,MAAA,2EACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAOZH,EAAA,CAAAC,cAAA,eAA+B;UAO3BD,EAAA,CAAAiF,UAAA,sBAAAC,uDAAA;YAAA,OAAYL,GAAA,CAAAlD,QAAA,EAAU;UAAA,EAAC;UAKvB3B,EAAA,CAAAC,cAAA,WAAK;UAIAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EACP;UACDH,EAAA,CAAA+E,SAAA,iBAME;UACF/E,EAAA,CAAAmF,UAAA,KAAAC,mCAAA,kBAQM;UACRpF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAIAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EACb;UACDH,EAAA,CAAA+E,SAAA,oBAMY;UACZ/E,EAAA,CAAAmF,UAAA,KAAAE,mCAAA,kBAQM;UACRrF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAIAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EACb;UACDH,EAAA,CAAA+E,SAAA,iBAKE;UACF/E,EAAA,CAAAmF,UAAA,KAAAG,mCAAA,kBAQM;UACRtF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAIAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EACV;UACDH,EAAA,CAAAC,cAAA,iBAME;UAHAD,EAAA,CAAAiF,UAAA,oBAAAM,sDAAAC,MAAA;YAAA,OAAUX,GAAA,CAAAzD,YAAA,CAAAoE,MAAA,CAAoB;UAAA,EAAC;UAHjCxF,EAAA,CAAAG,YAAA,EAME;UAIJH,EAAA,CAAAC,cAAA,WAAK;UAIAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EACR;UACDH,EAAA,CAAAC,cAAA,kBAIC;UACkBD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACjDH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE1CH,EAAA,CAAAmF,UAAA,KAAAM,mCAAA,kBAQM;UACRzF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,WAAK;UAODD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;UApIXH,EAAA,CAAA0F,SAAA,IAAwB;UAAxB1F,EAAA,CAAA2F,UAAA,cAAAd,GAAA,CAAAjE,UAAA,CAAwB;UAoBnBZ,EAAA,CAAA0F,SAAA,GAGD;UAHC1F,EAAA,CAAA2F,UAAA,WAAAC,OAAA,GAAAf,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,4BAAAD,OAAA,CAAAhE,OAAA,OAAAgE,OAAA,GAAAf,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,4BAAAD,OAAA,CAAAE,OAAA,EAGD;UAsBC9F,EAAA,CAAA0F,SAAA,GAGD;UAHC1F,EAAA,CAAA2F,UAAA,WAAAI,OAAA,GAAAlB,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,kCAAAE,OAAA,CAAAnE,OAAA,OAAAmE,OAAA,GAAAlB,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,kCAAAE,OAAA,CAAAD,OAAA,EAGD;UAqBC9F,EAAA,CAAA0F,SAAA,GAGD;UAHC1F,EAAA,CAAA2F,UAAA,WAAAK,OAAA,GAAAnB,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,iCAAAG,OAAA,CAAApE,OAAA,OAAAoE,OAAA,GAAAnB,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,iCAAAG,OAAA,CAAAF,OAAA,EAGD;UAyCC9F,EAAA,CAAA0F,SAAA,IAGD;UAHC1F,EAAA,CAAA2F,UAAA,WAAAM,OAAA,GAAApB,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,6BAAAI,OAAA,CAAArE,OAAA,OAAAqE,OAAA,GAAApB,GAAA,CAAAjE,UAAA,CAAAiF,GAAA,6BAAAI,OAAA,CAAAH,OAAA,EAGD;UAYA9F,EAAA,CAAA0F,SAAA,GAA+B;UAA/B1F,EAAA,CAAA2F,UAAA,aAAAd,GAAA,CAAAjE,UAAA,CAAAgB,OAAA,CAA+B,YAAA5B,EAAA,CAAAkG,eAAA,IAAAC,GAAA,EAAAtB,GAAA,CAAAjE,UAAA,CAAAgB,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}