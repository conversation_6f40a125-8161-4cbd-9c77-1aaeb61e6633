/* Styles pour le composant de liste des évaluations */
.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.error-message {
  color: #dc3545;
  margin-top: 0.25rem;
}

/* Animations pour les cartes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Effet glassmorphism */
.glass-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animation pour les cartes d'évaluation */
.evaluation-card {
  animation: fadeInUp 0.6s ease-out;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.evaluation-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .evaluation-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Animation pour les boutons */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Styles pour les avatars */
.avatar-gradient {
  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);
  transition: all 0.3s ease;
}

.dark .avatar-gradient {
  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);
}

.avatar-gradient:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(79, 95, 173, 0.3);
}

.dark .avatar-gradient:hover {
  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.3);
}

/* Animation pour les badges de score */
.score-badge {
  animation: scaleIn 0.4s ease-out;
  transition: all 0.2s ease;
}

.score-badge:hover {
  transform: scale(1.05);
}

/* Styles pour les filtres */
.filter-select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-select:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 95, 173, 0.15);
}

.dark .filter-select:focus {
  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.15);
}

/* Animation pour le header */
.header-gradient {
  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);
  animation: slideInRight 0.8s ease-out;
}

.dark .header-gradient {
  background: linear-gradient(135deg, #00f7ff 0%, #9d4edd 100%);
}

/* Responsive design amélioré */
@media (max-width: 768px) {
  .evaluation-card {
    margin-bottom: 1rem;
  }

  .btn-modern {
    width: 100%;
    justify-content: center;
  }

  .filter-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Animation pour les icônes */
.icon-hover {
  transition: transform 0.2s ease;
}

.icon-hover:hover {
  transform: scale(1.1) rotate(5deg);
}

/* Styles pour les tooltips */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* Animation pour les états de chargement */
.loading-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Styles pour les états vides */
.empty-state {
  animation: fadeInUp 0.8s ease-out;
}

/* Amélioration des focus states pour l'accessibilité */
.focus-visible:focus {
  outline: 2px solid #4f5fad;
  outline-offset: 2px;
}

.dark .focus-visible:focus {
  outline: 2px solid #00f7ff;
}

/* Styles spécifiques pour les boutons de suppression */
.delete-btn {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

.dark .delete-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.dark .delete-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}