<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary relative">
  <!-- Background decorative elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl"></div>
    <div class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl"></div>
  </div>

  <div class="container mx-auto px-4 py-8 relative z-10">

    <!-- Header moderne avec breadcrumb -->
    <div class="mb-8">
      <nav class="flex items-center space-x-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4">
        <a routerLink="/projects" class="hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors">Mes Projets</a>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
        <span class="text-[#4f5fad] dark:text-[#6d78c9] font-medium">{{ projet?.titre || 'Détails du projet' }}</span>
      </nav>

      <div class="bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div class="flex items-center space-x-4 mb-6 lg:mb-0">
            <div class="h-16 w-16 rounded-2xl bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] flex items-center justify-center shadow-lg">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent">
                {{ projet?.titre || 'Chargement...' }}
              </h1>
              <div class="flex items-center space-x-4 mt-2">
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <span class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">{{ projet?.groupe || 'Tous les groupes' }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">{{ projet?.dateLimite | date : "dd/MM/yyyy" || 'Pas de date limite' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Badge de statut -->
          <div class="flex items-center space-x-3">
            <span [ngClass]="getStatusClass()" class="px-4 py-2 rounded-xl text-sm font-medium">
              {{ getStatusText() }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div *ngIf="isLoading" class="flex justify-center my-12">
      <div class="relative">
        <div class="w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin"></div>
        <!-- Glow effect -->
        <div class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10"></div>
      </div>
    </div>

    <!-- Contenu principal -->
    <div *ngIf="!isLoading" class="grid grid-cols-1 lg:grid-cols-3 gap-8">

      <!-- Carte principale du projet -->
      <div class="lg:col-span-2 space-y-6">

        <!-- Description du projet -->
        <div class="bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]">
          <div class="flex items-center space-x-3 mb-4">
            <div class="bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg">
              <svg class="w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]">Description du projet</h3>
          </div>
          <div class="prose prose-gray dark:prose-invert max-w-none">
            <p class="text-[#6d6870] dark:text-[#a0a0a0] leading-relaxed">
              {{ projet?.description || 'Aucune description fournie pour ce projet.' }}
            </p>
          </div>
        </div>

        <!-- Fichiers du projet -->
        <div class="bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]">
          <div class="flex items-center space-x-3 mb-4">
            <div class="bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg">
              <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]">
              Ressources du projet
              <span class="text-sm font-normal text-[#6d6870] dark:text-[#a0a0a0] ml-2">
                ({{ projet?.fichiers?.length || 0 }} fichier{{ (projet?.fichiers?.length || 0) > 1 ? 's' : '' }})
              </span>
            </h3>
          </div>

          <div *ngIf="projet?.fichiers?.length > 0" class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div *ngFor="let file of projet.fichiers" class="group">
              <div class="bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-xl p-4 border border-[#edf1f4] dark:border-[#2a2a2a] hover:border-[#4f5fad] dark:hover:border-[#6d78c9] transition-all duration-200 hover:shadow-md">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3 flex-1 min-w-0">
                    <div class="bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg group-hover:scale-110 transition-transform">
                      <svg class="w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-[#3d4a85] dark:text-[#6d78c9] truncate">
                        {{ getFileName(file) }}
                      </p>
                      <p class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">Document de projet</p>
                    </div>
                  </div>
                  <a [href]="getFileUrl(file)" download
                     class="ml-3 px-3 py-2 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] rounded-lg transition-all duration-200 text-xs font-medium group-hover:scale-105">
                    <div class="flex items-center space-x-1">
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                      </svg>
                      <span>Télécharger</span>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="!projet?.fichiers || projet.fichiers.length === 0" class="text-center py-8">
            <div class="bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-xl p-6">
              <div class="bg-[#edf1f4] dark:bg-[#2a2a2a] p-3 rounded-lg inline-flex items-center justify-center mb-3">
                <svg class="w-6 h-6 text-[#6d6870] dark:text-[#a0a0a0]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">Aucun fichier joint à ce projet</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar avec informations et actions -->
      <div class="space-y-6">

        <!-- Informations du projet -->
        <div class="bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden">
          <div class="bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] p-6 text-white">
            <div class="flex items-center space-x-3">
              <div class="bg-white/20 p-2 rounded-lg">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold">Informations</h3>
                <p class="text-sm text-white/80">Détails du projet</p>
              </div>
            </div>
          </div>

          <div class="p-6 space-y-4">
            <!-- Date limite -->
            <div class="flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl">
              <div class="flex items-center space-x-3">
                <div class="bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg">
                  <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider">Date limite</p>
                  <p class="text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]">{{ projet?.dateLimite | date : "dd/MM/yyyy" || 'Non définie' }}</p>
                </div>
              </div>
            </div>

            <!-- Temps restant -->
            <div class="flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl">
              <div class="flex items-center space-x-3">
                <div class="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg">
                  <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider">Temps restant</p>
                  <p class="text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]">{{ getRemainingDays() }} jours</p>
                </div>
              </div>
            </div>

            <!-- Groupe -->
            <div class="flex items-center justify-between p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl">
              <div class="flex items-center space-x-3">
                <div class="bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg">
                  <svg class="w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider">Groupe cible</p>
                  <p class="text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]">{{ projet?.groupe || 'Tous les groupes' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]">
          <div class="flex items-center space-x-3 mb-4">
            <div class="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]">Actions</h3>
          </div>

          <div class="space-y-3">
            <ng-container *ngIf="hasSubmitted">
              <div class="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl">
                <div class="flex items-center space-x-3">
                  <div class="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm font-semibold text-green-800 dark:text-green-400">Projet soumis</p>
                    <p class="text-xs text-green-600 dark:text-green-500">Votre rendu a été enregistré avec succès</p>
                  </div>
                </div>
              </div>
            </ng-container>

            <ng-container *ngIf="!hasSubmitted">
              <a [routerLink]="['/projects/submit', projetId]"
                 class="block w-full px-6 py-3 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium text-center">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <span>Soumettre mon projet</span>
                </div>
              </a>
            </ng-container>

            <!-- Bouton retour -->
            <a routerLink="/projects"
               class="block w-full px-6 py-3 bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#3d4a85] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-xl transition-all duration-200 font-medium text-center">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span>Retour aux projets</span>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
