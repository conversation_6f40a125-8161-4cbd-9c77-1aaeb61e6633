<!-- Bouton flottant IA -->
<div class="fixed bottom-6 right-6 z-50">
  <!-- Bouton principal -->
  <button
    (click)="toggleChat()"
    class="group relative w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-purple-300 dark:focus:ring-purple-800"
    [class.rotate-180]="isOpen"
  >
    <!-- Glow effect -->
    <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-300"></div>
    
    <!-- Icône IA -->
    <div class="relative z-10 flex items-center justify-center w-full h-full">
      <svg *ngIf="!isOpen" class="w-7 h-7 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
      </svg>
      <svg *ngIf="isOpen" class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </div>

    <!-- Badge de notification (optionnel) -->
    <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
      <span class="text-xs text-white font-bold">!</span>
    </div>
  </button>

  <!-- Fenêtre de chat -->
  <div
    *ngIf="isOpen"
    class="absolute bottom-16 right-0 w-80 h-96 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden transform transition-all duration-300 ease-out"
    [@slideInOut]
  >
    <!-- Header du chat -->
    <div class="bg-gradient-to-r from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 p-4 text-white">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-sm">Assistant IA</h3>
            <p class="text-xs text-white/80">En ligne</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button
            (click)="clearChat()"
            class="p-1.5 hover:bg-white/20 rounded-lg transition-colors"
            title="Effacer la conversation"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
          <button
            (click)="closeChat()"
            class="p-1.5 hover:bg-white/20 rounded-lg transition-colors"
            title="Fermer"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <div #messagesContainer class="flex-1 overflow-y-auto p-4 space-y-3 h-64">
      <div *ngFor="let message of messages; trackBy: trackByMessageId" class="flex" [ngClass]="{'justify-end': message.isUser}">
        <div
          class="max-w-xs px-3 py-2 rounded-2xl text-sm"
          [ngClass]="{
            'bg-purple-500 text-white': message.isUser,
            'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200': !message.isUser && !message.isTyping,
            'bg-gray-100 dark:bg-gray-800': message.isTyping
          }"
        >
          <!-- Message normal -->
          <div *ngIf="!message.isTyping" class="whitespace-pre-wrap">{{ message.content }}</div>
          
          <!-- Indicateur de frappe -->
          <div *ngIf="message.isTyping" class="flex items-center space-x-1 py-1">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
            </div>
            <span class="text-xs text-gray-500 ml-2">IA écrit...</span>
          </div>
          
          <!-- Timestamp -->
          <div *ngIf="!message.isTyping" class="text-xs opacity-70 mt-1" [ngClass]="{'text-right': message.isUser}">
            {{ formatTime(message.timestamp) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Messages rapides -->
    <div *ngIf="messages.length <= 1" class="px-4 pb-2">
      <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">Suggestions :</div>
      <div class="flex flex-wrap gap-1">
        <button
          *ngFor="let quickMsg of quickMessages"
          (click)="sendQuickMessage(quickMsg)"
          class="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors"
        >
          {{ quickMsg }}
        </button>
      </div>
    </div>

    <!-- Input de message -->
    <div class="p-4 border-t border-gray-200 dark:border-gray-700">
      <form [formGroup]="chatForm" (ngSubmit)="sendMessage()" class="flex items-center space-x-2">
        <div class="flex-1 relative">
          <input
            #messageInput
            type="text"
            formControlName="message"
            placeholder="Tapez votre message..."
            (keypress)="onKeyPress($event)"
            class="w-full px-3 py-2 pr-10 bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 text-sm text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400"
            [disabled]="isLoading"
          />
          <div *ngIf="isLoading" class="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div class="w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
        <button
          type="submit"
          [disabled]="chatForm.invalid || isLoading"
          class="p-2 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-xl transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </button>
      </form>
    </div>
  </div>
</div>
