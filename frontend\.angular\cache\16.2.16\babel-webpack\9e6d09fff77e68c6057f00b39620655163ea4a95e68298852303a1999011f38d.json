{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction ProjectDetailComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectDetailComponent_div_38_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 49);\n    i0.ɵɵelement(4, \"path\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\", 51);\n    i0.ɵɵtext(6, \"Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 52);\n    i0.ɵɵtext(8, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"href\", ctx_r5.getFileUrl(file_r6), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectDetailComponent_div_38_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"h2\", 43);\n    i0.ɵɵtext(2, \" Fichiers du projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵtemplate(4, ProjectDetailComponent_div_38_div_18_div_4_Template, 9, 1, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projet.fichiers);\n  }\n}\nfunction ProjectDetailComponent_div_38_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 53);\n    i0.ɵɵtext(2, \" Projet d\\u00E9j\\u00E0 soumis \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/submit\", a1];\n};\nfunction ProjectDetailComponent_div_38_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 54);\n    i0.ɵɵtext(2, \" Soumettre mon projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, ctx_r4.projetId));\n  }\n}\nfunction ProjectDetailComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"h1\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 36);\n    i0.ɵɵtext(8, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 37)(13, \"div\", 5)(14, \"h2\", 38);\n    i0.ɵɵtext(15, \" Description du projet \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 39);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, ProjectDetailComponent_div_38_div_18_Template, 5, 1, \"div\", 40);\n    i0.ɵɵelementStart(19, \"div\", 41);\n    i0.ɵɵtemplate(20, ProjectDetailComponent_div_38_ng_container_20_Template, 3, 0, \"ng-container\", 42);\n    i0.ɵɵtemplate(21, ProjectDetailComponent_div_38_ng_container_21_Template, 3, 3, \"ng-container\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.projet == null ? null : ctx_r1.projet.titre);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Groupe: \", ctx_r1.projet == null ? null : ctx_r1.projet.groupe, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Date limite: \", i0.ɵɵpipeBind2(11, 7, ctx_r1.projet == null ? null : ctx_r1.projet.dateLimite, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.projet == null ? null : ctx_r1.projet.description) || \"Aucune description fournie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers) && (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers.length) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubmitted);\n  }\n}\n// Composant pour afficher les détails d'un projet\nexport class ProjectDetailComponent {\n  constructor(route, router, projetService, rendusService, authService) {\n    this.route = route;\n    this.router = router;\n    this.projetService = projetService;\n    this.rendusService = rendusService;\n    this.authService = authService;\n    this.projetId = '';\n    this.isLoading = true;\n    this.hasSubmitted = false;\n  }\n  ngOnInit() {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n    this.checkRenduStatus();\n  }\n  loadProjetDetails() {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: projet => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      }\n    });\n  }\n  checkRenduStatus() {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (etudiantId) {\n      this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n        next: exists => {\n          console.log(exists);\n          this.hasSubmitted = exists;\n        },\n        error: err => {\n          console.error('Erreur lors de la vérification du rendu', err);\n        }\n      });\n    }\n  }\n  getFileUrl(filePath) {\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser l'endpoint API spécifique pour le téléchargement\n    return `http://localhost:3000/api/projets/download/${fileName}`;\n  }\n  static {\n    this.ɵfac = function ProjectDetailComponent_Factory(t) {\n      return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectDetailComponent,\n      selectors: [[\"app-project-detail\"]],\n      decls: 39,\n      vars: 11,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\"], [\"routerLink\", \"/projects\", 1, \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"border-t-[#4f5fad]\", \"rounded-full\", \"animate-spin\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"border-t-4\", \"border-[#4f5fad]\", \"p-6\", \"bg-white\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\"], [1, \"flex\", \"items-center\", \"mt-2\", \"text-sm\", \"text-[#6d6870]\"], [1, \"mx-2\"], [1, \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-2\"], [1, \"text-[#6d6870]\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-end\", \"mt-6\"], [4, \"ngIf\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [\"class\", \"border border-[#bdc6cc] rounded-lg p-4 hover:bg-[#edf1f4] transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"border-[#bdc6cc]\", \"rounded-lg\", \"p-4\", \"hover:bg-[#edf1f4]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-[#4f5fad]\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-[#6d6870]\", \"truncate\"], [\"download\", \"\", 1, \"text-[#4f5fad]\", \"hover:text-[#3d4a85]\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"href\"], [1, \"bg-green-100\", \"text-green-800\", \"px-4\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\"], [1, \"bg-[#4f5fad]\", \"hover:bg-[#3d4a85]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"transition-colors\", 3, \"routerLink\"]],\n      template: function ProjectDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"nav\", 6)(7, \"a\", 7);\n          i0.ɵɵtext(8, \"Mes Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(9, \"svg\", 8);\n          i0.ɵɵelement(10, \"path\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(11, \"span\", 10);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"div\", 14);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(17, \"svg\", 15);\n          i0.ɵɵelement(18, \"path\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(19, \"div\")(20, \"h1\", 17);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 18)(23, \"div\", 19);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(24, \"svg\", 20);\n          i0.ɵɵelement(25, \"path\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(26, \"span\", 22);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 19);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(29, \"svg\", 23);\n          i0.ɵɵelement(30, \"path\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(31, \"span\", 25);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"date\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(34, \"div\", 26)(35, \"span\", 27);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(37, ProjectDetailComponent_div_37_Template, 2, 0, \"div\", 28);\n          i0.ɵɵtemplate(38, ProjectDetailComponent_div_38_Template, 22, 10, \"div\", 29);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 8, ctx.projet == null ? null : ctx.projet.dateLimite, \"dd/MM/yyyy\" || \"Pas de date limite\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(), \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.RouterLink, i5.DatePipe],\n      styles: [\"\\n\\n.project-container[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background-color: #fff;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.project-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  border-bottom: 1px solid #e5e7eb;\\n  padding-bottom: 1rem;\\n}\\n\\n.project-description[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.project-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.project-meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtZGV0YWlsLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsaURBQWlEO0FBQ2pEO0VBQ0UsZUFBZTtFQUNmLHNCQUFzQjtFQUN0QixxQkFBcUI7RUFDckIsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UscUJBQXFCO0VBQ3JCLGdDQUFnQztFQUNoQyxvQkFBb0I7QUFDdEI7O0FBRUE7RUFDRSxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsZUFBZTtFQUNmLFNBQVM7RUFDVCxxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFdBQVc7QUFDYiIsImZpbGUiOiJwcm9qZWN0LWRldGFpbC5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGRlIGTDqXRhaWwgZGUgcHJvamV0ICovXHJcbi5wcm9qZWN0LWNvbnRhaW5lciB7XHJcbiAgcGFkZGluZzogMS41cmVtO1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XHJcbiAgYm9yZGVyLXJhZGl1czogMC41cmVtO1xyXG4gIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbn1cclxuXHJcbi5wcm9qZWN0LWhlYWRlciB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xyXG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4ucHJvamVjdC1kZXNjcmlwdGlvbiB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xyXG59XHJcblxyXG4ucHJvamVjdC1tZXRhIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtd3JhcDogd3JhcDtcclxuICBnYXA6IDFyZW07XHJcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xyXG59XHJcblxyXG4ucHJvamVjdC1tZXRhLWl0ZW0ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBnYXA6IDAuNXJlbTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvamVjdHMvcHJvamVjdC1kZXRhaWwvcHJvamVjdC1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxpREFBaUQ7QUFDakQ7RUFDRSxlQUFlO0VBQ2Ysc0JBQXNCO0VBQ3RCLHFCQUFxQjtFQUNyQix3Q0FBd0M7QUFDMUM7O0FBRUE7RUFDRSxxQkFBcUI7RUFDckIsZ0NBQWdDO0VBQ2hDLG9CQUFvQjtBQUN0Qjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixlQUFlO0VBQ2YsU0FBUztFQUNULHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsV0FBVztBQUNiO0FBQ0EsNDFDQUE0MUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZGUgZMODwql0YWlsIGRlIHByb2pldCAqL1xyXG4ucHJvamVjdC1jb250YWluZXIge1xyXG4gIHBhZGRpbmc6IDEuNXJlbTtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcclxuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4ucHJvamVjdC1oZWFkZXIge1xyXG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICBwYWRkaW5nLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLnByb2plY3QtZGVzY3JpcHRpb24ge1xyXG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcclxufVxyXG5cclxuLnByb2plY3QtbWV0YSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgZ2FwOiAxcmVtO1xyXG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcclxufVxyXG5cclxuLnByb2plY3QtbWV0YS1pdGVtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgZ2FwOiAwLjVyZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r5", "getFileUrl", "file_r6", "ɵɵsanitizeUrl", "ɵɵtemplate", "ProjectDetailComponent_div_38_div_18_div_4_Template", "ctx_r2", "projet", "fichiers", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵpureFunction1", "_c0", "ctx_r4", "projetId", "ProjectDetailComponent_div_38_div_18_Template", "ProjectDetailComponent_div_38_ng_container_20_Template", "ProjectDetailComponent_div_38_ng_container_21_Template", "ɵɵtextInterpolate", "ctx_r1", "titre", "ɵɵtextInterpolate1", "groupe", "ɵɵpipeBind2", "dateLimite", "description", "length", "hasSubmitted", "ProjectDetailComponent", "constructor", "route", "router", "projetService", "rendusService", "authService", "isLoading", "ngOnInit", "snapshot", "paramMap", "get", "loadProjetDetails", "checkRenduStatus", "getProjetById", "subscribe", "next", "error", "err", "console", "navigate", "etudiantId", "getCurrentUserId", "checkRenduExists", "exists", "log", "filePath", "fileName", "includes", "parts", "split", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "RendusService", "i4", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ProjectDetailComponent_Template", "rf", "ctx", "ProjectDetailComponent_div_37_Template", "ProjectDetailComponent_div_38_Template", "getStatusClass", "getStatusText"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\front\\projects\\project-detail\\project-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\front\\projects\\project-detail\\project-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { RendusService } from 'src/app/services/rendus.service';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\n\r\n// Composant pour afficher les détails d'un projet\r\n@Component({\r\n  selector: 'app-project-detail',\r\n  templateUrl: './project-detail.component.html',\r\n  styleUrls: ['./project-detail.component.css']\r\n})\r\nexport class ProjectDetailComponent implements OnInit {\r\n  projetId: string = '';\r\n  projet: any;\r\n  rendu: any;\r\n  isLoading = true;\r\n  hasSubmitted = false;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private projetService: ProjetService,\r\n    private rendusService: RendusService,\r\n    private authService: AuthuserService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\r\n    this.loadProjetDetails();\r\n    this.checkRenduStatus();\r\n  }\r\n\r\n  loadProjetDetails(): void {\r\n    this.isLoading = true;\r\n    this.projetService.getProjetById(this.projetId).subscribe({\r\n      next: (projet: any) => {\r\n        this.projet = projet;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err) => {\r\n        console.error('Erreur lors du chargement du projet', err);\r\n        this.isLoading = false;\r\n        this.router.navigate(['/projects']);\r\n      },\r\n    });\r\n  }\r\n\r\n  checkRenduStatus(): void {\r\n    const etudiantId = this.authService.getCurrentUserId();\r\n    if (etudiantId) {\r\n      this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\r\n        next: (exists: boolean) => {\r\n          console.log(exists)\r\n          this.hasSubmitted = exists;\r\n        },\r\n        error: (err: any) => {\r\n          console.error('Erreur lors de la vérification du rendu', err);\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    // Extraire uniquement le nom du fichier\r\n    let fileName = filePath;\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      fileName = parts[parts.length - 1];\r\n    }\r\n\r\n    // Utiliser l'endpoint API spécifique pour le téléchargement\r\n    return `http://localhost:3000/api/projets/download/${fileName}`;\r\n  }\r\n}\r\n", "<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary relative\">\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\r\n    <div class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\r\n  </div>\r\n\r\n  <div class=\"container mx-auto px-4 py-8 relative z-10\">\r\n\r\n    <!-- Header moderne avec breadcrumb -->\r\n    <div class=\"mb-8\">\r\n      <nav class=\"flex items-center space-x-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4\">\r\n        <a routerLink=\"/projects\" class=\"hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\">Mes Projets</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">{{ projet?.titre || 'Détails du projet' }}</span>\r\n      </nav>\r\n\r\n      <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\r\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\r\n          <div class=\"flex items-center space-x-4 mb-6 lg:mb-0\">\r\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] flex items-center justify-center shadow-lg\">\r\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h1 class=\"text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\">\r\n                {{ projet?.titre || 'Chargement...' }}\r\n              </h1>\r\n              <div class=\"flex items-center space-x-4 mt-2\">\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">{{ projet?.groupe || 'Tous les groupes' }}</span>\r\n                </div>\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ projet?.dateLimite | date : \"dd/MM/yyyy\" || 'Pas de date limite' }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Badge de statut -->\r\n          <div class=\"flex items-center space-x-3\">\r\n            <span [ngClass]=\"getStatusClass()\" class=\"px-4 py-2 rounded-xl text-sm font-medium\">\r\n              {{ getStatusText() }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading Indicator -->\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\r\n      <div\r\n        class=\"w-12 h-12 border-4 border-[#4f5fad]/20 border-t-[#4f5fad] rounded-full animate-spin\"\r\n      ></div>\r\n    </div>\r\n\r\n    <div\r\n      *ngIf=\"!isLoading\"\r\n      class=\"bg-white rounded-xl shadow-md overflow-hidden\"\r\n    >\r\n      <!-- Header -->\r\n      <div class=\"border-t-4 border-[#4f5fad] p-6 bg-white\">\r\n        <h1 class=\"text-2xl font-bold text-[#4f5fad]\">{{ projet?.titre }}</h1>\r\n        <div class=\"flex items-center mt-2 text-sm text-[#6d6870]\">\r\n          <span>Groupe: {{ projet?.groupe }}</span>\r\n          <span class=\"mx-2\">•</span>\r\n          <span\r\n            >Date limite: {{ projet?.dateLimite | date : \"dd/MM/yyyy\" }}</span\r\n          >\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Project Content -->\r\n      <div class=\"p-6\">\r\n        <div class=\"mb-8\">\r\n          <h2 class=\"text-lg font-semibold text-[#4f5fad] mb-2\">\r\n            Description du projet\r\n          </h2>\r\n          <p class=\"text-[#6d6870]\">\r\n            {{ projet?.description || \"Aucune description fournie\" }}\r\n          </p>\r\n        </div>\r\n\r\n        <!-- Files Section -->\r\n        <div\r\n          *ngIf=\"projet?.fichiers && projet?.fichiers.length > 0\"\r\n          class=\"mb-8\"\r\n        >\r\n          <h2 class=\"text-lg font-semibold text-[#4f5fad] mb-4\">\r\n            Fichiers du projet\r\n          </h2>\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div\r\n              *ngFor=\"let file of projet.fichiers\"\r\n              class=\"border border-[#bdc6cc] rounded-lg p-4 hover:bg-[#edf1f4] transition-colors\"\r\n            >\r\n              <div class=\"flex items-center justify-between\">\r\n                <div class=\"flex items-center\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    class=\"h-6 w-6 text-[#4f5fad] mr-2\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      stroke-linecap=\"round\"\r\n                      stroke-linejoin=\"round\"\r\n                      stroke-width=\"2\"\r\n                      d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n                    />\r\n                  </svg>\r\n                  <span class=\"text-[#6d6870] truncate\">Document</span>\r\n                </div>\r\n                <a\r\n                  [href]=\"getFileUrl(file)\"\r\n                  download\r\n                  class=\"text-[#4f5fad] hover:text-[#3d4a85] text-sm font-medium transition-colors\"\r\n                >\r\n                  Télécharger\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Action Button -->\r\n        <div class=\"flex justify-end mt-6\">\r\n          <ng-container *ngIf=\"hasSubmitted\">\r\n            <span\r\n              class=\"bg-green-100 text-green-800 px-4 py-2 rounded-lg text-sm font-medium\"\r\n            >\r\n              Projet déjà soumis\r\n            </span>\r\n          </ng-container>\r\n\r\n          <ng-container *ngIf=\"!hasSubmitted\">\r\n            <a\r\n              [routerLink]=\"['/projects/submit', projetId]\"\r\n              class=\"bg-[#4f5fad] hover:bg-[#3d4a85] text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\r\n            >\r\n              Soumettre mon projet\r\n            </a>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;IC2DIA,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,cAEO;IACTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAsCEH,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAI,cAAA,EAMC;IANDJ,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,eAKE;IACJF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAsC;IAAtCL,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAM,MAAA,eAAQ;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EAAA,CAAAC,cAAA,YAIC;IACCD,EAAA,CAAAM,MAAA,8BACF;IAAAN,EAAA,CAAAG,YAAA,EAAI;;;;;IALFH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,GAAAX,EAAA,CAAAY,aAAA,CAAyB;;;;;IA/BnCZ,EAAA,CAAAC,cAAA,aAGC;IAEGD,EAAA,CAAAM,MAAA,2BACF;IAAAN,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAa,UAAA,IAAAC,mDAAA,kBA8BM;IACRd,EAAA,CAAAG,YAAA,EAAM;;;;IA9BeH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,YAAAO,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IAmCvCjB,EAAA,CAAAkB,uBAAA,GAAmC;IACjClB,EAAA,CAAAC,cAAA,eAEC;IACCD,EAAA,CAAAM,MAAA,qCACF;IAAAN,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAmB,qBAAA,EAAe;;;;;;;;IAEfnB,EAAA,CAAAkB,uBAAA,GAAoC;IAClClB,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAM,MAAA,6BACF;IAAAN,EAAA,CAAAG,YAAA,EAAI;IACNH,EAAA,CAAAmB,qBAAA,EAAe;;;;IALXnB,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,QAAA,EAA6C;;;;;IAlFvDvB,EAAA,CAAAC,cAAA,cAGC;IAGiDD,EAAA,CAAAM,MAAA,GAAmB;IAAAN,EAAA,CAAAG,YAAA,EAAK;IACtEH,EAAA,CAAAC,cAAA,cAA2D;IACnDD,EAAA,CAAAM,MAAA,GAA4B;IAAAN,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAM,MAAA,aAAC;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,WACG;IAAAD,EAAA,CAAAM,MAAA,IAA2D;;IAAAN,EAAA,CAAAG,YAAA,EAC7D;IAKLH,EAAA,CAAAC,cAAA,eAAiB;IAGXD,EAAA,CAAAM,MAAA,+BACF;IAAAN,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAA0B;IACxBD,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAa,UAAA,KAAAW,6CAAA,kBAwCM;IAGNxB,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAa,UAAA,KAAAY,sDAAA,2BAMe;IAEfzB,EAAA,CAAAa,UAAA,KAAAa,sDAAA,2BAOe;IACjB1B,EAAA,CAAAG,YAAA,EAAM;;;;IAlFwCH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA2B,iBAAA,CAAAC,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAa,KAAA,CAAmB;IAEzD7B,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA8B,kBAAA,aAAAF,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAe,MAAA,KAA4B;IAG/B/B,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAA8B,kBAAA,kBAAA9B,EAAA,CAAAgC,WAAA,QAAAJ,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAiB,UAAA,oBAA2D;IAY5DjC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,OAAAF,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAkB,WAAA,uCACF;IAKClC,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAoB,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,MAAAW,MAAA,CAAAZ,MAAA,kBAAAY,MAAA,CAAAZ,MAAA,CAAAC,QAAA,CAAAkB,MAAA,MAAqD;IA2CvCnC,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,SAAAoB,MAAA,CAAAQ,YAAA,CAAkB;IAQlBpC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAQ,UAAA,UAAAoB,MAAA,CAAAQ,YAAA,CAAmB;;;AD3I5C;AAMA,OAAM,MAAOC,sBAAsB;EAOjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAA4B;IAJ5B,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAXrB,KAAApB,QAAQ,GAAW,EAAE;IAGrB,KAAAqB,SAAS,GAAG,IAAI;IAChB,KAAAR,YAAY,GAAG,KAAK;EAQjB;EAEHS,QAAQA,CAAA;IACN,IAAI,CAACtB,QAAQ,GAAG,IAAI,CAACgB,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5D,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAACL,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAACU,aAAa,CAAC,IAAI,CAAC5B,QAAQ,CAAC,CAAC6B,SAAS,CAAC;MACxDC,IAAI,EAAGrC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC4B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDU,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEC,GAAG,CAAC;QACzD,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC;KACD,CAAC;EACJ;EAEAP,gBAAgBA,CAAA;IACd,MAAMQ,UAAU,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IACtD,IAAID,UAAU,EAAE;MACd,IAAI,CAAChB,aAAa,CAACkB,gBAAgB,CAAC,IAAI,CAACrC,QAAQ,EAAEmC,UAAU,CAAC,CAACN,SAAS,CAAC;QACvEC,IAAI,EAAGQ,MAAe,IAAI;UACxBL,OAAO,CAACM,GAAG,CAACD,MAAM,CAAC;UACnB,IAAI,CAACzB,YAAY,GAAGyB,MAAM;QAC5B,CAAC;QACDP,KAAK,EAAGC,GAAQ,IAAI;UAClBC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;QAC/D;OACD,CAAC;;EAEN;EAEA7C,UAAUA,CAACqD,QAAgB;IACzB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAAC/B,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,8CAA8C6B,QAAQ,EAAE;EACjE;;;uBA/DW3B,sBAAsB,EAAArC,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtE,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAvE,EAAA,CAAAoE,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAAzE,EAAA,CAAAoE,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAA3E,EAAA,CAAAoE,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAtBxC,sBAAsB;MAAAyC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZnCpF,EAAA,CAAAC,cAAA,aAA0K;UAGtKD,EAAA,CAAAE,SAAA,aAA6K;UAE/KF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuD;UAKkDD,EAAA,CAAAM,MAAA,kBAAW;UAAAN,EAAA,CAAAG,YAAA,EAAI;UAClHH,EAAA,CAAAI,cAAA,EAA2E;UAA3EJ,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAE,SAAA,eAA8F;UAChGF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAA6D;UAA7DL,EAAA,CAAAC,cAAA,gBAA6D;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAGhHH,EAAA,CAAAC,cAAA,eAA0I;UAIlID,EAAA,CAAAI,cAAA,EAAsF;UAAtFJ,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAAE,SAAA,gBAA4J;UAC9JF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAA8C;UAE1CD,EAAA,CAAAI,cAAA,EAA8G;UAA9GJ,EAAA,CAAAC,cAAA,eAA8G;UAC5GD,EAAA,CAAAE,SAAA,gBAAwV;UAC1VF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAAqE;UAArEL,EAAA,CAAAC,cAAA,gBAAqE;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAExHH,EAAA,CAAAC,cAAA,eAAyC;UACvCD,EAAA,CAAAI,cAAA,EAA2F;UAA3FJ,EAAA,CAAAC,cAAA,eAA2F;UACzFD,EAAA,CAAAE,SAAA,gBAA6H;UAC/HF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAAyD;UAAzDL,EAAA,CAAAC,cAAA,gBAAyD;UAAAD,EAAA,CAAAM,MAAA,IAAsE;;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAO9IH,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAOfH,EAAA,CAAAa,UAAA,KAAAyE,sCAAA,kBAIM;UAENtF,EAAA,CAAAa,UAAA,KAAA0E,sCAAA,oBA0FM;UACRvF,EAAA,CAAAG,YAAA,EAAM;;;UA5I6DH,EAAA,CAAAO,SAAA,IAA0C;UAA1CP,EAAA,CAAA2B,iBAAA,EAAA0D,GAAA,CAAArE,MAAA,kBAAAqE,GAAA,CAAArE,MAAA,CAAAa,KAAA,8BAA0C;UAa/F7B,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAA8B,kBAAA,OAAAuD,GAAA,CAAArE,MAAA,kBAAAqE,GAAA,CAAArE,MAAA,CAAAa,KAAA,0BACF;UAMyE7B,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAA2B,iBAAA,EAAA0D,GAAA,CAAArE,MAAA,kBAAAqE,GAAA,CAAArE,MAAA,CAAAe,MAAA,wBAA0C;UAMtD/B,EAAA,CAAAO,SAAA,GAAsE;UAAtEP,EAAA,CAAA2B,iBAAA,CAAA3B,EAAA,CAAAgC,WAAA,QAAAqD,GAAA,CAAArE,MAAA,kBAAAqE,GAAA,CAAArE,MAAA,CAAAiB,UAAA,wCAAsE;UAQ/HjC,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAAG,cAAA,GAA4B;UAChCxF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAA8B,kBAAA,MAAAuD,GAAA,CAAAI,aAAA,QACF;UAOFzF,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAA6E,GAAA,CAAAzC,SAAA,CAAe;UAOlB5C,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,UAAA6E,GAAA,CAAAzC,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}