{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/projets.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/file.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nfunction ProjectListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23)(3, \"div\")(4, \"p\", 24);\n    i0.ɵɵtext(5, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 26);\n    i0.ɵɵtext(9, \"Projets\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 28);\n    i0.ɵɵelement(12, \"path\", 29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"div\", 22)(14, \"div\", 23)(15, \"div\")(16, \"p\", 30);\n    i0.ɵɵtext(17, \"Rendus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 31);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 26);\n    i0.ɵɵtext(21, \"Compl\\u00E9t\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 32);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 33);\n    i0.ɵɵelement(24, \"path\", 34);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 23)(27, \"div\")(28, \"p\", 35);\n    i0.ɵɵtext(29, \"En attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 36);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 26);\n    i0.ɵɵtext(33, \"\\u00C0 rendre\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 37);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(35, \"svg\", 38);\n    i0.ɵɵelement(36, \"path\", 39);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(37, \"div\", 22)(38, \"div\", 23)(39, \"div\")(40, \"p\", 24);\n    i0.ɵɵtext(41, \"Taux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\", 25);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 26);\n    i0.ɵɵtext(45, \"R\\u00E9ussite\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(47, \"svg\", 28);\n    i0.ɵɵelement(48, \"path\", 40);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.getTotalProjects());\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.getRendusCount());\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.getPendingCount());\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getSuccessRate(), \"%\");\n  }\n}\nfunction ProjectListComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h3\", 43);\n    i0.ɵɵtext(3, \"Progression globale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 44);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵelement(7, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 47)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getSuccessRate(), \"% compl\\u00E9t\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getSuccessRate(), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getRendusCount(), \" projets rendus\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getPendingCount(), \" en attente\");\n  }\n}\nfunction ProjectListComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"div\", 50)(3, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 54);\n    i0.ɵɵelement(3, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(4, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 56);\n    i0.ɵɵtext(6, \" Aucun projet disponible \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 57);\n    i0.ɵɵtext(8, \" Vos missions appara\\u00EEtront ici \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_33_div_1_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84)(2, \"div\", 85);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 86);\n    i0.ɵɵelement(4, \"path\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 89);\n    i0.ɵɵtext(7, \"Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"a\", 90);\n    i0.ɵɵelement(9, \"div\", 91)(10, \"div\", 92);\n    i0.ɵɵelementStart(11, \"span\", 93);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 94);\n    i0.ɵɵelement(13, \"path\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", ctx_r10.getFileUrl(file_r11), i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"download\", ctx_r10.getFileName(file_r11));\n  }\n}\nfunction ProjectListComponent_div_33_div_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"h4\", 80);\n    i0.ɵɵtext(2, \" Fichiers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtemplate(4, ProjectListComponent_div_33_div_1_div_18_div_4_Template, 15, 2, \"div\", 82);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projet_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", projet_r6.fichiers);\n  }\n}\nfunction ProjectListComponent_div_33_div_1_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 96)(2, \"div\", 74);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 97);\n    i0.ɵɵelement(4, \"path\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Rendu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/submit\", a1];\n};\nfunction ProjectListComponent_div_33_div_1_ng_container_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 100);\n    i0.ɵɵelement(2, \"div\", 101)(3, \"div\", 102);\n    i0.ɵɵelementStart(4, \"span\", 103);\n    i0.ɵɵtext(5, \" Rendre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const projet_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, projet_r6._id));\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/projects/detail\", a1];\n};\nfunction ProjectListComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61);\n    i0.ɵɵelement(2, \"div\", 62)(3, \"div\", 63);\n    i0.ɵɵelementStart(4, \"div\", 64)(5, \"h3\", 65);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 66)(8, \"span\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 68);\n    i0.ɵɵtext(11, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 67);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 69)(16, \"p\", 70);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProjectListComponent_div_33_div_1_div_18_Template, 5, 1, \"div\", 71);\n    i0.ɵɵelementStart(19, \"div\", 72)(20, \"a\", 73)(21, \"div\", 74);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(22, \"svg\", 75);\n    i0.ɵɵelement(23, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(24, \"div\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"D\\u00E9tails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, ProjectListComponent_div_33_div_1_ng_container_27_Template, 8, 0, \"ng-container\", 78);\n    i0.ɵɵtemplate(28, ProjectListComponent_div_33_div_1_ng_container_28_Template, 6, 3, \"ng-container\", 78);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const projet_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", projet_r6.titre, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(projet_r6.groupe || \"Tous\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, projet_r6.dateLimite, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", projet_r6.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", projet_r6.fichiers && projet_r6.fichiers.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(11, _c1, projet_r6._id));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isRendu(projet_r6._id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isRendu(projet_r6._id));\n  }\n}\nfunction ProjectListComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, ProjectListComponent_div_33_div_1_Template, 29, 13, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.projets);\n  }\n}\n// Composant pour afficher la liste des projets\nexport class ProjectListComponent {\n  constructor(projetService, authService, rendusService, fileService) {\n    this.projetService = projetService;\n    this.authService = authService;\n    this.rendusService = rendusService;\n    this.fileService = fileService;\n    this.projets = [];\n    this.rendusMap = new Map();\n    this.isLoading = true;\n    this.userGroup = '';\n  }\n  ngOnInit() {\n    // On garde cette ligne pour une utilisation future\n    this.userGroup = this.authService.getCurrentUser()?.groupe || '';\n    this.loadProjets();\n  }\n  loadProjets() {\n    this.isLoading = true;\n    this.projetService.getProjets().subscribe({\n      next: projets => {\n        // Afficher tous les projets sans filtrage\n        this.projets = projets;\n        this.isLoading = false;\n        // Vérifier quels projets ont déjà été rendus par l'étudiant\n        this.projets.forEach(projet => {\n          if (projet._id) {\n            this.checkRenduStatus(projet._id);\n          }\n        });\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des projets', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  checkRenduStatus(projetId) {\n    const etudiantId = this.authService.getCurrentUserId();\n    if (!etudiantId) return;\n    this.rendusService.checkRenduExists(projetId, etudiantId).subscribe({\n      next: exists => {\n        this.rendusMap.set(projetId, exists);\n      },\n      error: error => {\n        console.error(`Erreur lors de la vérification du rendu pour le projet ${projetId}`, error);\n      }\n    });\n  }\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route qui pointe vers le bon emplacement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'fichier';\n    // Extraire uniquement le nom du fichier\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  // Méthode pour vérifier si un projet a été rendu\n  isRendu(projetId) {\n    return projetId ? this.rendusMap.get(projetId) === true : false;\n  }\n  static {\n    this.ɵfac = function ProjectListComponent_Factory(t) {\n      return new (t || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.ProjetService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectListComponent,\n      selectors: [[\"app-project-list\"]],\n      decls: 34,\n      vars: 5,\n      consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"p-4\", \"md:p-6\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"max-w-6xl\", \"mx-auto\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:justify-between\", \"lg:items-center\", \"mb-6\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"md:text-base\", \"mt-1\"], [1, \"h-12\", \"w-12\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"shadow-lg\", \"relative\", \"group\", \"overflow-hidden\", \"mt-4\", \"lg:mt-0\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-full\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\", \"duration-300\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [\"class\", \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\", 4, \"ngIf\"], [\"class\", \"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-6 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md mb-8\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-2\", \"md:grid-cols-4\", \"gap-4\", \"mb-8\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-xl\", \"p-4\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", \"group\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-3\", \"rounded-xl\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-xs\", \"font-medium\", \"text-green-600\", \"dark:text-green-400\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-green-700\", \"dark:text-green-400\"], [1, \"bg-green-100\", \"dark:bg-green-900/30\", \"p-3\", \"rounded-xl\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-green-600\", \"dark:text-green-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-xs\", \"font-medium\", \"text-orange-600\", \"dark:text-orange-400\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-700\", \"dark:text-orange-400\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/30\", \"p-3\", \"rounded-xl\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\", \"dark:text-orange-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-xl\", \"p-6\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"shadow-md\", \"mb-8\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-3\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"w-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-3\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"h-3\", \"rounded-full\", \"transition-all\", \"duration-500\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-8\", \"text-center\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"mb-6\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"hover:shadow-lg\", \"dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-1\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"group\"], [1, \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-5\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"relative\"], [1, \"text-lg\", \"font-bold\", \"pr-10\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"group-hover:scale-[1.01]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"mt-2\", \"text-xs\", \"space-x-2\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"px-2\", \"py-0.5\", \"rounded-full\", \"backdrop-blur-sm\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"p-5\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\", \"line-clamp-3\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"pt-3\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"flex\", \"items-center\", \"transition-colors\", \"relative\", \"group/details\", 3, \"routerLink\"], [1, \"relative\", \"mr-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"relative\", \"z-10\", \"group-hover/details:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/details:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [4, \"ngIf\"], [1, \"mb-4\"], [1, \"text-xs\", \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\", \"mb-2\"], [1, \"space-y-2\"], [\"class\", \"flex items-center justify-between bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-lg p-2.5 backdrop-blur-sm group/file hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"rounded-lg\", \"p-2.5\", \"backdrop-blur-sm\", \"group/file\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"truncate\"], [1, \"relative\", \"mr-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\", \"group-hover/file:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/file:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"truncate\"], [\"download\", \"\", 1, \"relative\", \"overflow-hidden\", \"group/download\", 3, \"href\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/download:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/download:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"text-xs\", \"px-3\", \"py-1\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"mr-1\", \"group-hover/download:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"bg-gradient-to-r\", \"from-green-100\", \"to-green-50\", \"dark:from-green-900/30\", \"dark:to-green-800/30\", \"text-green-800\", \"dark:text-green-400\", \"text-xs\", \"px-3\", \"py-1.5\", \"rounded-full\", \"flex\", \"items-center\", \"shadow-sm\", \"backdrop-blur-sm\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"w-3\", \"h-3\", \"relative\", \"z-10\"], [\"fill-rule\", \"evenodd\", \"d\", \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"absolute\", \"inset-0\", \"bg-green-500/20\", \"blur-md\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"relative\", \"overflow-hidden\", \"group/submit\", 3, \"routerLink\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/submit:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/submit:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"text-sm\", \"font-medium\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"transition-all\", \"z-10\"]],\n      template: function ProjectListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\", 9)(20, \"div\")(21, \"h1\", 10);\n          i0.ɵɵtext(22, \" Mes Projets \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 11);\n          i0.ɵɵtext(24, \" G\\u00E9rez vos missions acad\\u00E9miques et suivez vos rendus \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 12);\n          i0.ɵɵelement(26, \"div\", 13);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(27, \"svg\", 14);\n          i0.ɵɵelement(28, \"path\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(29, ProjectListComponent_div_29_Template, 49, 4, \"div\", 16);\n          i0.ɵɵtemplate(30, ProjectListComponent_div_30_Template, 13, 5, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, ProjectListComponent_div_31_Template, 4, 0, \"div\", 18);\n          i0.ɵɵtemplate(32, ProjectListComponent_div_32_Template, 9, 0, \"div\", 19);\n          i0.ɵɵtemplate(33, ProjectListComponent_div_33_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.RouterLink, i5.DatePipe],\n      styles: [\"\\n\\n.badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 9999px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.badge-group[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  color: #ffffff;\\n}\\n\\n.badge-deadline[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  color: #ffffff;\\n}\\n\\n\\n\\n.line-clamp-3[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3QtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDJCQUEyQjtBQUMzQjtFQUNFLG9CQUFvQjtFQUNwQixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLHFCQUFxQjtFQUNyQixrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsMENBQTBDO0VBQzFDLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSwwQ0FBMEM7RUFDMUMsY0FBYztBQUNoQjs7QUFFQSxvREFBb0Q7QUFDcEQ7RUFDRSxvQkFBb0I7RUFDcEIscUJBQXFCO0VBQ3JCLDRCQUE0QjtFQUM1QixnQkFBZ0I7QUFDbEIiLCJmaWxlIjoicHJvamVjdC1saXN0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZXMgYmFkZ2VzICovXHJcbi5iYWRnZSB7XHJcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTtcclxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XHJcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbn1cclxuXHJcbi5iYWRnZS1ncm91cCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xyXG4gIGNvbG9yOiAjZmZmZmZmO1xyXG59XHJcblxyXG4uYmFkZ2UtZGVhZGxpbmUge1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcclxuICBjb2xvcjogI2ZmZmZmZjtcclxufVxyXG5cclxuLyogTGltaXRlciBsZSBub21icmUgZGUgbGlnbmVzIHBvdXIgbGEgZGVzY3JpcHRpb24gKi9cclxuLmxpbmUtY2xhbXAtMyB7XHJcbiAgZGlzcGxheTogLXdlYmtpdC1ib3g7XHJcbiAgLXdlYmtpdC1saW5lLWNsYW1wOiAzO1xyXG4gIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvamVjdHMvcHJvamVjdC1saXN0L3Byb2plY3QtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDJCQUEyQjtBQUMzQjtFQUNFLG9CQUFvQjtFQUNwQixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLHFCQUFxQjtFQUNyQixrQkFBa0I7RUFDbEIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsMENBQTBDO0VBQzFDLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSwwQ0FBMEM7RUFDMUMsY0FBYztBQUNoQjs7QUFFQSxvREFBb0Q7QUFDcEQ7RUFDRSxvQkFBb0I7RUFDcEIscUJBQXFCO0VBQ3JCLDRCQUE0QjtFQUM1QixnQkFBZ0I7QUFDbEI7QUFDQSxvMUNBQW8xQyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlcyBiYWRnZXMgKi9cclxuLmJhZGdlIHtcclxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcclxuICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxufVxyXG5cclxuLmJhZGdlLWdyb3VwIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XHJcbiAgY29sb3I6ICNmZmZmZmY7XHJcbn1cclxuXHJcbi5iYWRnZS1kZWFkbGluZSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xyXG4gIGNvbG9yOiAjZmZmZmZmO1xyXG59XHJcblxyXG4vKiBMaW1pdGVyIGxlIG5vbWJyZSBkZSBsaWduZXMgcG91ciBsYSBkZXNjcmlwdGlvbiAqL1xyXG4ubGluZS1jbGFtcC0zIHtcclxuICBkaXNwbGF5OiAtd2Via2l0LWJveDtcclxuICAtd2Via2l0LWxpbmUtY2xhbXA6IDM7XHJcbiAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getTotalProjects", "getRendusCount", "getPendingCount", "ɵɵtextInterpolate1", "getSuccessRate", "ctx_r1", "ɵɵstyleProp", "ɵɵproperty", "ctx_r10", "getFileUrl", "file_r11", "ɵɵsanitizeUrl", "ɵɵattribute", "getFileName", "ɵɵtemplate", "ProjectListComponent_div_33_div_1_div_18_div_4_Template", "projet_r6", "fichiers", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵpureFunction1", "_c0", "_id", "ProjectListComponent_div_33_div_1_div_18_Template", "ProjectListComponent_div_33_div_1_ng_container_27_Template", "ProjectListComponent_div_33_div_1_ng_container_28_Template", "titre", "groupe", "ɵɵpipeBind2", "dateLimite", "description", "length", "_c1", "ctx_r5", "isRendu", "ProjectListComponent_div_33_div_1_Template", "ctx_r4", "projets", "ProjectListComponent", "constructor", "projetService", "authService", "rendusService", "fileService", "rendusMap", "Map", "isLoading", "userGroup", "ngOnInit", "getCurrentUser", "loadProjets", "getProjets", "subscribe", "next", "for<PERSON>ach", "projet", "checkRenduStatus", "error", "console", "projetId", "etudiantId", "getCurrentUserId", "checkRenduExists", "exists", "set", "filePath", "fileName", "includes", "parts", "split", "urlBackend", "get", "ɵɵdirectiveInject", "i1", "ProjetService", "i2", "AuthuserService", "i3", "RendusService", "i4", "FileService", "selectors", "decls", "vars", "consts", "template", "ProjectListComponent_Template", "rf", "ctx", "ProjectListComponent_div_29_Template", "ProjectListComponent_div_30_Template", "ProjectListComponent_div_31_Template", "ProjectListComponent_div_32_Template", "ProjectListComponent_div_33_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\front\\projects\\project-list\\project-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\front\\projects\\project-list\\project-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { Projet } from 'src/app/models/projet.model';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\nimport { RendusService } from 'src/app/services/rendus.service';\r\nimport { FileService } from 'src/app/services/file.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n// Composant pour afficher la liste des projets\r\n@Component({\r\n  selector: 'app-project-list',\r\n  templateUrl: './project-list.component.html',\r\n  styleUrls: ['./project-list.component.css'],\r\n})\r\nexport class ProjectListComponent implements OnInit {\r\n  projets: Projet[] = [];\r\n  rendusMap: Map<string, boolean> = new Map();\r\n  isLoading = true;\r\n  userGroup: string = '';\r\n\r\n  constructor(\r\n    private projetService: ProjetService,\r\n    private authService: AuthuserService,\r\n    private rendusService: RendusService,\r\n    private fileService: FileService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // On garde cette ligne pour une utilisation future\r\n    this.userGroup = this.authService.getCurrentUser()?.groupe || '';\r\n    this.loadProjets();\r\n  }\r\n\r\n  loadProjets(): void {\r\n    this.isLoading = true;\r\n    this.projetService.getProjets().subscribe({\r\n      next: (projets: Projet[]) => {\r\n        // Afficher tous les projets sans filtrage\r\n        this.projets = projets;\r\n        this.isLoading = false;\r\n\r\n        // Vérifier quels projets ont déjà été rendus par l'étudiant\r\n        this.projets.forEach((projet) => {\r\n          if (projet._id) {\r\n            this.checkRenduStatus(projet._id);\r\n          }\r\n        });\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Erreur lors du chargement des projets', error);\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  checkRenduStatus(projetId: string): void {\r\n    const etudiantId = this.authService.getCurrentUserId();\r\n    if (!etudiantId) return;\r\n\r\n    this.rendusService.checkRenduExists(projetId, etudiantId).subscribe({\r\n      next: (exists: boolean) => {\r\n        this.rendusMap.set(projetId, exists);\r\n      },\r\n      error: (error: any) => {\r\n        console.error(\r\n          `Erreur lors de la vérification du rendu pour le projet ${projetId}`,\r\n          error\r\n        );\r\n      },\r\n    });\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    if (!filePath) return '';\r\n\r\n    // Extraire uniquement le nom du fichier\r\n    let fileName = filePath;\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      fileName = parts[parts.length - 1];\r\n    }\r\n\r\n    // Utiliser la route qui pointe vers le bon emplacement\r\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\r\n  }\r\n\r\n  getFileName(filePath: string): string {\r\n    if (!filePath) return 'fichier';\r\n\r\n    // Extraire uniquement le nom du fichier\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      return parts[parts.length - 1];\r\n    }\r\n\r\n    return filePath;\r\n  }\r\n\r\n  // Méthode pour vérifier si un projet a été rendu\r\n  isRendu(projetId: string | undefined): boolean {\r\n    return projetId ? this.rendusMap.get(projetId) === true : false;\r\n  }\r\n}\r\n", "<div class=\"min-h-screen bg-[#edf1f4] dark:bg-[#121212] p-4 md:p-6 relative\">\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"max-w-6xl mx-auto relative z-10\">\r\n    <!-- Header moderne avec statistiques -->\r\n    <div class=\"mb-8\">\r\n      <div class=\"flex flex-col lg:flex-row lg:justify-between lg:items-center mb-6\">\r\n        <div>\r\n          <h1\r\n            class=\"text-3xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n          >\r\n            Mes Projets\r\n          </h1>\r\n          <p class=\"text-[#6d6870] dark:text-[#a0a0a0] text-sm md:text-base mt-1\">\r\n            Gérez vos missions académiques et suivez vos rendus\r\n          </p>\r\n        </div>\r\n        <div\r\n          class=\"h-12 w-12 rounded-full bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] flex items-center justify-center text-white shadow-lg relative group overflow-hidden mt-4 lg:mt-0\"\r\n        >\r\n          <!-- Glow effect -->\r\n          <div\r\n            class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-full opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            class=\"h-6 w-6 relative z-10 group-hover:scale-110 transition-transform duration-300\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            stroke=\"currentColor\"\r\n          >\r\n            <path\r\n              stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\"\r\n              stroke-width=\"2\"\r\n              d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n            />\r\n          </svg>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Statistiques -->\r\n      <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\" *ngIf=\"!isLoading\">\r\n        <!-- Total projets -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div>\r\n              <p class=\"text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider\">Total</p>\r\n              <p class=\"text-2xl font-bold text-[#3d4a85] dark:text-[#6d78c9]\">{{ getTotalProjects() }}</p>\r\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">Projets</p>\r\n            </div>\r\n            <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-3 rounded-xl group-hover:scale-110 transition-transform\">\r\n              <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Projets rendus -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div>\r\n              <p class=\"text-xs font-medium text-green-600 dark:text-green-400 uppercase tracking-wider\">Rendus</p>\r\n              <p class=\"text-2xl font-bold text-green-700 dark:text-green-400\">{{ getRendusCount() }}</p>\r\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">Complétés</p>\r\n            </div>\r\n            <div class=\"bg-green-100 dark:bg-green-900/30 p-3 rounded-xl group-hover:scale-110 transition-transform\">\r\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Projets en attente -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div>\r\n              <p class=\"text-xs font-medium text-orange-600 dark:text-orange-400 uppercase tracking-wider\">En attente</p>\r\n              <p class=\"text-2xl font-bold text-orange-700 dark:text-orange-400\">{{ getPendingCount() }}</p>\r\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">À rendre</p>\r\n            </div>\r\n            <div class=\"bg-orange-100 dark:bg-orange-900/30 p-3 rounded-xl group-hover:scale-110 transition-transform\">\r\n              <svg class=\"w-5 h-5 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Taux de réussite -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-4 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md hover:shadow-lg transition-all duration-300 group\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div>\r\n              <p class=\"text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9] uppercase tracking-wider\">Taux</p>\r\n              <p class=\"text-2xl font-bold text-[#3d4a85] dark:text-[#6d78c9]\">{{ getSuccessRate() }}%</p>\r\n              <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-1\">Réussite</p>\r\n            </div>\r\n            <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-3 rounded-xl group-hover:scale-110 transition-transform\">\r\n              <svg class=\"w-5 h-5 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Barre de progression -->\r\n      <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-6 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md mb-8\" *ngIf=\"!isLoading && projets.length > 0\">\r\n        <div class=\"flex items-center justify-between mb-3\">\r\n          <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Progression globale</h3>\r\n          <span class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">{{ getSuccessRate() }}% complété</span>\r\n        </div>\r\n        <div class=\"w-full bg-[#edf1f4] dark:bg-[#2a2a2a] rounded-full h-3\">\r\n          <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] h-3 rounded-full transition-all duration-500\"\r\n               [style.width.%]=\"getSuccessRate()\"></div>\r\n        </div>\r\n        <div class=\"flex justify-between text-xs text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n          <span>{{ getRendusCount() }} projets rendus</span>\r\n          <span>{{ getPendingCount() }} en attente</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading Indicator -->\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\r\n      <div class=\"relative\">\r\n        <div\r\n          class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"\r\n        ></div>\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No Projects -->\r\n    <div\r\n      *ngIf=\"!isLoading && projets.length === 0\"\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\r\n    >\r\n      <div\r\n        class=\"w-24 h-24 mx-auto mb-6 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 rounded-full flex items-center justify-center relative\"\r\n      >\r\n        <svg\r\n          class=\"w-12 h-12 text-[#4f5fad] dark:text-[#6d78c9] relative z-10\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n        >\r\n          <path\r\n            stroke-linecap=\"round\"\r\n            stroke-linejoin=\"round\"\r\n            stroke-width=\"1.5\"\r\n            d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n          ></path>\r\n        </svg>\r\n        <!-- Glow effect -->\r\n        <div\r\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n        ></div>\r\n      </div>\r\n      <h3\r\n        class=\"text-xl font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent mb-2\"\r\n      >\r\n        Aucun projet disponible\r\n      </h3>\r\n      <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-1\">\r\n        Vos missions apparaîtront ici\r\n      </p>\r\n    </div>\r\n\r\n    <!-- Projects Grid -->\r\n    <div\r\n      *ngIf=\"!isLoading\"\r\n      class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\r\n    >\r\n      <div\r\n        *ngFor=\"let projet of projets\"\r\n        class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\"\r\n      >\r\n        <!-- Header -->\r\n        <div class=\"relative overflow-hidden\">\r\n          <!-- Decorative gradient top border -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n          ></div>\r\n\r\n          <!-- Glow effect on hover -->\r\n          <div\r\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\r\n          ></div>\r\n\r\n          <div class=\"p-5 bg-white dark:bg-[#1e1e1e] relative\">\r\n            <h3\r\n              class=\"text-lg font-bold pr-10 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-[1.01] transition-transform duration-300 origin-left\"\r\n            >\r\n              {{ projet.titre }}\r\n            </h3>\r\n            <div class=\"flex items-center mt-2 text-xs space-x-2\">\r\n              <span\r\n                class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm\"\r\n                >{{ projet.groupe || \"Tous\" }}</span\r\n              >\r\n              <span class=\"text-[#6d6870] dark:text-[#a0a0a0]\">•</span>\r\n              <span\r\n                class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] px-2 py-0.5 rounded-full backdrop-blur-sm\"\r\n                >{{ projet.dateLimite | date : \"dd/MM/yyyy\" }}</span\r\n              >\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Content -->\r\n        <div class=\"p-5\">\r\n          <p\r\n            class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4 line-clamp-3\"\r\n          >\r\n            {{ projet.description || \"Aucune description\" }}\r\n          </p>\r\n\r\n          <!-- Files -->\r\n          <div\r\n            *ngIf=\"projet.fichiers && projet.fichiers.length > 0\"\r\n            class=\"mb-4\"\r\n          >\r\n            <h4\r\n              class=\"text-xs font-semibold text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider mb-2\"\r\n            >\r\n              Fichiers\r\n            </h4>\r\n            <div class=\"space-y-2\">\r\n              <div\r\n                *ngFor=\"let file of projet.fichiers\"\r\n                class=\"flex items-center justify-between bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-lg p-2.5 backdrop-blur-sm group/file hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\"\r\n              >\r\n                <div class=\"flex items-center truncate\">\r\n                  <div class=\"relative mr-2\">\r\n                    <svg\r\n                      class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9] relative z-10 group-hover/file:scale-110 transition-transform\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        stroke-linecap=\"round\"\r\n                        stroke-linejoin=\"round\"\r\n                        stroke-width=\"2\"\r\n                        d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\r\n                      ></path>\r\n                    </svg>\r\n                    <!-- Glow effect -->\r\n                    <div\r\n                      class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/file:opacity-100 transition-opacity blur-md rounded-full\"\r\n                    ></div>\r\n                  </div>\r\n                  <span\r\n                    class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0] truncate\"\r\n                    >Document</span\r\n                  >\r\n                </div>\r\n                <a\r\n                  [href]=\"getFileUrl(file)\"\r\n                  download\r\n                  [attr.download]=\"getFileName(file)\"\r\n                  class=\"relative overflow-hidden group/download\"\r\n                >\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/download:scale-105\"\r\n                  ></div>\r\n                  <div\r\n                    class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/download:opacity-100 blur-md transition-opacity duration-300\"\r\n                  ></div>\r\n                  <span\r\n                    class=\"relative flex items-center text-white text-xs px-3 py-1 rounded-lg transition-all z-10\"\r\n                  >\r\n                    <svg\r\n                      class=\"w-3 h-3 mr-1 group-hover/download:scale-110 transition-transform\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                    >\r\n                      <path\r\n                        stroke-linecap=\"round\"\r\n                        stroke-linejoin=\"round\"\r\n                        stroke-width=\"2\"\r\n                        d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\r\n                      ></path>\r\n                    </svg>\r\n                    Télécharger\r\n                  </span>\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Actions -->\r\n          <div\r\n            class=\"flex justify-between items-center pt-3 border-t border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\r\n          >\r\n            <a\r\n              [routerLink]=\"['/projects/detail', projet._id]\"\r\n              class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] text-sm font-medium flex items-center transition-colors relative group/details\"\r\n            >\r\n              <div class=\"relative mr-1\">\r\n                <svg\r\n                  class=\"w-4 h-4 relative z-10 group-hover/details:scale-110 transition-transform\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path\r\n                    stroke-linecap=\"round\"\r\n                    stroke-linejoin=\"round\"\r\n                    stroke-width=\"2\"\r\n                    d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                  ></path>\r\n                </svg>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover/details:opacity-100 transition-opacity blur-md rounded-full\"\r\n                ></div>\r\n              </div>\r\n              <span>Détails</span>\r\n            </a>\r\n\r\n            <ng-container *ngIf=\"isRendu(projet._id)\">\r\n              <span\r\n                class=\"bg-gradient-to-r from-green-100 to-green-50 dark:from-green-900/30 dark:to-green-800/30 text-green-800 dark:text-green-400 text-xs px-3 py-1.5 rounded-full flex items-center shadow-sm backdrop-blur-sm\"\r\n              >\r\n                <div class=\"relative mr-1\">\r\n                  <svg\r\n                    class=\"w-3 h-3 relative z-10\"\r\n                    fill=\"currentColor\"\r\n                    viewBox=\"0 0 20 20\"\r\n                  >\r\n                    <path\r\n                      fill-rule=\"evenodd\"\r\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\r\n                      clip-rule=\"evenodd\"\r\n                    ></path>\r\n                  </svg>\r\n                  <!-- Glow effect -->\r\n                  <div\r\n                    class=\"absolute inset-0 bg-green-500/20 blur-md rounded-full transform scale-150 -z-10\"\r\n                  ></div>\r\n                </div>\r\n                <span>Rendu</span>\r\n              </span>\r\n            </ng-container>\r\n\r\n            <ng-container *ngIf=\"!isRendu(projet._id)\">\r\n              <a\r\n                [routerLink]=\"['/projects/submit', projet._id]\"\r\n                class=\"relative overflow-hidden group/submit\"\r\n              >\r\n                <div\r\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover/submit:scale-105\"\r\n                ></div>\r\n                <div\r\n                  class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover/submit:opacity-100 blur-md transition-opacity duration-300\"\r\n                ></div>\r\n                <span\r\n                  class=\"relative flex items-center text-white text-sm font-medium px-3 py-1.5 rounded-lg transition-all z-10\"\r\n                >\r\n                  Rendre\r\n                </span>\r\n              </a>\r\n            </ng-container>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAMA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;;;;;IC8DpDC,EAAA,CAAAC,eAAA,EAA2E;IAA3ED,EAAA,CAAAE,cAAA,cAA2E;IAKwBF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpGJ,EAAA,CAAAE,cAAA,YAAiE;IAAAF,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC7FJ,EAAA,CAAAE,cAAA,YAA2D;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAExEJ,EAAA,CAAAE,cAAA,eAA4G;IAC1GF,EAAA,CAAAK,cAAA,EAA8G;IAA9GL,EAAA,CAAAE,cAAA,eAA8G;IAC5GF,EAAA,CAAAM,SAAA,gBAA4J;IAC9JN,EAAA,CAAAI,YAAA,EAAM;IAMZJ,EAAA,CAAAC,eAAA,EAA2L;IAA3LD,EAAA,CAAAE,cAAA,eAA2L;IAG1FF,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrGJ,EAAA,CAAAE,cAAA,aAAiE;IAAAF,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC3FJ,EAAA,CAAAE,cAAA,aAA2D;IAAAF,EAAA,CAAAG,MAAA,2BAAS;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE1EJ,EAAA,CAAAE,cAAA,eAAyG;IACvGF,EAAA,CAAAK,cAAA,EAA8G;IAA9GL,EAAA,CAAAE,cAAA,eAA8G;IAC5GF,EAAA,CAAAM,SAAA,gBAA+H;IACjIN,EAAA,CAAAI,YAAA,EAAM;IAMZJ,EAAA,CAAAC,eAAA,EAA2L;IAA3LD,EAAA,CAAAE,cAAA,eAA2L;IAGxFF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC3GJ,EAAA,CAAAE,cAAA,aAAmE;IAAAF,EAAA,CAAAG,MAAA,IAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9FJ,EAAA,CAAAE,cAAA,aAA2D;IAAAF,EAAA,CAAAG,MAAA,qBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEzEJ,EAAA,CAAAE,cAAA,eAA2G;IACzGF,EAAA,CAAAK,cAAA,EAAgH;IAAhHL,EAAA,CAAAE,cAAA,eAAgH;IAC9GF,EAAA,CAAAM,SAAA,gBAA6H;IAC/HN,EAAA,CAAAI,YAAA,EAAM;IAMZJ,EAAA,CAAAC,eAAA,EAA2L;IAA3LD,EAAA,CAAAE,cAAA,eAA2L;IAG1FF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnGJ,EAAA,CAAAE,cAAA,aAAiE;IAAAF,EAAA,CAAAG,MAAA,IAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC5FJ,EAAA,CAAAE,cAAA,aAA2D;IAAAF,EAAA,CAAAG,MAAA,qBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEzEJ,EAAA,CAAAE,cAAA,eAA4G;IAC1GF,EAAA,CAAAK,cAAA,EAA8G;IAA9GL,EAAA,CAAAE,cAAA,eAA8G;IAC5GF,EAAA,CAAAM,SAAA,gBAAgH;IAClHN,EAAA,CAAAI,YAAA,EAAM;;;;IAtD2DJ,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,GAAwB;IAgBxBV,EAAA,CAAAO,SAAA,IAAsB;IAAtBP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAE,cAAA,GAAsB;IAgBpBX,EAAA,CAAAO,SAAA,IAAuB;IAAvBP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAG,eAAA,GAAuB;IAgBzBZ,EAAA,CAAAO,SAAA,IAAuB;IAAvBP,EAAA,CAAAa,kBAAA,KAAAJ,MAAA,CAAAK,cAAA,QAAuB;;;;;;IAahGd,EAAA,CAAAC,eAAA,EAAuL;IAAvLD,EAAA,CAAAE,cAAA,cAAuL;IAE9GF,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7FJ,EAAA,CAAAE,cAAA,eAAqE;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE9GJ,EAAA,CAAAE,cAAA,cAAoE;IAClEF,EAAA,CAAAM,SAAA,cAC8C;IAChDN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAkF;IAC1EF,EAAA,CAAAG,MAAA,IAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClDJ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,IAAkC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IARsBJ,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAa,kBAAA,KAAAE,MAAA,CAAAD,cAAA,2BAAgC;IAIhGd,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAgB,WAAA,UAAAD,MAAA,CAAAD,cAAA,QAAkC;IAGjCd,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAa,kBAAA,KAAAE,MAAA,CAAAJ,cAAA,sBAAqC;IACrCX,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAa,kBAAA,KAAAE,MAAA,CAAAH,eAAA,kBAAkC;;;;;;IAM9CZ,EAAA,CAAAC,eAAA,EAAyD;IAAzDD,EAAA,CAAAE,cAAA,cAAyD;IAErDF,EAAA,CAAAM,SAAA,cAEO;IAKTN,EAAA,CAAAI,YAAA,EAAM;;;;;;IAIRJ,EAAA,CAAAC,eAAA,EAGC;IAHDD,EAAA,CAAAE,cAAA,cAGC;IAIGF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,cAKC;IACCF,EAAA,CAAAM,SAAA,eAKQ;IACVN,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAM,SAAA,cAEO;IACTN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,aAEC;IACCF,EAAA,CAAAG,MAAA,gCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAmD;IACjDF,EAAA,CAAAG,MAAA,2CACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IA+DIJ,EAAA,CAAAE,cAAA,cAGC;IAGKF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,cAKC;IACCF,EAAA,CAAAM,SAAA,eAKQ;IACVN,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAM,SAAA,cAEO;IACTN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAEG;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EACV;IAEHJ,EAAA,CAAAE,cAAA,YAKC;IACCF,EAAA,CAAAM,SAAA,cAEO;IAIPN,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,eAKC;IACCF,EAAA,CAAAM,SAAA,gBAKQ;IACVN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,+BACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IA5BPJ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAiB,UAAA,SAAAC,OAAA,CAAAC,UAAA,CAAAC,QAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAyB;IAEzBrB,EAAA,CAAAsB,WAAA,aAAAJ,OAAA,CAAAK,WAAA,CAAAH,QAAA,EAAmC;;;;;IA1C3CpB,EAAA,CAAAE,cAAA,cAGC;IAIGF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,cAAuB;IACrBF,EAAA,CAAAwB,UAAA,IAAAC,uDAAA,mBA4DM;IACRzB,EAAA,CAAAI,YAAA,EAAM;;;;IA5DeJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAiB,UAAA,YAAAS,SAAA,CAAAC,QAAA,CAAkB;;;;;IA6FvC3B,EAAA,CAAA4B,uBAAA,GAA0C;IACxC5B,EAAA,CAAAE,cAAA,eAEC;IAEGF,EAAA,CAAAK,cAAA,EAIC;IAJDL,EAAA,CAAAE,cAAA,cAIC;IACCF,EAAA,CAAAM,SAAA,eAIQ;IACVN,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAM,SAAA,cAEO;IACTN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEtBJ,EAAA,CAAA6B,qBAAA,EAAe;;;;;;;;IAEf7B,EAAA,CAAA4B,uBAAA,GAA2C;IACzC5B,EAAA,CAAAE,cAAA,aAGC;IACCF,EAAA,CAAAM,SAAA,eAEO;IAIPN,EAAA,CAAAE,cAAA,gBAEC;IACCF,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEXJ,EAAA,CAAA6B,qBAAA,EAAe;;;;IAfX7B,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAL,SAAA,CAAAM,GAAA,EAA+C;;;;;;;;IAhLzDhC,EAAA,CAAAE,cAAA,cAGC;IAIGF,EAAA,CAAAM,SAAA,cAEO;IAOPN,EAAA,CAAAE,cAAA,cAAqD;IAIjDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,cAAsD;IAGjDF,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAC/B;IACDJ,EAAA,CAAAE,cAAA,gBAAiD;IAAAF,EAAA,CAAAG,MAAA,cAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzDJ,EAAA,CAAAE,cAAA,gBAEG;IAAAF,EAAA,CAAAG,MAAA,IAA6C;;IAAAH,EAAA,CAAAI,YAAA,EAC/C;IAMPJ,EAAA,CAAAE,cAAA,eAAiB;IAIbF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGJJ,EAAA,CAAAwB,UAAA,KAAAS,iDAAA,kBAwEM;IAGNjC,EAAA,CAAAE,cAAA,eAEC;IAMKF,EAAA,CAAAK,cAAA,EAKC;IALDL,EAAA,CAAAE,cAAA,eAKC;IACCF,EAAA,CAAAM,SAAA,gBAKQ;IACVN,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,eAAA,EAEC;IAFDD,EAAA,CAAAM,SAAA,eAEO;IACTN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,oBAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGtBJ,EAAA,CAAAwB,UAAA,KAAAU,0DAAA,2BAuBe;IAEflC,EAAA,CAAAwB,UAAA,KAAAW,0DAAA,2BAiBe;IACjBnC,EAAA,CAAAI,YAAA,EAAM;;;;;IA5KFJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAa,kBAAA,MAAAa,SAAA,CAAAU,KAAA,MACF;IAIKpC,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,iBAAA,CAAAkB,SAAA,CAAAW,MAAA,WAA6B;IAK7BrC,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAsC,WAAA,QAAAZ,SAAA,CAAAa,UAAA,gBAA6C;IAWlDvC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAa,kBAAA,MAAAa,SAAA,CAAAc,WAAA,8BACF;IAIGxC,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAiB,UAAA,SAAAS,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAc,MAAA,KAAmD;IA8ElDzC,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAA8B,eAAA,KAAAY,GAAA,EAAAhB,SAAA,CAAAM,GAAA,EAA+C;IAyBlChC,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAiB,UAAA,SAAA0B,MAAA,CAAAC,OAAA,CAAAlB,SAAA,CAAAM,GAAA,EAAyB;IAyBzBhC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAiB,UAAA,UAAA0B,MAAA,CAAAC,OAAA,CAAAlB,SAAA,CAAAM,GAAA,EAA0B;;;;;;IAlLjDhC,EAAA,CAAAC,eAAA,EAGC;IAHDD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAwB,UAAA,IAAAqB,0CAAA,oBAkMM;IACR7C,EAAA,CAAAI,YAAA,EAAM;;;;IAlMiBJ,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAiB,UAAA,YAAA6B,MAAA,CAAAC,OAAA,CAAU;;;ADtMrC;AAMA,OAAM,MAAOC,oBAAoB;EAM/BC,YACUC,aAA4B,EAC5BC,WAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB;IAHxB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IATrB,KAAAN,OAAO,GAAa,EAAE;IACtB,KAAAO,SAAS,GAAyB,IAAIC,GAAG,EAAE;IAC3C,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,SAAS,GAAW,EAAE;EAOnB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,SAAS,GAAG,IAAI,CAACN,WAAW,CAACQ,cAAc,EAAE,EAAEtB,MAAM,IAAI,EAAE;IAChE,IAAI,CAACuB,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACJ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACN,aAAa,CAACW,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGhB,OAAiB,IAAI;QAC1B;QACA,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACS,SAAS,GAAG,KAAK;QAEtB;QACA,IAAI,CAACT,OAAO,CAACiB,OAAO,CAAEC,MAAM,IAAI;UAC9B,IAAIA,MAAM,CAACjC,GAAG,EAAE;YACd,IAAI,CAACkC,gBAAgB,CAACD,MAAM,CAACjC,GAAG,CAAC;;QAErC,CAAC,CAAC;MACJ,CAAC;MACDmC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,IAAI,CAACX,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAU,gBAAgBA,CAACG,QAAgB;IAC/B,MAAMC,UAAU,GAAG,IAAI,CAACnB,WAAW,CAACoB,gBAAgB,EAAE;IACtD,IAAI,CAACD,UAAU,EAAE;IAEjB,IAAI,CAAClB,aAAa,CAACoB,gBAAgB,CAACH,QAAQ,EAAEC,UAAU,CAAC,CAACR,SAAS,CAAC;MAClEC,IAAI,EAAGU,MAAe,IAAI;QACxB,IAAI,CAACnB,SAAS,CAACoB,GAAG,CAACL,QAAQ,EAAEI,MAAM,CAAC;MACtC,CAAC;MACDN,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CACX,0DAA0DE,QAAQ,EAAE,EACpEF,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEAhD,UAAUA,CAACwD,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAG1C,WAAW,CAACiF,UAAU,uBAAuBJ,QAAQ,EAAE;EACnE;EAEArD,WAAWA,CAACoD,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOkC,QAAQ;EACjB;EAEA;EACA/B,OAAOA,CAACyB,QAA4B;IAClC,OAAOA,QAAQ,GAAG,IAAI,CAACf,SAAS,CAAC2B,GAAG,CAACZ,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK;EACjE;;;uBAzFWrB,oBAAoB,EAAAhD,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtF,EAAA,CAAAkF,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAxF,EAAA,CAAAkF,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApB1C,oBAAoB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdjCjG,EAAA,CAAAE,cAAA,aAA6E;UAGzEF,EAAA,CAAAM,SAAA,aAEO;UAMPN,EAAA,CAAAE,cAAA,aAA4D;UAExDF,EAAA,CAAAM,SAAA,aAAmE;UAWrEN,EAAA,CAAAI,YAAA,EAAM;UAIVJ,EAAA,CAAAE,cAAA,cAA6C;UAQnCF,EAAA,CAAAG,MAAA,qBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,aAAwE;UACtEF,EAAA,CAAAG,MAAA,uEACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAE,cAAA,eAEC;UAECF,EAAA,CAAAM,SAAA,eAEO;UAEPN,EAAA,CAAAK,cAAA,EAMC;UANDL,EAAA,CAAAE,cAAA,eAMC;UACCF,EAAA,CAAAM,SAAA,gBAKE;UACJN,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAwB,UAAA,KAAA2E,oCAAA,mBAgEM;UAGNnG,EAAA,CAAAwB,UAAA,KAAA4E,oCAAA,mBAaM;UACRpG,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAwB,UAAA,KAAA6E,oCAAA,kBAUM;UAGNrG,EAAA,CAAAwB,UAAA,KAAA8E,oCAAA,kBAiCM;UAGNtG,EAAA,CAAAwB,UAAA,KAAA+E,oCAAA,kBAuMM;UACRvG,EAAA,CAAAI,YAAA,EAAM;;;UA7UuDJ,EAAA,CAAAO,SAAA,IAAgB;UAAhBP,EAAA,CAAAiB,UAAA,UAAAiF,GAAA,CAAA1C,SAAA,CAAgB;UAmEsExD,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAiB,UAAA,UAAAiF,GAAA,CAAA1C,SAAA,IAAA0C,GAAA,CAAAnD,OAAA,CAAAN,MAAA,KAAsC;UAiBjLzC,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAiB,UAAA,SAAAiF,GAAA,CAAA1C,SAAA,CAAe;UAclBxD,EAAA,CAAAO,SAAA,GAAwC;UAAxCP,EAAA,CAAAiB,UAAA,UAAAiF,GAAA,CAAA1C,SAAA,IAAA0C,GAAA,CAAAnD,OAAA,CAAAN,MAAA,OAAwC;UAoCxCzC,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAiB,UAAA,UAAAiF,GAAA,CAAA1C,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}