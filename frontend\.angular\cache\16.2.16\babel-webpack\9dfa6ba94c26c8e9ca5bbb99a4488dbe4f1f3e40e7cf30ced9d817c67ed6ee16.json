{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/file.service\";\nimport * as i4 from \"@angular/common\";\nfunction DetailProjectComponent_div_40_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"a\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 61);\n    i0.ɵɵelement(3, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r4.getFileUrl(file_r5), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailProjectComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h4\", 53);\n    i0.ɵɵtext(2, \"Fichiers joints\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 57);\n    i0.ɵɵtemplate(4, DetailProjectComponent_div_40_div_4_Template, 6, 1, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projet.fichiers);\n  }\n}\nfunction DetailProjectComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Aucun fichier joint \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailProjectComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"div\", 66);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const etudiant_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", (etudiant_r6.nom == null ? null : etudiant_r6.nom.charAt(0)) || \"\", \"\", (etudiant_r6.prenom == null ? null : etudiant_r6.prenom.charAt(0)) || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", etudiant_r6.prenom, \" \", etudiant_r6.nom, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(etudiant_r6.dateRendu));\n  }\n}\nfunction DetailProjectComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Aucun rendu pour le moment \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c1 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c2 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nconst _c3 = function () {\n  return [];\n};\nexport class DetailProjectComponent {\n  constructor(route, router, projectService, fileService) {\n    this.route = route;\n    this.router = router;\n    this.projectService = projectService;\n    this.fileService = fileService;\n    this.projet = null;\n  }\n  ngOnInit() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.projectService.getProjetById(id).subscribe(data => {\n        this.projet = data;\n      });\n    }\n  }\n  getFileUrl(filePath) {\n    return this.fileService.getDownloadUrl(filePath);\n  }\n  deleteProjet(id) {\n    if (!id) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      this.projectService.deleteProjet(id).subscribe({\n        next: () => {\n          alert('Projet supprimé avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du projet', err);\n          alert('Erreur lors de la suppression du projet');\n        }\n      });\n    }\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n  }\n  static {\n    this.ɵfac = function DetailProjectComponent_Factory(t) {\n      return new (t || DetailProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailProjectComponent,\n      selectors: [[\"app-detail-project\"]],\n      decls: 85,\n      vars: 26,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"p-6\", \"space-y-6\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-600\", \"mb-2\"], [1, \"text-gray-700\"], [4, \"ngIf\"], [\"class\", \"text-sm text-gray-500 italic\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-3\", \"pt-4\", \"border-t\", \"border-gray-100\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#3CAEA3]\", \"to-[#20BF55]\", \"hover:from-[#2d9b91]\", \"hover:to-[#18a046]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"routerLink\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#F5576C]\", \"to-[#F093FB]\", \"hover:from-[#e04054]\", \"hover:to-[#d87fe0]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"flex-1\", \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#8E2DE2]\", \"hover:from-[#5046e5]\", \"hover:to-[#7816c7]\", \"text-white\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"font-medium\", \"text-sm\", \"text-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"flex\", \"items-center\", \"justify-center\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"bg-white\", \"rounded-2xl\", \"shadow-md\", \"border\", \"border-[#e4e7ec]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"p-4\", \"text-white\"], [1, \"font-semibold\"], [1, \"flex\", \"justify-between\", \"mb-2\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-sm\", \"font-medium\", \"text-[#6C63FF]\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2.5\"], [1, \"bg-gradient-to-r\", \"from-[#6C63FF]\", \"to-[#C77DFF]\", \"h-2.5\", \"rounded-full\"], [1, \"grid\", \"grid-cols-2\", \"gap-4\"], [1, \"bg-[#f0f7ff]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4A00E0]\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"bg-[#fff5f5]\", \"p-4\", \"rounded-lg\", \"border\", \"border-[#e4e7ec]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#E02D6D]\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-600\", \"mb-3\"], [1, \"space-y-3\"], [\"class\", \"flex items-center space-x-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-sm text-gray-500 italic text-center py-2\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-3\"], [\"class\", \"flex items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\"], [\"download\", \"\", 1, \"flex-1\", \"text-center\", \"text-sm\", \"text-white\", \"bg-gradient-to-r\", \"from-[#8E2DE2]\", \"to-[#4A00E0]\", \"hover:from-[#7c22d2]\", \"hover:to-[#3f00cc]\", \"rounded-lg\", \"py-2\", \"px-4\", \"transition-all\", \"hover:scale-[1.02]\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", 3, \"href\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-sm\", \"text-gray-500\", \"italic\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-xs\", \"font-bold\"], [1, \"text-sm\"], [1, \"font-medium\"], [1, \"text-sm\", \"text-gray-500\", \"italic\", \"text-center\", \"py-2\"]],\n      template: function DetailProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n          i0.ɵɵtext(5, \"Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 5);\n          i0.ɵɵelement(7, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"span\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 12);\n          i0.ɵɵelement(15, \"path\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(16, \"div\")(17, \"h1\", 14);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 17);\n          i0.ɵɵelement(22, \"path\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"span\", 19);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 20);\n          i0.ɵɵelement(27, \"path\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"span\", 22);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(30, \"div\", 23)(31, \"span\", 24);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 25)(34, \"div\", 26)(35, \"div\")(36, \"h4\", 27);\n          i0.ɵɵtext(37, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\", 28);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(40, DetailProjectComponent_div_40_Template, 5, 1, \"div\", 29);\n          i0.ɵɵtemplate(41, DetailProjectComponent_div_41_Template, 2, 0, \"div\", 30);\n          i0.ɵɵelementStart(42, \"div\", 31)(43, \"a\", 32);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(44, \"svg\", 33);\n          i0.ɵɵelement(45, \"path\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Modifier \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(47, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function DetailProjectComponent_Template_button_click_47_listener() {\n            return ctx.deleteProjet(ctx.projet == null ? null : ctx.projet._id);\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(48, \"svg\", 33);\n          i0.ɵɵelement(49, \"path\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" Supprimer \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(51, \"a\", 37);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(52, \"svg\", 33);\n          i0.ɵɵelement(53, \"path\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(54, \" Voir les rendus \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(55, \"div\", 39)(56, \"div\", 40)(57, \"h3\", 41);\n          i0.ɵɵtext(58, \"Statistiques de rendu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 26)(60, \"div\")(61, \"div\", 42)(62, \"span\", 43);\n          i0.ɵɵtext(63, \"Progression\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 44);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 45);\n          i0.ɵɵelement(67, \"div\", 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 47)(69, \"div\", 48)(70, \"div\", 49);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 50);\n          i0.ɵɵtext(73, \"Rendus\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 51)(75, \"div\", 52);\n          i0.ɵɵtext(76);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 50);\n          i0.ɵɵtext(78, \"En attente\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"div\")(80, \"h4\", 53);\n          i0.ɵɵtext(81, \"Derniers rendus\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 54);\n          i0.ɵɵtemplate(83, DetailProjectComponent_div_83_Template, 8, 5, \"div\", 55);\n          i0.ɵɵtemplate(84, DetailProjectComponent_div_84_Template, 2, 0, \"div\", 56);\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.dateLimite) ? ctx.formatDate(ctx.projet == null ? null : ctx.projet.dateLimite) : \"Pas de date limite\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getProjectStatus(), \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.description) || \"Aucune description fournie\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.fichiers) || ctx.projet.fichiers.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(20, _c0, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(22, _c1))(\"queryParams\", i0.ɵɵpureFunction1(23, _c2, ctx.projet == null ? null : ctx.projet._id));\n          i0.ɵɵadvance(14);\n          i0.ɵɵtextInterpolate2(\"\", (ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0, \"/\", (ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0) / ((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 1) * 100, \"%\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(((ctx.projet == null ? null : ctx.projet.totalEtudiants) || 0) - ((ctx.projet == null ? null : ctx.projet.etudiantsRendus == null ? null : ctx.projet.etudiantsRendus.length) || 0));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", (ctx.projet == null ? null : ctx.projet.derniersRendus) || i0.ɵɵpureFunction0(25, _c3));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.derniersRendus) || ctx.projet.derniersRendus.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtcHJvamVjdC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvZGV0YWlsLXByb2plY3QvZGV0YWlsLXByb2plY3QuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r4", "getFileUrl", "file_r5", "ɵɵsanitizeUrl", "ɵɵtemplate", "DetailProjectComponent_div_40_div_4_Template", "ctx_r0", "projet", "fichiers", "ɵɵtextInterpolate2", "etudiant_r6", "nom", "char<PERSON>t", "prenom", "ɵɵtextInterpolate", "ctx_r2", "formatDate", "dateRendu", "DetailProjectComponent", "constructor", "route", "router", "projectService", "fileService", "ngOnInit", "id", "snapshot", "paramMap", "get", "getProjetById", "subscribe", "data", "filePath", "getDownloadUrl", "deleteProjet", "confirm", "next", "alert", "navigate", "error", "err", "console", "date", "d", "Date", "getDate", "toString", "padStart", "getMonth", "getFullYear", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ProjetService", "i3", "FileService", "selectors", "decls", "vars", "consts", "template", "DetailProjectComponent_Template", "rf", "ctx", "DetailProjectComponent_div_40_Template", "DetailProjectComponent_div_41_Template", "ɵɵlistener", "DetailProjectComponent_Template_button_click_47_listener", "_id", "DetailProjectComponent_div_83_Template", "DetailProjectComponent_div_84_Template", "titre", "ɵɵtextInterpolate1", "groupe", "dateLimite", "getStatusClass", "getProjectStatus", "description", "length", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction0", "_c1", "_c2", "etudiantsRendus", "totalEtudiants", "ɵɵstyleProp", "derniersRendus", "_c3"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\detail-project\\detail-project.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { FileService } from 'src/app/services/file.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-project',\r\n  templateUrl: './detail-project.component.html',\r\n  styleUrls: ['./detail-project.component.css'],\r\n})\r\nexport class DetailProjectComponent implements OnInit {\r\n  projet: any = null;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private projectService: ProjetService,\r\n    private fileService: FileService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const id = this.route.snapshot.paramMap.get('id');\r\n    if (id) {\r\n      this.projectService.getProjetById(id).subscribe((data: any) => {\r\n        this.projet = data;\r\n      });\r\n    }\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    return this.fileService.getDownloadUrl(filePath);\r\n  }\r\n\r\n  deleteProjet(id: string | undefined): void {\r\n    if (!id) return;\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\r\n      this.projectService.deleteProjet(id).subscribe({\r\n        next: () => {\r\n          alert('Projet supprimé avec succès');\r\n          this.router.navigate(['/admin/projects']);\r\n        },\r\n        error: (err) => {\r\n          console.error('Erreur lors de la suppression du projet', err);\r\n          alert('Erreur lors de la suppression du projet');\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  formatDate(date: string | Date): string {\r\n    const d = new Date(date);\r\n    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1)\r\n      .toString()\r\n      .padStart(2, '0')}/${d.getFullYear()}`;\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n\r\n    <!-- Header moderne avec breadcrumb -->\r\n    <div class=\"mb-8\">\r\n      <nav class=\"flex items-center space-x-2 text-sm text-text dark:text-dark-text-secondary mb-4\">\r\n        <a routerLink=\"/admin/projects/list-project\" class=\"hover:text-primary dark:hover:text-dark-accent-primary transition-colors\">Projets</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <span class=\"text-primary dark:text-dark-accent-primary font-medium\">{{ projet?.titre || 'Détails du projet' }}</span>\r\n      </nav>\r\n\r\n      <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n        <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\r\n          <div class=\"flex items-center space-x-4 mb-6 lg:mb-0\">\r\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\r\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\r\n                {{ projet?.titre || 'Chargement...' }}\r\n              </h1>\r\n              <div class=\"flex items-center space-x-4 mt-2\">\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-text-dark dark:text-dark-text-primary\">{{ projet?.groupe || 'Tous les groupes' }}</span>\r\n                </div>\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm text-text dark:text-dark-text-secondary\">{{ projet?.dateLimite ? formatDate(projet?.dateLimite) : 'Pas de date limite' }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Badge de statut -->\r\n          <div class=\"flex items-center space-x-3\">\r\n            <span [ngClass]=\"getStatusClass()\" class=\"px-4 py-2 rounded-xl text-sm font-medium\">\r\n              {{ getProjectStatus() }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Contenu principal -->\r\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n      <div class=\"p-6 space-y-6\">\r\n        <div>\r\n          <h4 class=\"text-sm font-semibold text-gray-600 mb-2\">Description</h4>\r\n          <p class=\"text-gray-700\">{{ projet?.description || 'Aucune description fournie' }}</p>\r\n        </div>\r\n\r\n        <div *ngIf=\"projet?.fichiers?.length > 0\">\r\n          <h4 class=\"text-sm font-semibold text-gray-600 mb-3\">Fichiers joints</h4>\r\n          <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\r\n            <div *ngFor=\"let file of projet.fichiers\" class=\"flex items-center\">\r\n              <a [href]=\"getFileUrl(file)\" download\r\n                class=\"flex-1 text-center text-sm text-white bg-gradient-to-r from-[#8E2DE2] to-[#4A00E0] hover:from-[#7c22d2] hover:to-[#3f00cc] rounded-lg py-2 px-4 transition-all hover:scale-[1.02] flex items-center justify-center space-x-2\">\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\" />\r\n                </svg>\r\n                <span>Télécharger</span>\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"!projet?.fichiers || projet.fichiers.length === 0\" class=\"text-sm text-gray-500 italic\">\r\n          Aucun fichier joint\r\n        </div>\r\n\r\n        <!-- Boutons d'action -->\r\n        <div class=\"flex flex-wrap gap-3 pt-4 border-t border-gray-100\">\r\n          <a [routerLink]=\"['/admin/projects/editProjet', projet?._id]\"\r\n             class=\"flex-1 bg-gradient-to-r from-[#3CAEA3] to-[#20BF55] hover:from-[#2d9b91] hover:to-[#18a046] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n            </svg>\r\n            Modifier\r\n          </a>\r\n\r\n          <button (click)=\"deleteProjet(projet?._id)\"\r\n                  class=\"flex-1 bg-gradient-to-r from-[#F5576C] to-[#F093FB] hover:from-[#e04054] hover:to-[#d87fe0] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\r\n            </svg>\r\n            Supprimer\r\n          </button>\r\n\r\n          <a [routerLink]=\"['/admin/projects/rendus']\" [queryParams]=\"{projetId: projet?._id}\"\r\n             class=\"flex-1 bg-gradient-to-r from-[#6C63FF] to-[#8E2DE2] hover:from-[#5046e5] hover:to-[#7816c7] text-white py-2.5 px-4 rounded-lg font-medium text-sm text-center shadow-sm hover:shadow-md transition-all flex items-center justify-center\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n            </svg>\r\n            Voir les rendus\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Dashboard de rendu -->\r\n    <div class=\"bg-white rounded-2xl shadow-md border border-[#e4e7ec] overflow-hidden\">\r\n      <div class=\"bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] p-4 text-white\">\r\n        <h3 class=\"font-semibold\">Statistiques de rendu</h3>\r\n      </div>\r\n      <div class=\"p-6 space-y-6\">\r\n        <!-- Progression générale -->\r\n        <div>\r\n          <div class=\"flex justify-between mb-2\">\r\n            <span class=\"text-sm font-medium text-gray-700\">Progression</span>\r\n            <span class=\"text-sm font-medium text-[#6C63FF]\">{{ (projet?.etudiantsRendus?.length || 0) }}/{{ projet?.totalEtudiants || 0 }}</span>\r\n          </div>\r\n          <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n            <div class=\"bg-gradient-to-r from-[#6C63FF] to-[#C77DFF] h-2.5 rounded-full\"\r\n                 [style.width.%]=\"((projet?.etudiantsRendus?.length || 0) / (projet?.totalEtudiants || 1) * 100)\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Cartes stats -->\r\n        <div class=\"grid grid-cols-2 gap-4\">\r\n          <div class=\"bg-[#f0f7ff] p-4 rounded-lg border border-[#e4e7ec]\">\r\n            <div class=\"text-2xl font-bold text-[#4A00E0]\">{{ projet?.etudiantsRendus?.length || 0 }}</div>\r\n            <div class=\"text-xs text-gray-500\">Rendus</div>\r\n          </div>\r\n          <div class=\"bg-[#fff5f5] p-4 rounded-lg border border-[#e4e7ec]\">\r\n            <div class=\"text-2xl font-bold text-[#E02D6D]\">{{ (projet?.totalEtudiants || 0) - (projet?.etudiantsRendus?.length || 0) }}</div>\r\n            <div class=\"text-xs text-gray-500\">En attente</div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Liste rapide -->\r\n        <div>\r\n          <h4 class=\"text-sm font-semibold text-gray-600 mb-3\">Derniers rendus</h4>\r\n          <div class=\"space-y-3\">\r\n            <div *ngFor=\"let etudiant of projet?.derniersRendus || []\" class=\"flex items-center space-x-3\">\r\n              <div class=\"h-8 w-8 rounded-full bg-[#6C63FF] flex items-center justify-center text-white text-xs font-bold\">\r\n                {{ etudiant.nom?.charAt(0) || '' }}{{ etudiant.prenom?.charAt(0) || '' }}\r\n              </div>\r\n              <div class=\"text-sm\">\r\n                <div class=\"font-medium\">{{ etudiant.prenom }} {{ etudiant.nom }}</div>\r\n                <div class=\"text-xs text-gray-500\">{{ formatDate(etudiant.dateRendu) }}</div>\r\n              </div>\r\n            </div>\r\n            <div *ngIf=\"!projet?.derniersRendus || projet.derniersRendus.length === 0\" class=\"text-sm text-gray-500 italic text-center py-2\">\r\n              Aucun rendu pour le moment\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";;;;;;;ICgEYA,EAAA,CAAAC,cAAA,cAAoE;IAGhED,EAAA,CAAAE,cAAA,EAA8G;IAA9GF,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAG,SAAA,eAA2I;IAC7IH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,4BAAW;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IALvBJ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,GAAAX,EAAA,CAAAY,aAAA,CAAyB;;;;;IAJlCZ,EAAA,CAAAC,cAAA,UAA0C;IACaD,EAAA,CAAAM,MAAA,sBAAe;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACzEJ,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAa,UAAA,IAAAC,4CAAA,kBAQM;IACRd,EAAA,CAAAI,YAAA,EAAM;;;;IATkBJ,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,UAAA,YAAAO,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;;;;;IAY5CjB,EAAA,CAAAC,cAAA,cAAoG;IAClGD,EAAA,CAAAM,MAAA,4BACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;IAiEFJ,EAAA,CAAAC,cAAA,cAA+F;IAE3FD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAqB;IACMD,EAAA,CAAAM,MAAA,GAAwC;IAAAN,EAAA,CAAAI,YAAA,EAAM;IACvEJ,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAM,MAAA,GAAoC;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;IAJ7EJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAkB,kBAAA,OAAAC,WAAA,CAAAC,GAAA,kBAAAD,WAAA,CAAAC,GAAA,CAAAC,MAAA,iBAAAF,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAD,MAAA,gBACF;IAE2BrB,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAkB,kBAAA,KAAAC,WAAA,CAAAG,MAAA,OAAAH,WAAA,CAAAC,GAAA,KAAwC;IAC9BpB,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAN,WAAA,CAAAO,SAAA,EAAoC;;;;;IAG3E1B,EAAA,CAAAC,cAAA,cAAiI;IAC/HD,EAAA,CAAAM,MAAA,mCACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;;;;ADhJlB,OAAM,MAAOuB,sBAAsB;EAGjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA6B,EAC7BC,WAAwB;IAHxB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IANrB,KAAAhB,MAAM,GAAQ,IAAI;EAOf;EAEHiB,QAAQA,CAAA;IACN,MAAMC,EAAE,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACH,cAAc,CAACO,aAAa,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAEC,IAAS,IAAI;QAC5D,IAAI,CAACxB,MAAM,GAAGwB,IAAI;MACpB,CAAC,CAAC;;EAEN;EAEA9B,UAAUA,CAAC+B,QAAgB;IACzB,OAAO,IAAI,CAACT,WAAW,CAACU,cAAc,CAACD,QAAQ,CAAC;EAClD;EAEAE,YAAYA,CAACT,EAAsB;IACjC,IAAI,CAACA,EAAE,EAAE;IAET,IAAIU,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7D,IAAI,CAACb,cAAc,CAACY,YAAY,CAACT,EAAE,CAAC,CAACK,SAAS,CAAC;QAC7CM,IAAI,EAAEA,CAAA,KAAK;UACTC,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC;QACDC,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;UAC7DH,KAAK,CAAC,yCAAyC,CAAC;QAClD;OACD,CAAC;;EAEN;EAEArB,UAAUA,CAAC0B,IAAmB;IAC5B,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAO,GAAGC,CAAC,CAACE,OAAO,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACJ,CAAC,CAACK,QAAQ,EAAE,GAAG,CAAC,EACnEF,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,CAAC,CAACM,WAAW,EAAE,EAAE;EAC1C;;;uBA7CW/B,sBAAsB,EAAA3B,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA9D,EAAA,CAAA2D,iBAAA,CAAAI,EAAA,CAAAC,aAAA,GAAAhE,EAAA,CAAA2D,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtBvC,sBAAsB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTnCzE,EAAA,CAAAC,cAAA,aAAiK;UAM3BD,EAAA,CAAAM,MAAA,cAAO;UAAAN,EAAA,CAAAI,YAAA,EAAI;UACzIJ,EAAA,CAAAE,cAAA,EAA2E;UAA3EF,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAG,SAAA,cAA8F;UAChGH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAqE;UAArEL,EAAA,CAAAC,cAAA,cAAqE;UAAAD,EAAA,CAAAM,MAAA,GAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAGxHJ,EAAA,CAAAC,cAAA,cAA2J;UAInJD,EAAA,CAAAE,cAAA,EAAsF;UAAtFF,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAAG,SAAA,gBAA4J;UAC9JH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,eAA8C;UAE1CD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAAwV;UAC1VH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA6E;UAA7EL,EAAA,CAAAC,cAAA,gBAA6E;UAAAD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAEhIJ,EAAA,CAAAC,cAAA,eAAyC;UACvCD,EAAA,CAAAE,cAAA,EAA0G;UAA1GF,EAAA,CAAAC,cAAA,eAA0G;UACxGD,EAAA,CAAAG,SAAA,gBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA8D;UAA9DL,EAAA,CAAAC,cAAA,gBAA8D;UAAAD,EAAA,CAAAM,MAAA,IAAgF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAO7JJ,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAM,MAAA,IACF;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAOfJ,EAAA,CAAAC,cAAA,eAAmD;UAGQD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACrEJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAM,MAAA,IAAyD;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAGxFJ,EAAA,CAAAa,UAAA,KAAA8D,sCAAA,kBAaM;UAEN3E,EAAA,CAAAa,UAAA,KAAA+D,sCAAA,kBAEM;UAGN5E,EAAA,CAAAC,cAAA,eAAgE;UAG5DD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAmM;UACrMH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,kBACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAEJJ,EAAA,CAAAK,eAAA,EACwP;UADxPL,EAAA,CAAAC,cAAA,kBACwP;UADhPD,EAAA,CAAA6E,UAAA,mBAAAC,yDAAA;YAAA,OAASJ,GAAA,CAAA/B,YAAA,CAAA+B,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA,CAAyB;UAAA,EAAC;UAEzC/E,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAAyM;UAC3MH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,mBACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAK,eAAA,EACmP;UADnPL,EAAA,CAAAC,cAAA,aACmP;UACjPD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAA4M;UAC9MH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,yBACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAMVJ,EAAA,CAAAK,eAAA,EAAoF;UAApFL,EAAA,CAAAC,cAAA,eAAoF;UAEtDD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAEtDJ,EAAA,CAAAC,cAAA,eAA2B;UAI2BD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAClEJ,EAAA,CAAAC,cAAA,gBAAiD;UAAAD,EAAA,CAAAM,MAAA,IAA8E;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAExIJ,EAAA,CAAAC,cAAA,eAAmD;UACjDD,EAAA,CAAAG,SAAA,eAC4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,eAAoC;UAEeD,EAAA,CAAAM,MAAA,IAA0C;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAC/FJ,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAM,MAAA,cAAM;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAEjDJ,EAAA,CAAAC,cAAA,eAAiE;UAChBD,EAAA,CAAAM,MAAA,IAA4E;UAAAN,EAAA,CAAAI,YAAA,EAAM;UACjIJ,EAAA,CAAAC,cAAA,eAAmC;UAAAD,EAAA,CAAAM,MAAA,kBAAU;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAKvDJ,EAAA,CAAAC,cAAA,WAAK;UACkDD,EAAA,CAAAM,MAAA,uBAAe;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACzEJ,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAa,UAAA,KAAAmE,sCAAA,kBAQM;UACNhF,EAAA,CAAAa,UAAA,KAAAoE,sCAAA,kBAEM;UACRjF,EAAA,CAAAI,YAAA,EAAM;;;UAhJ6DJ,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAuB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAkE,KAAA,8BAA0C;UAavGlF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAmF,kBAAA,OAAAT,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAkE,KAAA,0BACF;UAMiFlF,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAuB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAoE,MAAA,wBAA0C;UAMzDpF,EAAA,CAAAO,SAAA,GAAgF;UAAhFP,EAAA,CAAAuB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAqE,UAAA,IAAAX,GAAA,CAAAjD,UAAA,CAAAiD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAqE,UAAA,yBAAgF;UAQ9IrF,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAQ,UAAA,YAAAkE,GAAA,CAAAY,cAAA,GAA4B;UAChCtF,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAmF,kBAAA,MAAAT,GAAA,CAAAa,gBAAA,QACF;UAWuBvF,EAAA,CAAAO,SAAA,GAAyD;UAAzDP,EAAA,CAAAuB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAwE,WAAA,kCAAyD;UAG9ExF,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,UAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,kBAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAwE,MAAA,MAAkC;UAelCzF,EAAA,CAAAO,SAAA,GAAuD;UAAvDP,EAAA,CAAAQ,UAAA,WAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAC,QAAA,KAAAyD,GAAA,CAAA1D,MAAA,CAAAC,QAAA,CAAAwE,MAAA,OAAuD;UAMxDzF,EAAA,CAAAO,SAAA,GAA0D;UAA1DP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAA0F,eAAA,KAAAC,GAAA,EAAAjB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA,EAA0D;UAgB1D/E,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAA4F,eAAA,KAAAC,GAAA,EAAyC,gBAAA7F,EAAA,CAAA0F,eAAA,KAAAI,GAAA,EAAApB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+D,GAAA;UAqBO/E,EAAA,CAAAO,SAAA,IAA8E;UAA9EP,EAAA,CAAAkB,kBAAA,MAAAwD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,kBAAArB,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,CAAAN,MAAA,cAAAf,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAgF,cAAA,WAA8E;UAI1HhG,EAAA,CAAAO,SAAA,GAAgG;UAAhGP,EAAA,CAAAiG,WAAA,YAAAvB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,kBAAArB,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,CAAAN,MAAA,YAAAf,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAgF,cAAA,mBAAgG;UAOtDhG,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAuB,iBAAA,EAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,kBAAArB,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,CAAAN,MAAA,OAA0C;UAI1CzF,EAAA,CAAAO,SAAA,GAA4E;UAA5EP,EAAA,CAAAuB,iBAAA,GAAAmD,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAgF,cAAA,YAAAtB,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,kBAAArB,GAAA,CAAA1D,MAAA,CAAA+E,eAAA,CAAAN,MAAA,QAA4E;UASjGzF,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAQ,UAAA,aAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAkF,cAAA,KAAAlG,EAAA,CAAA4F,eAAA,KAAAO,GAAA,EAA+B;UASnDnG,EAAA,CAAAO,SAAA,GAAmE;UAAnEP,EAAA,CAAAQ,UAAA,WAAAkE,GAAA,CAAA1D,MAAA,kBAAA0D,GAAA,CAAA1D,MAAA,CAAAkF,cAAA,KAAAxB,GAAA,CAAA1D,MAAA,CAAAkF,cAAA,CAAAT,MAAA,OAAmE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}