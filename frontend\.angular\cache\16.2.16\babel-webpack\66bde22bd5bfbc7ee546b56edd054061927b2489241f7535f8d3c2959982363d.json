{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_30_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 38);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 39);\n    i0.ɵɵelement(2, \"path\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r7, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(fichier_r7.split(\"/\").pop());\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 32);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 33);\n    i0.ɵɵelement(3, \"path\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 36);\n    i0.ɵɵtemplate(7, ProjectEvaluationComponent_div_6_div_30_a_7_Template, 5, 2, \"a\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Fichiers joints (\", ctx_r3.rendu.fichiers.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getFieldError(\"scores.structure\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getFieldError(\"scores.pratiques\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getFieldError(\"scores.fonctionnalite\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getFieldError(\"scores.originalite\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"commentaires\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 76);\n    i0.ɵɵelement(2, \"path\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Soumettre l'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 78);\n    i0.ɵɵelement(2, \"circle\", 79)(3, \"path\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Soumission en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"form\", 42);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_6_form_49_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 43)(2, \"h3\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 45);\n    i0.ɵɵelement(4, \"path\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Crit\\u00E8res d'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\", 47)(7, \"div\", 48)(8, \"label\", 49);\n    i0.ɵɵtext(9, \" Structure du code \");\n    i0.ɵɵelementStart(10, \"span\", 50);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 51);\n    i0.ɵɵelement(13, \"input\", 52);\n    i0.ɵɵelementStart(14, \"div\", 53)(15, \"span\", 54);\n    i0.ɵɵtext(16, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, ProjectEvaluationComponent_div_6_form_49_div_17_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 48)(19, \"label\", 49);\n    i0.ɵɵtext(20, \" Bonnes pratiques \");\n    i0.ɵɵelementStart(21, \"span\", 50);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 51);\n    i0.ɵɵelement(24, \"input\", 56);\n    i0.ɵɵelementStart(25, \"div\", 53)(26, \"span\", 54);\n    i0.ɵɵtext(27, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, ProjectEvaluationComponent_div_6_form_49_div_28_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 48)(30, \"label\", 49);\n    i0.ɵɵtext(31, \" Fonctionnalit\\u00E9 \");\n    i0.ɵɵelementStart(32, \"span\", 50);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 51);\n    i0.ɵɵelement(35, \"input\", 57);\n    i0.ɵɵelementStart(36, \"div\", 53)(37, \"span\", 54);\n    i0.ɵɵtext(38, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(39, ProjectEvaluationComponent_div_6_form_49_div_39_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 48)(41, \"label\", 49);\n    i0.ɵɵtext(42, \" Originalit\\u00E9 \");\n    i0.ɵɵelementStart(43, \"span\", 50);\n    i0.ɵɵtext(44, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 51);\n    i0.ɵɵelement(46, \"input\", 58);\n    i0.ɵɵelementStart(47, \"div\", 53)(48, \"span\", 54);\n    i0.ɵɵtext(49, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(50, ProjectEvaluationComponent_div_6_form_49_div_50_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 59)(52, \"div\", 60)(53, \"span\", 61);\n    i0.ɵɵtext(54, \"Score total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 23)(56, \"span\", 62);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"span\", 63);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 64);\n    i0.ɵɵelement(61, \"div\", 65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"div\", 48)(63, \"label\", 49);\n    i0.ɵɵtext(64, \" Commentaires d\\u00E9taill\\u00E9s \");\n    i0.ɵɵelementStart(65, \"span\", 50);\n    i0.ɵɵtext(66, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(67, \"textarea\", 66);\n    i0.ɵɵtemplate(68, ProjectEvaluationComponent_div_6_form_49_div_68_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementStart(69, \"p\", 67);\n    i0.ɵɵtext(70, \"D\\u00E9crivez les points forts et les axes d'am\\u00E9lioration du projet.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 68)(72, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_form_49_Template_button_click_72_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.annuler());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(73, \"svg\", 70);\n    i0.ɵɵelement(74, \"path\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(75, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(76, \"button\", 72);\n    i0.ɵɵtemplate(77, ProjectEvaluationComponent_div_6_form_49_span_77_Template, 4, 0, \"span\", 73);\n    i0.ɵɵtemplate(78, ProjectEvaluationComponent_div_6_form_49_span_78_Template, 5, 0, \"span\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.evaluationForm);\n    i0.ɵɵadvance(13);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.structure\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.structure\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.pratiques\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.pratiques\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.fonctionnalite\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.fonctionnalite\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"scores.originalite\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"scores.originalite\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.getScoreTotal());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"/\", ctx_r4.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.getScoreTotal() / ctx_r4.getScoreMaximum() * 100, \"%\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r4.isFieldInvalid(\"commentaires\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isFieldInvalid(\"commentaires\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.evaluationForm.invalid || ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_50_div_1_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 76);\n    i0.ɵɵelement(2, \"path\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Lancer l'\\u00E9valuation IA \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_50_div_1_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 78);\n    i0.ɵɵelement(2, \"circle\", 79)(3, \"path\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Lancement en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_50_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 83);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 84);\n    i0.ɵɵelement(4, \"path\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 85);\n    i0.ɵɵtext(6, \"\\u00C9valuation automatique par IA\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 86)(8, \"div\", 87)(9, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 89);\n    i0.ɵɵelement(11, \"path\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"div\", 91)(13, \"h4\", 92);\n    i0.ɵɵtext(14, \"Comment \\u00E7a fonctionne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 93);\n    i0.ɵɵtext(16, \"Notre syst\\u00E8me d'IA (Mistral 7B) analysera automatiquement le code soumis selon les crit\\u00E8res suivants :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ul\", 94)(18, \"li\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 95);\n    i0.ɵɵelement(20, \"path\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Structure et organisation du code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"li\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 95);\n    i0.ɵɵelement(24, \"path\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Respect des bonnes pratiques \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"li\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(27, \"svg\", 95);\n    i0.ɵɵelement(28, \"path\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \" Fonctionnalit\\u00E9s impl\\u00E9ment\\u00E9es \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"li\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(31, \"svg\", 95);\n    i0.ɵɵelement(32, \"path\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Originalit\\u00E9 et cr\\u00E9ativit\\u00E9 \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(34, \"div\", 97)(35, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_div_50_div_1_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.annuler());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 70);\n    i0.ɵɵelement(37, \"path\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_div_50_div_1_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.onSubmit());\n    });\n    i0.ɵɵtemplate(40, ProjectEvaluationComponent_div_6_div_50_div_1_span_40_Template, 4, 0, \"span\", 73);\n    i0.ɵɵtemplate(41, ProjectEvaluationComponent_div_6_div_50_div_1_span_41_Template, 5, 0, \"span\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(39);\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r18.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_50_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"div\", 101);\n    i0.ɵɵelementStart(3, \"div\", 102);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 103);\n    i0.ɵɵelement(5, \"path\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h3\", 104);\n    i0.ɵɵtext(7, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 105);\n    i0.ɵɵtext(9, \"Notre syst\\u00E8me examine le code selon les crit\\u00E8res d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 106)(11, \"div\", 107);\n    i0.ɵɵelement(12, \"div\", 108)(13, \"div\", 109)(14, \"div\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 111);\n    i0.ɵɵtext(16, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_6_div_50_div_1_Template, 42, 3, \"div\", 5);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_6_div_50_div_2_Template, 17, 0, \"div\", 82);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 12);\n    i0.ɵɵelement(5, \"path\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h2\", 14);\n    i0.ɵɵtext(7, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"div\", 16)(10, \"p\", 17);\n    i0.ɵɵtext(11, \"Projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 18);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 16)(15, \"p\", 17);\n    i0.ɵɵtext(16, \"\\u00C9tudiant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 18);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 16)(20, \"p\", 17);\n    i0.ɵɵtext(21, \"Date de soumission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 18);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 16)(26, \"p\", 17);\n    i0.ɵɵtext(27, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 18);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(30, ProjectEvaluationComponent_div_6_div_30_Template, 8, 2, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 20)(32, \"div\", 21)(33, \"div\", 22)(34, \"div\", 23)(35, \"div\", 24);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 12);\n    i0.ɵɵelement(37, \"path\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(38, \"h2\", 14);\n    i0.ɵɵtext(39, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 26)(41, \"span\");\n    i0.ɵɵtext(42, \" Manuel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵtext(44, \" IA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.toggleEvaluationMode());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(46, \"svg\", 28);\n    i0.ɵɵelement(47, \"path\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Changer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(49, ProjectEvaluationComponent_div_6_form_49_Template, 79, 28, \"form\", 30);\n    i0.ɵɵtemplate(50, ProjectEvaluationComponent_div_6_div_50_Template, 3, 2, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.projet.titre);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(24, 14, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.description || \"Aucune description\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassMapInterpolate1(\"px-3 py-2 text-sm font-medium \", ctx_r2.evaluationMode === \"manual\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600\", \" rounded-md transition-all duration-200\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"px-3 py-2 text-sm font-medium \", ctx_r2.evaluationMode === \"ai\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600\", \" rounded-md transition-all duration-200\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.successMessage = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n    console.log('Form values:', this.evaluationForm.value);\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\n      console.log('Form is invalid, marking fields as touched');\n      this.markFormGroupTouched(this.evaluationForm);\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\n      return;\n    }\n    this.isSubmitting = true;\n    this.error = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    console.log('Sending evaluation data:', evaluationData);\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/list-rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/list-rendus']);\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldError(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return 'Ce champ est obligatoire';\n      }\n      if (field.errors['min']) {\n        return `La valeur minimum est ${field.errors['min'].min}`;\n      }\n      if (field.errors['max']) {\n        return `La valeur maximum est ${field.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 7,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-lg\", \"shadow\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-6\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-blue-500\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"mb-8\", \"bg-gradient-to-r\", \"from-blue-50\", \"to-purple-50\", \"rounded-xl\", \"p-6\", \"border\", \"border-blue-100\"], [1, \"flex\", \"items-center\", \"mb-4\"], [1, \"bg-blue-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-2xl\", \"font-semibold\", \"text-gray-800\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\", \"mb-4\"], [1, \"bg-white\", \"p-4\", \"rounded-lg\", \"shadow-sm\"], [1, \"text-sm\", \"text-gray-600\", \"mb-1\"], [1, \"font-semibold\", \"text-gray-800\"], [\"class\", \"bg-white p-4 rounded-lg shadow-sm\", 4, \"ngIf\"], [1, \"mb-8\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-sm\", \"border\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-6\"], [1, \"flex\", \"items-center\"], [1, \"bg-purple-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"rounded-lg\", \"p-1\"], [1, \"ml-2\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"text-white\", \"rounded-lg\", \"hover:from-purple-600\", \"hover:to-blue-600\", \"transition-all\", \"duration-200\", \"shadow-sm\", \"hover:shadow-md\", \"transform\", \"hover:scale-105\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"], [\"class\", \"space-y-6\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-600\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"], [1, \"font-medium\", \"text-gray-800\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-2\"], [\"target\", \"_blank\", \"class\", \"flex items-center p-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200 text-blue-700 hover:text-blue-800\", 3, \"href\", 4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"flex\", \"items-center\", \"p-2\", \"bg-blue-50\", \"hover:bg-blue-100\", \"rounded-lg\", \"transition-colors\", \"duration-200\", \"text-blue-700\", \"hover:text-blue-800\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"truncate\"], [1, \"space-y-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"bg-gray-50\", \"rounded-lg\", \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"mb-4\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\", \"text-purple-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"form-group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [1, \"text-red-500\"], [1, \"relative\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-3\"], [1, \"text-sm\", \"text-gray-500\"], [\"class\", \"mt-1 text-sm text-red-600\", 4, \"ngIf\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [1, \"mt-6\", \"p-4\", \"bg-white\", \"rounded-lg\", \"border-2\", \"border-purple-200\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-700\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-600\"], [1, \"text-lg\", \"text-gray-500\", \"ml-1\"], [1, \"mt-2\", \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2\"], [1, \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"h-2\", \"rounded-full\", \"transition-all\", \"duration-300\"], [\"formControlName\", \"commentaires\", \"rows\", \"6\", \"placeholder\", \"Saisissez vos commentaires d\\u00E9taill\\u00E9s sur l'\\u00E9valuation...\"], [1, \"mt-2\", \"text-sm\", \"text-gray-500\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\", \"pt-6\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", 1, \"w-full\", \"sm:w-auto\", \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"text-gray-700\", \"rounded-lg\", \"hover:bg-gray-50\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"type\", \"submit\", 1, \"w-full\", \"sm:w-auto\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-green-500\", \"to-emerald-500\", \"text-white\", \"rounded-lg\", \"hover:from-green-600\", \"hover:to-emerald-600\", \"transition-all\", \"duration-200\", \"font-semibold\", \"shadow-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", \"disabled:shadow-none\", 3, \"disabled\"], [\"class\", \"flex items-center justify-center\", 4, \"ngIf\"], [1, \"mt-1\", \"text-sm\", \"text-red-600\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"bg-gradient-to-br\", \"from-indigo-50\", \"to-purple-50\", \"rounded-xl\", \"p-6\", \"border\", \"border-indigo-200\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [1, \"bg-indigo-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-white\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-800\"], [1, \"bg-white\", \"rounded-lg\", \"p-4\", \"mb-6\", \"border\", \"border-indigo-100\"], [1, \"flex\", \"items-start\"], [1, \"flex-shrink-0\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-indigo-500\", \"mt-0.5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"ml-3\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\", \"mb-1\"], [1, \"text-sm\", \"text-gray-600\", \"mb-2\"], [1, \"text-sm\", \"text-gray-600\", \"space-y-1\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"w-3\", \"h-3\", \"text-green-500\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\"], [1, \"w-full\", \"sm:w-auto\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-indigo-500\", \"to-purple-500\", \"text-white\", \"rounded-lg\", \"hover:from-indigo-600\", \"hover:to-purple-600\", \"transition-all\", \"duration-200\", \"font-semibold\", \"shadow-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", \"disabled:shadow-none\", 3, \"disabled\", \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"text-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-t-4\", \"border-b-4\", \"border-indigo-500\", \"mx-auto\", \"mb-6\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-indigo-500\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"mb-2\"], [1, \"text-gray-600\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"p-4\", \"max-w-md\", \"mx-auto\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"text-sm\", \"text-gray-500\", \"mt-2\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_4_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ProjectEvaluationComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵtemplate(6, ProjectEvaluationComponent_div_6_Template, 51, 17, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n.fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.5s ease-out;\\n}\\n\\n.pulse-animation[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);\\n  border-color: #8b5cf6;\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);\\n}\\n\\n\\n\\n.card-hover[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.card-hover[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #8b5cf6, #3b82f6);\\n  transition: width 0.5s ease;\\n}\\n\\n\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\\n}\\n\\n.alert-error[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n\\n  .grid-responsive[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n\\n\\n.icon-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵproperty", "fichier_r7", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "split", "pop", "ɵɵtemplate", "ProjectEvaluationComponent_div_6_div_30_a_7_Template", "ctx_r3", "rendu", "fichiers", "length", "ctx_r8", "getFieldError", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ɵɵlistener", "ProjectEvaluationComponent_div_6_form_49_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProjectEvaluationComponent_div_6_form_49_div_17_Template", "ProjectEvaluationComponent_div_6_form_49_div_28_Template", "ProjectEvaluationComponent_div_6_form_49_div_39_Template", "ProjectEvaluationComponent_div_6_form_49_div_50_Template", "ProjectEvaluationComponent_div_6_form_49_div_68_Template", "ProjectEvaluationComponent_div_6_form_49_Template_button_click_72_listener", "ctx_r17", "annuler", "ProjectEvaluationComponent_div_6_form_49_span_77_Template", "ProjectEvaluationComponent_div_6_form_49_span_78_Template", "ctx_r4", "evaluationForm", "ɵɵclassMapInterpolate1", "isFieldInvalid", "getScoreTotal", "getScoreMaximum", "ɵɵstyleProp", "invalid", "isSubmitting", "ProjectEvaluationComponent_div_6_div_50_div_1_Template_button_click_35_listener", "_r23", "ctx_r22", "ProjectEvaluationComponent_div_6_div_50_div_1_Template_button_click_39_listener", "ctx_r24", "ProjectEvaluationComponent_div_6_div_50_div_1_span_40_Template", "ProjectEvaluationComponent_div_6_div_50_div_1_span_41_Template", "ctx_r18", "ProjectEvaluationComponent_div_6_div_50_div_1_Template", "ProjectEvaluationComponent_div_6_div_50_div_2_Template", "ctx_r5", "aiProcessing", "ProjectEvaluationComponent_div_6_div_30_Template", "ProjectEvaluationComponent_div_6_Template_button_click_45_listener", "_r26", "ctx_r25", "toggleEvaluationMode", "ProjectEvaluationComponent_div_6_form_49_Template", "ProjectEvaluationComponent_div_6_div_50_Template", "ctx_r2", "projet", "titre", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "evaluationMode", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "isLoading", "successMessage", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "err", "console", "log", "valid", "value", "markFormGroupTouched", "evaluationData", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "field", "dirty", "touched", "errors", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ProjectEvaluationComponent_div_4_Template", "ProjectEvaluationComponent_div_5_Template", "ProjectEvaluationComponent_div_6_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  successMessage: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\r\n    console.log('Form values:', this.evaluationForm.value);\r\n\r\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\r\n      console.log('Form is invalid, marking fields as touched');\r\n      this.markFormGroupTouched(this.evaluationForm);\r\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n    console.log('Sending evaluation data:', evaluationData);\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/list-rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    this.router.navigate(['/admin/projects/list-rendus']);\r\n  }\r\n\r\n  markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    if (field && field.errors && (field.dirty || field.touched)) {\r\n      if (field.errors['required']) {\r\n        return 'Ce champ est obligatoire';\r\n      }\r\n      if (field.errors['min']) {\r\n        return `La valeur minimum est ${field.errors['min'].min}`;\r\n      }\r\n      if (field.errors['max']) {\r\n        return `La valeur maximum est ${field.errors['max'].max}`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"container mx-auto px-4 py-6\">\r\n  <div class=\"max-w-4xl mx-auto bg-white rounded-lg shadow p-6\">\r\n    <h1 class=\"text-2xl font-bold text-gray-800 mb-6\">Évaluation du projet</h1>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\r\n      <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n    </div>\r\n\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error }}\r\n    </div>\r\n\r\n    <div *ngIf=\"rendu && !isLoading\">\r\n      <div class=\"mb-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100\">\r\n        <div class=\"flex items-center mb-4\">\r\n          <div class=\"bg-blue-500 p-2 rounded-lg mr-3\">\r\n            <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n            </svg>\r\n          </div>\r\n          <h2 class=\"text-2xl font-semibold text-gray-800\">Informations sur le rendu</h2>\r\n        </div>\r\n\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Projet</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.projet.titre }}</p>\r\n          </div>\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Étudiant</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n          </div>\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Date de soumission</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n          </div>\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Description</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.description || 'Aucune description' }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n          <div class=\"flex items-center mb-3\">\r\n            <svg class=\"w-5 h-5 text-gray-600 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"></path>\r\n            </svg>\r\n            <h3 class=\"font-medium text-gray-800\">Fichiers joints ({{ rendu.fichiers.length }})</h3>\r\n          </div>\r\n          <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\r\n            <a *ngFor=\"let fichier of rendu.fichiers\"\r\n               [href]=\"'http://localhost:3000/' + fichier\"\r\n               target=\"_blank\"\r\n               class=\"flex items-center p-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200 text-blue-700 hover:text-blue-800\">\r\n              <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n              <span class=\"text-sm truncate\">{{ fichier.split('/').pop() }}</span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-8\">\r\n        <div class=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n          <div class=\"flex items-center justify-between mb-6\">\r\n            <div class=\"flex items-center\">\r\n              <div class=\"bg-purple-500 p-2 rounded-lg mr-3\">\r\n                <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h2 class=\"text-2xl font-semibold text-gray-800\">Mode d'évaluation</h2>\r\n            </div>\r\n            <div class=\"flex items-center bg-gray-100 rounded-lg p-1\">\r\n              <span class=\"px-3 py-2 text-sm font-medium {{ evaluationMode === 'manual' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600' }} rounded-md transition-all duration-200\">\r\n                Manuel\r\n              </span>\r\n              <span class=\"px-3 py-2 text-sm font-medium {{ evaluationMode === 'ai' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600' }} rounded-md transition-all duration-200\">\r\n                IA\r\n              </span>\r\n              <button\r\n                (click)=\"toggleEvaluationMode()\"\r\n                class=\"ml-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n              >\r\n                <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"></path>\r\n                </svg>\r\n                Changer\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\" class=\"space-y-6\">\r\n            <div class=\"bg-gray-50 rounded-lg p-6\">\r\n              <h3 class=\"text-lg font-semibold text-gray-800 mb-4 flex items-center\">\r\n                <svg class=\"w-5 h-5 mr-2 text-purple-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\r\n                </svg>\r\n                Critères d'évaluation\r\n              </h3>\r\n\r\n              <div formGroupName=\"scores\" class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Structure du code\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"structure\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.structure') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.structure')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.structure') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Bonnes pratiques\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"pratiques\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.pratiques') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.pratiques')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.pratiques') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Fonctionnalité\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"fonctionnalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.fonctionnalite') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.fonctionnalite')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.fonctionnalite') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Originalité\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"originalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.originalite') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.originalite')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.originalite') }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Score total display -->\r\n              <div class=\"mt-6 p-4 bg-white rounded-lg border-2 border-purple-200\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <span class=\"text-lg font-semibold text-gray-700\">Score total:</span>\r\n                  <div class=\"flex items-center\">\r\n                    <span class=\"text-2xl font-bold text-purple-600\">{{ getScoreTotal() }}</span>\r\n                    <span class=\"text-lg text-gray-500 ml-1\">/{{ getScoreMaximum() }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"mt-2 w-full bg-gray-200 rounded-full h-2\">\r\n                  <div class=\"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300\"\r\n                       [style.width.%]=\"(getScoreTotal() / getScoreMaximum()) * 100\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                Commentaires détaillés\r\n                <span class=\"text-red-500\">*</span>\r\n              </label>\r\n              <textarea\r\n                formControlName=\"commentaires\"\r\n                rows=\"6\"\r\n                class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('commentaires') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                placeholder=\"Saisissez vos commentaires détaillés sur l'évaluation...\"\r\n              ></textarea>\r\n              <div *ngIf=\"isFieldInvalid('commentaires')\" class=\"mt-1 text-sm text-red-600\">\r\n                {{ getFieldError('commentaires') }}\r\n              </div>\r\n              <p class=\"mt-2 text-sm text-gray-500\">Décrivez les points forts et les axes d'amélioration du projet.</p>\r\n            </div>\r\n\r\n            <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center pt-6 border-t border-gray-200\">\r\n              <button\r\n                type=\"button\"\r\n                (click)=\"annuler()\"\r\n                class=\"w-full sm:w-auto px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium\"\r\n              >\r\n                <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                </svg>\r\n                Annuler\r\n              </button>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                [disabled]=\"evaluationForm.invalid || isSubmitting\"\r\n                class=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none\"\r\n              >\r\n                <span *ngIf=\"!isSubmitting\" class=\"flex items-center justify-center\">\r\n                  <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  Soumettre l'évaluation\r\n                </span>\r\n                <span *ngIf=\"isSubmitting\" class=\"flex items-center justify-center\">\r\n                  <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                    <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Soumission en cours...\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </form>\r\n\r\n          <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200\">\r\n            <div *ngIf=\"!aiProcessing\">\r\n              <div class=\"flex items-center mb-4\">\r\n                <div class=\"bg-indigo-500 p-2 rounded-lg mr-3\">\r\n                  <svg class=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <h3 class=\"text-xl font-semibold text-gray-800\">Évaluation automatique par IA</h3>\r\n              </div>\r\n\r\n              <div class=\"bg-white rounded-lg p-4 mb-6 border border-indigo-100\">\r\n                <div class=\"flex items-start\">\r\n                  <div class=\"flex-shrink-0\">\r\n                    <svg class=\"w-5 h-5 text-indigo-500 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"ml-3\">\r\n                    <h4 class=\"text-sm font-medium text-gray-900 mb-1\">Comment ça fonctionne</h4>\r\n                    <p class=\"text-sm text-gray-600 mb-2\">Notre système d'IA (Mistral 7B) analysera automatiquement le code soumis selon les critères suivants :</p>\r\n                    <ul class=\"text-sm text-gray-600 space-y-1\">\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Structure et organisation du code\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Respect des bonnes pratiques\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Fonctionnalités implémentées\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Originalité et créativité\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  (click)=\"annuler()\"\r\n                  class=\"w-full sm:w-auto px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium\"\r\n                >\r\n                  <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                  Annuler\r\n                </button>\r\n\r\n                <button\r\n                  (click)=\"onSubmit()\"\r\n                  [disabled]=\"isSubmitting\"\r\n                  class=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none\"\r\n                >\r\n                  <span *ngIf=\"!isSubmitting\" class=\"flex items-center justify-center\">\r\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\r\n                    </svg>\r\n                    Lancer l'évaluation IA\r\n                  </span>\r\n                  <span *ngIf=\"isSubmitting\" class=\"flex items-center justify-center\">\r\n                    <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                      <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    Lancement en cours...\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"aiProcessing\" class=\"text-center py-12\">\r\n              <div class=\"relative\">\r\n                <div class=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-500 mx-auto mb-6\"></div>\r\n                <div class=\"absolute inset-0 flex items-center justify-center\">\r\n                  <svg class=\"w-6 h-6 text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <h3 class=\"text-lg font-semibold text-gray-800 mb-2\">L'IA analyse le projet...</h3>\r\n              <p class=\"text-gray-600 mb-4\">Notre système examine le code selon les critères d'évaluation</p>\r\n              <div class=\"bg-white rounded-lg p-4 max-w-md mx-auto\">\r\n                <div class=\"flex items-center justify-center space-x-2\">\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\"></div>\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\" style=\"animation-delay: 0.1s\"></div>\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\" style=\"animation-delay: 0.2s\"></div>\r\n                </div>\r\n                <p class=\"text-sm text-gray-500 mt-2\">Cela peut prendre quelques instants</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;;;ICG/DC,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,SAAA,aAA6F;IAC/FF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAAgG;IAC9FD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAwCQR,EAAA,CAAAC,cAAA,YAG0I;IACxID,EAAA,CAAAS,cAAA,EAAgF;IAAhFT,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,eAAiN;IACnNF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,eAAA,EAA+B;IAA/BV,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,GAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IANnEH,EAAA,CAAAW,UAAA,oCAAAC,UAAA,EAAAZ,EAAA,CAAAa,aAAA,CAA2C;IAMbb,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAc,iBAAA,CAAAF,UAAA,CAAAG,KAAA,MAAAC,GAAA,GAA8B;;;;;IAfnEhB,EAAA,CAAAC,cAAA,cAAmG;IAE/FD,EAAA,CAAAS,cAAA,EAA8F;IAA9FT,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,eAAA,EAAsC;IAAtCV,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAI,MAAA,GAA6C;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAE1FH,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAiB,UAAA,IAAAC,oDAAA,gBAQI;IACNlB,EAAA,CAAAG,YAAA,EAAM;;;;IAZkCH,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAM,kBAAA,sBAAAa,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA,MAA6C;IAG5DtB,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAW,UAAA,YAAAQ,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;IAuElCrB,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAiB,MAAA,CAAAC,aAAA,0BACF;;;;;IAqBAxB,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAmB,MAAA,CAAAD,aAAA,0BACF;;;;;IAqBAxB,EAAA,CAAAC,cAAA,cAAuF;IACrFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAoB,OAAA,CAAAF,aAAA,+BACF;;;;;IAqBAxB,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAqB,OAAA,CAAAH,aAAA,4BACF;;;;;IA+BJxB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsB,OAAA,CAAAJ,aAAA,sBACF;;;;;IAqBExB,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAS,cAAA,EAAgF;IAAhFT,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,eAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAS,cAAA,EAA2H;IAA3HT,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,iBAAkG;IAEpGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,+BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;;IArKbH,EAAA,CAAAU,eAAA,EAAiH;IAAjHV,EAAA,CAAAC,cAAA,eAAiH;IAA9ED,EAAA,CAAA6B,UAAA,sBAAAC,2EAAA;MAAA9B,EAAA,CAAA+B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAkC,aAAA;MAAA,OAAYlC,EAAA,CAAAmC,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxDpC,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAS,cAAA,EAAgG;IAAhGT,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,SAAA,eAAsR;IACxRF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,wCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAU,eAAA,EAA0E;IAA1EV,EAAA,CAAAC,cAAA,cAA0E;IAGpED,EAAA,CAAAI,MAAA,0BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAiB,UAAA,KAAAoB,wDAAA,kBAEM;IACRrC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,0BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAiB,UAAA,KAAAqB,wDAAA,kBAEM;IACRtC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,6BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAiB,UAAA,KAAAsB,wDAAA,kBAEM;IACRvC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,0BACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAI,MAAA,UAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGjDH,EAAA,CAAAiB,UAAA,KAAAuB,wDAAA,kBAEM;IACRxC,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAAqE;IAEfD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACrEH,EAAA,CAAAC,cAAA,eAA+B;IACoBD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAC,cAAA,gBAAyC;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAG5EH,EAAA,CAAAC,cAAA,eAAsD;IACpDD,EAAA,CAAAE,SAAA,eACyE;IAC3EF,EAAA,CAAAG,YAAA,EAAM;IAIVH,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAI,MAAA,0CACA;IAAAJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,SAAC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAE,SAAA,oBAKY;IACZF,EAAA,CAAAiB,UAAA,KAAAwB,wDAAA,kBAEM;IACNzC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAI,MAAA,iFAA+D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAG3GH,EAAA,CAAAC,cAAA,eAAwG;IAGpGD,EAAA,CAAA6B,UAAA,mBAAAa,2EAAA;MAAA1C,EAAA,CAAA+B,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAA3C,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAmC,WAAA,CAAAQ,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnB5C,EAAA,CAAAS,cAAA,EAAuF;IAAvFT,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAsG;IACxGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAU,eAAA,EAIC;IAJDV,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAAiB,UAAA,KAAA4B,yDAAA,mBAKO;IACP7C,EAAA,CAAAiB,UAAA,KAAA6B,yDAAA,mBAMO;IACT9C,EAAA,CAAAG,YAAA,EAAS;;;;IAtKPH,EAAA,CAAAW,UAAA,cAAAoC,MAAA,CAAAC,cAAA,CAA4B;IAqBtBhD,EAAA,CAAAK,SAAA,IAAuQ;IAAvQL,EAAA,CAAAiD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,gGAAuQ;IAOrQlD,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAW,UAAA,SAAAoC,MAAA,CAAAG,cAAA,qBAAwC;IAgB1ClD,EAAA,CAAAK,SAAA,GAAuQ;IAAvQL,EAAA,CAAAiD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,gGAAuQ;IAOrQlD,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAW,UAAA,SAAAoC,MAAA,CAAAG,cAAA,qBAAwC;IAgB1ClD,EAAA,CAAAK,SAAA,GAA4Q;IAA5QL,EAAA,CAAAiD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,qGAA4Q;IAO1QlD,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAW,UAAA,SAAAoC,MAAA,CAAAG,cAAA,0BAA6C;IAgB/ClD,EAAA,CAAAK,SAAA,GAAyQ;IAAzQL,EAAA,CAAAiD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,kGAAyQ;IAOvQlD,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAW,UAAA,SAAAoC,MAAA,CAAAG,cAAA,uBAA0C;IAWGlD,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAc,iBAAA,CAAAiC,MAAA,CAAAI,aAAA,GAAqB;IAC7BnD,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,kBAAA,MAAAyC,MAAA,CAAAK,eAAA,OAAwB;IAK9DpD,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAAqD,WAAA,UAAAN,MAAA,CAAAI,aAAA,KAAAJ,MAAA,CAAAK,eAAA,cAA6D;IAapEpD,EAAA,CAAAK,SAAA,GAAmQ;IAAnQL,EAAA,CAAAiD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,4FAAmQ;IAG/PlD,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAW,UAAA,SAAAoC,MAAA,CAAAG,cAAA,iBAAoC;IAoBxClD,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAW,UAAA,aAAAoC,MAAA,CAAAC,cAAA,CAAAM,OAAA,IAAAP,MAAA,CAAAQ,YAAA,CAAmD;IAG5CvD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,UAAAoC,MAAA,CAAAQ,YAAA,CAAmB;IAMnBvD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAW,UAAA,SAAAoC,MAAA,CAAAQ,YAAA,CAAkB;;;;;IA+EvBvD,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAS,cAAA,EAAgF;IAAhFT,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,eAA4G;IAC9GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,oCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAS,cAAA,EAA2H;IAA3HT,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,iBAAkG;IAEpGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,8BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IA/EbH,EAAA,CAAAC,cAAA,UAA2B;IAGrBD,EAAA,CAAAS,cAAA,EAAsF;IAAtFT,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAU,eAAA,EAAgD;IAAhDV,EAAA,CAAAC,cAAA,aAAgD;IAAAD,EAAA,CAAAI,MAAA,yCAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGpFH,EAAA,CAAAC,cAAA,cAAmE;IAG7DD,EAAA,CAAAS,cAAA,EAAkG;IAAlGT,EAAA,CAAAC,cAAA,eAAkG;IAChGD,EAAA,CAAAE,SAAA,gBAA2I;IAC7IF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAU,eAAA,EAAkB;IAAlBV,EAAA,CAAAC,cAAA,eAAkB;IACmCD,EAAA,CAAAI,MAAA,kCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAI,MAAA,wHAAsG;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChJH,EAAA,CAAAC,cAAA,cAA4C;IAExCD,EAAA,CAAAS,cAAA,EAAiF;IAAjFT,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,2CACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAU,eAAA,EAA8B;IAA9BV,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAS,cAAA,EAAiF;IAAjFT,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,sCACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAU,eAAA,EAA8B;IAA9BV,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAS,cAAA,EAAiF;IAAjFT,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,qDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAU,eAAA,EAA8B;IAA9BV,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAS,cAAA,EAAiF;IAAjFT,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAE,SAAA,gBAA4K;IAC9KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,kDACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAMbH,EAAA,CAAAU,eAAA,EAA0E;IAA1EV,EAAA,CAAAC,cAAA,eAA0E;IAGtED,EAAA,CAAA6B,UAAA,mBAAA2B,gFAAA;MAAAxD,EAAA,CAAA+B,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAmC,WAAA,CAAAuB,OAAA,CAAAd,OAAA,EAAS;IAAA,EAAC;IAGnB5C,EAAA,CAAAS,cAAA,EAAuF;IAAvFT,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAsG;IACxGF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAU,eAAA,EAIC;IAJDV,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAA6B,UAAA,mBAAA8B,gFAAA;MAAA3D,EAAA,CAAA+B,aAAA,CAAA0B,IAAA;MAAA,MAAAG,OAAA,GAAA5D,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAmC,WAAA,CAAAyB,OAAA,CAAAxB,QAAA,EAAU;IAAA,EAAC;IAIpBpC,EAAA,CAAAiB,UAAA,KAAA4C,8DAAA,mBAKO;IACP7D,EAAA,CAAAiB,UAAA,KAAA6C,8DAAA,mBAMO;IACT9D,EAAA,CAAAG,YAAA,EAAS;;;;IAhBPH,EAAA,CAAAK,SAAA,IAAyB;IAAzBL,EAAA,CAAAW,UAAA,aAAAoD,OAAA,CAAAR,YAAA,CAAyB;IAGlBvD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,UAAAoD,OAAA,CAAAR,YAAA,CAAmB;IAMnBvD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAW,UAAA,SAAAoD,OAAA,CAAAR,YAAA,CAAkB;;;;;IAW/BvD,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,SAAA,eAA4G;IAC5GF,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAS,cAAA,EAA2F;IAA3FT,EAAA,CAAAC,cAAA,eAA2F;IACzFD,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAU,eAAA,EAAqD;IAArDV,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAI,MAAA,mFAA6D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC/FH,EAAA,CAAAC,cAAA,gBAAsD;IAElDD,EAAA,CAAAE,SAAA,gBAAqE;IAGvEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAI,MAAA,2CAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAtGnFH,EAAA,CAAAU,eAAA,EAAmI;IAAnIV,EAAA,CAAAC,cAAA,cAAmI;IACjID,EAAA,CAAAiB,UAAA,IAAA+C,sDAAA,kBAkFM;IAENhE,EAAA,CAAAiB,UAAA,IAAAgD,sDAAA,mBAmBM;IACRjE,EAAA,CAAAG,YAAA,EAAM;;;;IAxGEH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,UAAA,UAAAuD,MAAA,CAAAC,YAAA,CAAmB;IAoFnBnE,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAW,UAAA,SAAAuD,MAAA,CAAAC,YAAA,CAAkB;;;;;;IAhVhCnE,EAAA,CAAAC,cAAA,UAAiC;IAIzBD,EAAA,CAAAS,cAAA,EAAsF;IAAtFT,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAU,eAAA,EAAiD;IAAjDV,EAAA,CAAAC,cAAA,aAAiD;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGjFH,EAAA,CAAAC,cAAA,cAAwD;IAEdD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAErEH,EAAA,CAAAC,cAAA,eAA+C;IACPD,EAAA,CAAAI,MAAA,qBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAClDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAI,MAAA,IAAoD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEjGH,EAAA,CAAAC,cAAA,eAA+C;IACPD,EAAA,CAAAI,MAAA,0BAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAI,MAAA,IAAoD;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEjGH,EAAA,CAAAC,cAAA,eAA+C;IACPD,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAI,MAAA,IAA+C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAI9FH,EAAA,CAAAiB,UAAA,KAAAmD,gDAAA,kBAkBM;IACRpE,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAKRD,EAAA,CAAAS,cAAA,EAAsF;IAAtFT,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,gBAAkS;IACpSF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAU,eAAA,EAAiD;IAAjDV,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAEzEH,EAAA,CAAAC,cAAA,eAA0D;IAEtDD,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,YAA0K;IACxKD,EAAA,CAAAI,MAAA,YACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAA6B,UAAA,mBAAAwC,mEAAA;MAAArE,EAAA,CAAA+B,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAmC,WAAA,CAAAoC,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCxE,EAAA,CAAAS,cAAA,EAAuF;IAAvFT,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAiB,UAAA,KAAAwD,iDAAA,qBAwKO;IAEPzE,EAAA,CAAAiB,UAAA,KAAAyD,gDAAA,kBAyGM;IACR1E,EAAA,CAAAG,YAAA,EAAM;;;;IAvVqCH,EAAA,CAAAK,SAAA,IAAwB;IAAxBL,EAAA,CAAAc,iBAAA,CAAA6D,MAAA,CAAAvD,KAAA,CAAAwD,MAAA,CAAAC,KAAA,CAAwB;IAIxB7E,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAA8E,kBAAA,KAAAH,MAAA,CAAAvD,KAAA,CAAA2D,QAAA,CAAAC,GAAA,OAAAL,MAAA,CAAAvD,KAAA,CAAA2D,QAAA,CAAAE,MAAA,KAAoD;IAIpDjF,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAkF,WAAA,SAAAP,MAAA,CAAAvD,KAAA,CAAA+D,cAAA,sBAAoD;IAIpDnF,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAc,iBAAA,CAAA6D,MAAA,CAAAvD,KAAA,CAAAgE,WAAA,yBAA+C;IAIpFpF,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAW,UAAA,SAAAgE,MAAA,CAAAvD,KAAA,CAAAC,QAAA,IAAAsD,MAAA,CAAAvD,KAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;IAiC3CtB,EAAA,CAAAK,SAAA,IAAuK;IAAvKL,EAAA,CAAAiD,sBAAA,mCAAA0B,MAAA,CAAAU,cAAA,kHAAuK;IAGvKrF,EAAA,CAAAK,SAAA,GAAmK;IAAnKL,EAAA,CAAAiD,sBAAA,mCAAA0B,MAAA,CAAAU,cAAA,8GAAmK;IAejHrF,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAW,UAAA,SAAAgE,MAAA,CAAAU,cAAA,cAAiC;IA0KvFrF,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAW,UAAA,SAAAgE,MAAA,CAAAU,cAAA,UAA6B;;;AD7P7C,OAAM,MAAOC,0BAA0B;EAWrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAdvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAxE,KAAK,GAAQ,IAAI;IAEjB,KAAAyE,SAAS,GAAY,IAAI;IACzB,KAAAtC,YAAY,GAAY,KAAK;IAC7B,KAAA/C,KAAK,GAAW,EAAE;IAClB,KAAAsF,cAAc,GAAW,EAAE;IAC3B,KAAAT,cAAc,GAAoB,QAAQ;IAC1C,KAAAlB,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAACnB,cAAc,GAAG,IAAI,CAACwC,EAAE,CAACO,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACR,EAAE,CAACO,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAClG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACtG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACvG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACxG,UAAU,CAACmG,QAAQ,EAAEnG,UAAU,CAACoG,GAAG,CAAC,CAAC,CAAC,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAEzG,UAAU,CAACmG,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACd,OAAO,GAAG,IAAI,CAACH,KAAK,CAACkB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACrB,KAAK,CAACkB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACzB,cAAc,GAAGyB,IAAI;MAC1B,IAAI,CAAC9D,cAAc,CAACgE,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC9B,cAAc,GAAG8B,UAAU;QAChC,IAAI,CAACnE,cAAc,CAACgE,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACvB,OAAO,EAAE;MAChB,IAAI,CAACyB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAC7G,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACqF,SAAS,GAAG,KAAK;;EAE1B;EAEAwB,SAASA,CAAA;IACP,IAAI,CAACxB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAAC2B,YAAY,CAAC,IAAI,CAAC1B,OAAO,CAAC,CAAC2B,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACrG,KAAK,GAAGqG,IAAI;QACjB,IAAI,CAAC5B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDrF,KAAK,EAAGkH,GAAQ,IAAI;QAClB,IAAI,CAAClH,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACqF,SAAS,GAAG,KAAK;QACtB8B,OAAO,CAACnH,KAAK,CAACkH,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAlD,oBAAoBA,CAAA;IAClB,IAAI,CAACa,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAACrC,cAAc,CAACgE,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACpB,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E4B,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC7B,cAAc,CAAC;EAC7D;EAEAjD,QAAQA,CAAA;IACNuF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC5E,cAAc,CAAC6E,KAAK,CAAC;IACrEF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC5E,cAAc,CAAC8E,KAAK,CAAC;IAEtD,IAAI,IAAI,CAACzC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACrC,cAAc,CAACM,OAAO,EAAE;MACnEqE,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACG,oBAAoB,CAAC,IAAI,CAAC/E,cAAc,CAAC;MAC9C,IAAI,CAACxC,KAAK,GAAG,gDAAgD;MAC7D;;IAGF,IAAI,CAAC+C,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC/C,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAAC6E,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAACrC,cAAc,CAACgE,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACtC,YAAY,GAAG,IAAI;;IAG1B,MAAM6D,cAAc,GAAG,IAAI,CAAChF,cAAc,CAAC8E,KAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,cAAc,CAAC;IAEvD,IAAI,CAACrC,aAAa,CAACsC,aAAa,CAAC,IAAI,CAACrC,OAAO,EAAEoC,cAAc,CAAC,CAACT,SAAS,CAAC;MACvEC,IAAI,EAAGU,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC7C,cAAc,KAAK,IAAI,IAAI6C,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACnC,MAAM;UAC3C,MAAMqC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAAC3B,YAAY;UAEvD,IAAI,CAACxD,cAAc,CAACgE,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEmC,QAAQ,CAACnC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAE+B,QAAQ,CAAC/B,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE8B,QAAQ,CAAC9B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE6B,QAAQ,CAAC7B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE6B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAAClE,YAAY,GAAG,KAAK;UACzB,IAAI,CAACZ,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAAC/C,KAAK,GAAG,EAAE;UACf8H,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAAC/E,YAAY,GAAG,KAAK;UACzB+E,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAAC5C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;;MAEzD,CAAC;MACD/H,KAAK,EAAGkH,GAAQ,IAAI;QAClB,IAAI,CAAClH,KAAK,GAAG,yCAAyC,IAAIkH,GAAG,CAAClH,KAAK,EAAEgI,OAAO,IAAId,GAAG,CAACc,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAACjF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACY,YAAY,GAAG,KAAK;QACzBwD,OAAO,CAACnH,KAAK,CAACkH,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAvE,aAAaA,CAAA;IACX,MAAM6C,MAAM,GAAG,IAAI,CAAChD,cAAc,CAAC6D,GAAG,CAAC,QAAQ,CAAC,EAAEiB,KAAK;IACvD,IAAI,CAAC9B,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAnD,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAR,OAAOA,CAAA;IACL,IAAI,CAAC8C,MAAM,CAAC6C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EACvD;EAEAR,oBAAoBA,CAACU,SAAoB;IACvCC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAC5B,GAAG,CAACiC,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAYjJ,SAAS,EAAE;QAChC,IAAI,CAACiI,oBAAoB,CAACgB,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA7F,cAAcA,CAAC+F,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAAClG,cAAc,CAAC6D,GAAG,CAACoC,SAAS,CAAC;IAChD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAAC5F,OAAO,KAAK4F,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACrE;EAEA5H,aAAaA,CAACyH,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAClG,cAAc,CAAC6D,GAAG,CAACoC,SAAS,CAAC;IAChD,IAAIC,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAKH,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,EAAE;MAC3D,IAAIF,KAAK,CAACG,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,0BAA0B;;MAEnC,IAAIH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,CAAClD,GAAG,EAAE;;MAE3D,IAAI+C,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,CAACjD,GAAG,EAAE;;;IAG7D,OAAO,EAAE;EACX;;;uBAxLWd,0BAA0B,EAAAtF,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA3J,EAAA,CAAAsJ,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1BvE,0BAA0B;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvCpK,EAAA,CAAAC,cAAA,aAAyC;UAEaD,EAAA,CAAAI,MAAA,gCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAE3EH,EAAA,CAAAiB,UAAA,IAAAqJ,yCAAA,iBAEM;UAENtK,EAAA,CAAAiB,UAAA,IAAAsJ,yCAAA,iBAEM;UAENvK,EAAA,CAAAiB,UAAA,IAAAuJ,yCAAA,mBAuWM;UACRxK,EAAA,CAAAG,YAAA,EAAM;;;UAhXEH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAW,UAAA,SAAA0J,GAAA,CAAAxE,SAAA,CAAe;UAIf7F,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAW,UAAA,SAAA0J,GAAA,CAAA7J,KAAA,CAAW;UAIXR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAW,UAAA,SAAA0J,GAAA,CAAAjJ,KAAA,KAAAiJ,GAAA,CAAAxE,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}