{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let ProjectEvaluationComponent = class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        // Filter out null/undefined files\n        if (this.rendu.fichiers) {\n          this.rendu.fichiers = this.rendu.fichiers.filter(fichier => fichier != null && fichier !== '');\n        }\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n    console.log('Form values:', this.evaluationForm.value);\n    this.isSubmitting = true;\n    this.error = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    console.log('Sending evaluation data:', evaluationData);\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/list-rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    // Confirmer avant d'annuler si des données ont été saisies\n    const formData = this.evaluationForm.value;\n    const hasData = formData.scores?.structure || formData.scores?.pratiques || formData.scores?.fonctionnalite || formData.scores?.originalite || formData.commentaires;\n    if (hasData) {\n      const confirmation = confirm('Êtes-vous sûr de vouloir annuler ? Toutes les données saisies seront perdues.');\n      if (!confirmation) {\n        return;\n      }\n    }\n    console.log('Navigation vers la liste des rendus...');\n    this.router.navigate(['/admin/projects/list-rendus']);\n  }\n};\nProjectEvaluationComponent = __decorate([Component({\n  selector: 'app-project-evaluation',\n  templateUrl: './project-evaluation.component.html',\n  styleUrls: ['./project-evaluation.component.css']\n})], ProjectEvaluationComponent);", "map": {"version": 3, "names": ["Component", "Validators", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "rendu", "isLoading", "isSubmitting", "error", "evaluationMode", "aiProcessing", "evaluationForm", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "fichiers", "filter", "<PERSON><PERSON><PERSON>", "err", "console", "toggleEvaluationMode", "onSubmit", "log", "valid", "value", "evaluationData", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "getScoreTotal", "getScoreMaximum", "annuler", "formData", "hasData", "confirmation", "confirm", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        // Filter out null/undefined files\r\n        if (this.rendu.fichiers) {\r\n          this.rendu.fichiers = this.rendu.fichiers.filter((fichier: any) => fichier != null && fichier !== '');\r\n        }\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\r\n    console.log('Form values:', this.evaluationForm.value);\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n    console.log('Sending evaluation data:', evaluationData);\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/list-rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    // Confirmer avant d'annuler si des données ont été saisies\r\n    const formData = this.evaluationForm.value;\r\n    const hasData = formData.scores?.structure || formData.scores?.pratiques ||\r\n                   formData.scores?.fonctionnalite || formData.scores?.originalite ||\r\n                   formData.commentaires;\r\n\r\n    if (hasData) {\r\n      const confirmation = confirm('Êtes-vous sûr de vouloir annuler ? Toutes les données saisies seront perdues.');\r\n      if (!confirmation) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    console.log('Navigation vers la liste des rendus...');\r\n    this.router.navigate(['/admin/projects/list-rendus']);\r\n  }\r\n\r\n\r\n}\r\n\r\n\r\n\r\n\r\n\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAAiCC,UAAU,QAAQ,gBAAgB;AAS5D,WAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAUrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAbvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,KAAK,GAAQ,IAAI;IAEjB,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,cAAc,GAAoB,QAAQ;IAC1C,KAAAC,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACX,EAAE,CAACY,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACb,EAAE,CAACY,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAACjB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACmB,GAAG,CAAC,CAAC,CAAC,EAAEnB,UAAU,CAACoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACrB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACmB,GAAG,CAAC,CAAC,CAAC,EAAEnB,UAAU,CAACoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACtB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACmB,GAAG,CAAC,CAAC,CAAC,EAAEnB,UAAU,CAACoB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACvB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACmB,GAAG,CAAC,CAAC,CAAC,EAAEnB,UAAU,CAACoB,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAExB,UAAU,CAACkB,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACnB,OAAO,GAAG,IAAI,CAACH,KAAK,CAACuB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAAC1B,KAAK,CAACuB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAAClB,cAAc,GAAGkB,IAAI;MAC1B,IAAI,CAAChB,cAAc,CAACkB,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAACvB,cAAc,GAAGuB,UAAU;QAChC,IAAI,CAACrB,cAAc,CAACkB,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAAC5B,OAAO,EAAE;MAChB,IAAI,CAAC8B,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAC1B,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACF,SAAS,GAAG,KAAK;;EAE1B;EAEA4B,SAASA,CAAA;IACP,IAAI,CAAC5B,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAACgC,YAAY,CAAC,IAAI,CAAC/B,OAAO,CAAC,CAACgC,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACjC,KAAK,GAAGiC,IAAI;QACjB;QACA,IAAI,IAAI,CAACjC,KAAK,CAACkC,QAAQ,EAAE;UACvB,IAAI,CAAClC,KAAK,CAACkC,QAAQ,GAAG,IAAI,CAAClC,KAAK,CAACkC,QAAQ,CAACC,MAAM,CAAEC,OAAY,IAAKA,OAAO,IAAI,IAAI,IAAIA,OAAO,KAAK,EAAE,CAAC;;QAEvG,IAAI,CAACnC,SAAS,GAAG,KAAK;MACxB,CAAC;MACDE,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAClC,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACF,SAAS,GAAG,KAAK;QACtBqC,OAAO,CAACnC,KAAK,CAACkC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAE,oBAAoBA,CAAA;IAClB,IAAI,CAACnC,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAACE,cAAc,CAACkB,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACb,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5EqB,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACtB,cAAc,CAAC;EAC7D;EAEAoC,QAAQA,CAAA;IACNF,OAAO,CAACG,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACnC,cAAc,CAACoC,KAAK,CAAC;IACrEJ,OAAO,CAACG,GAAG,CAAC,cAAc,EAAE,IAAI,CAACnC,cAAc,CAACqC,KAAK,CAAC;IAEtD,IAAI,CAACzC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAACC,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAACE,cAAc,CAACkB,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACZ,YAAY,GAAG,IAAI;;IAG1B,MAAMuC,cAAc,GAAG,IAAI,CAACtC,cAAc,CAACqC,KAAK;IAChDL,OAAO,CAACG,GAAG,CAAC,0BAA0B,EAAEG,cAAc,CAAC;IAEvD,IAAI,CAAC9C,aAAa,CAAC+C,aAAa,CAAC,IAAI,CAAC9C,OAAO,EAAE6C,cAAc,CAAC,CAACb,SAAS,CAAC;MACvEC,IAAI,EAAGc,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC1C,cAAc,KAAK,IAAI,IAAI0C,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACvC,MAAM;UAC3C,MAAMyC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAAC/B,YAAY;UAEvD,IAAI,CAACV,cAAc,CAACkB,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEuC,QAAQ,CAACvC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAEmC,QAAQ,CAACnC,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAEkC,QAAQ,CAAClC,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAEiC,QAAQ,CAACjC,WAAW,IAAI;aACtC;YACDC,YAAY,EAAEiC,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAAC5C,YAAY,GAAG,KAAK;UACzB,IAAI,CAACH,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAACC,KAAK,GAAG,EAAE;UACf+C,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAAChD,YAAY,GAAG,KAAK;UACzBgD,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;;MAEzD,CAAC;MACDhD,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAAClC,KAAK,GAAG,yCAAyC,IAAIkC,GAAG,CAAClC,KAAK,EAAEiD,OAAO,IAAIf,GAAG,CAACe,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAAClD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACG,YAAY,GAAG,KAAK;QACzBiC,OAAO,CAACnC,KAAK,CAACkC,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAgB,aAAaA,CAAA;IACX,MAAM7C,MAAM,GAAG,IAAI,CAACF,cAAc,CAACe,GAAG,CAAC,QAAQ,CAAC,EAAEsB,KAAK;IACvD,IAAI,CAACnC,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAuC,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAC,OAAOA,CAAA;IACL;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAClD,cAAc,CAACqC,KAAK;IAC1C,MAAMc,OAAO,GAAGD,QAAQ,CAAChD,MAAM,EAAEC,SAAS,IAAI+C,QAAQ,CAAChD,MAAM,EAAEK,SAAS,IACzD2C,QAAQ,CAAChD,MAAM,EAAEM,cAAc,IAAI0C,QAAQ,CAAChD,MAAM,EAAEO,WAAW,IAC/DyC,QAAQ,CAACxC,YAAY;IAEpC,IAAIyC,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGC,OAAO,CAAC,+EAA+E,CAAC;MAC7G,IAAI,CAACD,YAAY,EAAE;QACjB;;;IAIJpB,OAAO,CAACG,GAAG,CAAC,wCAAwC,CAAC;IACrD,IAAI,CAAC5C,MAAM,CAACsD,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EACvD;CAGD;AArKY1D,0BAA0B,GAAAmE,UAAA,EALtCrE,SAAS,CAAC;EACTsE,QAAQ,EAAE,wBAAwB;EAClCC,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,CAAC,oCAAoC;CACjD,CAAC,C,EACWtE,0BAA0B,CAqKtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}