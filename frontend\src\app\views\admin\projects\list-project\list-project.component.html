<!-- Begin Page Content -->
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary">
  <div class="container mx-auto px-4 py-8">

    <!-- Header moderne avec gradient -->
    <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="mb-6 lg:mb-0">
          <div class="flex items-center space-x-4 mb-4">
            <div class="h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold text-text-dark dark:text-dark-text-primary">
                Gestion des Projets
              </h1>
              <p class="text-text dark:text-dark-text-secondary">
                Créez, gérez et suivez vos projets académiques
              </p>
            </div>
          </div>

          <!-- Statistiques avancées -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <!-- Total projets -->
            <div class="bg-gradient-to-br from-primary/10 to-primary/5 dark:from-dark-accent-primary/20 dark:to-dark-accent-primary/10 rounded-xl p-4 border border-primary/20 dark:border-dark-accent-primary/30">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs font-medium text-primary dark:text-dark-accent-primary uppercase tracking-wider">Total</p>
                  <p class="text-2xl font-bold text-primary dark:text-dark-accent-primary">{{ projets.length || 0 }}</p>
                  <p class="text-xs text-text dark:text-dark-text-secondary mt-1">Projets créés</p>
                </div>
                <div class="bg-primary/20 dark:bg-dark-accent-primary/30 p-3 rounded-xl">
                  <svg class="w-6 h-6 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Projets actifs -->
            <div class="bg-gradient-to-br from-success/10 to-success/5 dark:from-dark-accent-secondary/20 dark:to-dark-accent-secondary/10 rounded-xl p-4 border border-success/20 dark:border-dark-accent-secondary/30">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs font-medium text-success dark:text-dark-accent-secondary uppercase tracking-wider">Actifs</p>
                  <p class="text-2xl font-bold text-success dark:text-dark-accent-secondary">{{ getActiveProjectsCount() }}</p>
                  <p class="text-xs text-text dark:text-dark-text-secondary mt-1">En cours</p>
                </div>
                <div class="bg-success/20 dark:bg-dark-accent-secondary/30 p-3 rounded-xl">
                  <svg class="w-6 h-6 text-success dark:text-dark-accent-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Projets expirés -->
            <div class="bg-gradient-to-br from-warning/10 to-warning/5 dark:from-warning/20 dark:to-warning/10 rounded-xl p-4 border border-warning/20 dark:border-warning/30">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs font-medium text-warning dark:text-warning uppercase tracking-wider">Expirés</p>
                  <p class="text-2xl font-bold text-warning dark:text-warning">{{ getExpiredProjectsCount() }}</p>
                  <p class="text-xs text-text dark:text-dark-text-secondary mt-1">Date dépassée</p>
                </div>
                <div class="bg-warning/20 dark:bg-warning/30 p-3 rounded-xl">
                  <svg class="w-6 h-6 text-warning dark:text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Groupes uniques -->
            <div class="bg-gradient-to-br from-info/10 to-info/5 dark:from-dark-accent-primary/20 dark:to-dark-accent-primary/10 rounded-xl p-4 border border-info/20 dark:border-dark-accent-primary/30">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-xs font-medium text-info dark:text-dark-accent-primary uppercase tracking-wider">Groupes</p>
                  <p class="text-2xl font-bold text-info dark:text-dark-accent-primary">{{ getUniqueGroupsCount() }}</p>
                  <p class="text-xs text-text dark:text-dark-text-secondary mt-1">Différents</p>
                </div>
                <div class="bg-info/20 dark:bg-dark-accent-primary/30 p-3 rounded-xl">
                  <svg class="w-6 h-6 text-info dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Barre de progression globale -->
          <div class="mt-6 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4">
            <div class="flex items-center justify-between mb-2">
              <h4 class="text-sm font-semibold text-text-dark dark:text-dark-text-primary">Progression des projets</h4>
              <span class="text-xs text-text dark:text-dark-text-secondary">{{ getCompletionPercentage() }}% complétés</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-dark-bg-tertiary rounded-full h-2">
              <div class="bg-gradient-to-r from-success to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary h-2 rounded-full transition-all duration-500"
                   [style.width.%]="getCompletionPercentage()"></div>
            </div>
            <div class="flex justify-between text-xs text-text dark:text-dark-text-secondary mt-2">
              <span>{{ getActiveProjectsCount() }} actifs</span>
              <span>{{ getExpiredProjectsCount() }} expirés</span>
            </div>
          </div>
        </div>

        <!-- Bouton d'ajout moderne -->
        <div class="flex flex-col sm:flex-row gap-3">
          <a routerLink="/admin/projects/new"
             class="group px-6 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
            <div class="flex items-center justify-center space-x-2">
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              <span>Nouveau projet</span>
            </div>
          </a>
        </div>
      </div>
    </div>

    <!-- Liste des projets en cartes modernes -->
    <div *ngIf="!isLoading && projets && projets.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <!-- Carte de projet moderne -->
      <div *ngFor="let projet of projets" class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group overflow-hidden">

        <!-- Header du projet avec gradient -->
        <div class="relative p-6 bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10">
          <div class="flex items-start justify-between">
            <div class="flex-1 pr-4">
              <h3 class="text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2 line-clamp-2">
                {{ projet.titre }}
              </h3>
              <div class="flex items-center space-x-4">
                <!-- Badge groupe -->
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <span class="text-sm font-medium text-primary dark:text-dark-accent-primary">
                    {{ projet.groupe || "Tous" }}
                  </span>
                </div>
                <!-- Date limite -->
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-warning dark:text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-sm text-text dark:text-dark-text-secondary">
                    {{ projet.dateLimite | date : "dd/MM/yyyy" }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Actions flottantes -->
            <div class="flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-200">
              <a [routerLink]="['/admin/projects/editProjet', projet._id]"
                 class="p-2 bg-white/80 dark:bg-dark-bg-tertiary/80 backdrop-blur-sm rounded-lg text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary transition-all duration-200 shadow-lg">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
              </a>
              <button (click)="projet._id && openDeleteDialog(projet._id)"
                      class="p-2 bg-white/80 dark:bg-dark-bg-tertiary/80 backdrop-blur-sm rounded-lg text-danger dark:text-danger-dark hover:bg-danger hover:text-white dark:hover:bg-danger-dark transition-all duration-200 shadow-lg">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Contenu du projet -->
        <div class="p-6">
          <!-- Description -->
          <div class="mb-6">
            <div class="flex items-center space-x-2 mb-3">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h4 class="text-sm font-semibold text-text-dark dark:text-dark-text-primary uppercase tracking-wider">
                Description
              </h4>
            </div>
            <p class="text-text dark:text-dark-text-secondary text-sm line-clamp-3 leading-relaxed">
              {{ projet.description || "Aucune description fournie" }}
            </p>
          </div>

          <!-- Section fichiers -->
          <div *ngIf="projet.fichiers && projet.fichiers.length > 0" class="mb-6">
            <div class="flex items-center space-x-2 mb-3">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <h4 class="text-sm font-semibold text-text-dark dark:text-dark-text-primary uppercase tracking-wider">
                Fichiers ({{ projet.fichiers.length }})
              </h4>
            </div>
            <div class="space-y-2 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4">
              <div *ngFor="let file of projet.fichiers" class="flex items-center justify-between p-2 bg-white dark:bg-dark-bg-secondary rounded-lg shadow-sm">
                <div class="flex items-center space-x-2 flex-1 min-w-0">
                  <div class="bg-primary/10 dark:bg-dark-accent-primary/20 p-1.5 rounded-lg">
                    <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-text-dark dark:text-dark-text-primary truncate">
                    {{ getFileName(file) }}
                  </span>
                </div>
                <a [href]="getFileUrl(file)" [download]="getFileName(file)"
                   class="ml-2 px-3 py-1.5 bg-primary/10 dark:bg-dark-accent-primary/20 text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary rounded-lg transition-all duration-200 text-xs font-medium">
                  <div class="flex items-center space-x-1">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    <span>Télécharger</span>
                  </div>
                </a>
              </div>
            </div>
          </div>

          <!-- Message aucun fichier -->
          <div *ngIf="!projet.fichiers || projet.fichiers.length === 0" class="mb-6">
            <div class="flex items-center space-x-2 mb-3">
              <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <h4 class="text-sm font-semibold text-text-dark dark:text-dark-text-primary uppercase tracking-wider">
                Fichiers
              </h4>
            </div>
            <div class="bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl p-4 text-center">
              <div class="bg-gray-200 dark:bg-dark-bg-tertiary p-3 rounded-lg inline-flex items-center justify-center mb-2">
                <svg class="w-5 h-5 text-gray-400 dark:text-dark-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <p class="text-sm text-text dark:text-dark-text-secondary">Aucun fichier joint</p>
            </div>
          </div>

          <!-- Actions principales -->
          <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-100 dark:border-dark-bg-tertiary/50">
            <a [routerLink]="['/admin/projects/details', projet._id]"
               class="flex-1 group px-4 py-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 text-text-dark dark:text-dark-text-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary rounded-xl transition-all duration-200 font-medium">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Voir détails</span>
              </div>
            </a>
            <a [routerLink]="['/admin/projects/rendus']" [queryParams]="{ projetId: projet._id }"
               class="flex-1 group px-4 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <span>Voir rendus</span>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- État de chargement moderne -->
    <div *ngIf="isLoading" class="text-center py-16">
      <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto">
        <div class="relative">
          <div class="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary dark:border-dark-accent-primary/20 dark:border-t-dark-accent-primary mx-auto"></div>
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full animate-pulse"></div>
          </div>
        </div>
        <p class="mt-6 text-text-dark dark:text-dark-text-primary font-medium">Chargement des projets...</p>
        <p class="mt-2 text-sm text-text dark:text-dark-text-secondary">Veuillez patienter</p>
      </div>
    </div>

    <!-- État vide moderne -->
    <div *ngIf="!isLoading && (!projets || projets.length === 0)" class="text-center py-16">
      <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-12 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 max-w-md mx-auto">
        <div class="bg-gradient-to-br from-primary/10 to-secondary/10 dark:from-dark-accent-primary/20 dark:to-dark-accent-secondary/20 rounded-2xl p-6 mb-6">
          <svg class="h-16 w-16 mx-auto text-primary dark:text-dark-accent-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2">Aucun projet disponible</h3>
        <p class="text-text dark:text-dark-text-secondary mb-6">Commencez par créer votre premier projet pour organiser vos cours</p>
        <a routerLink="/admin/projects/new"
           class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Créer un projet
        </a>
      </div>
    </div>

    <!-- Boîte de dialogue de confirmation moderne -->
    <div *ngIf="showDeleteDialog" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div class="bg-white/95 dark:bg-dark-bg-secondary/95 backdrop-blur-sm rounded-2xl shadow-2xl w-full max-w-md p-8 border border-gray-200/50 dark:border-dark-bg-tertiary/50 mx-4">
        <div class="text-center mb-6">
          <div class="bg-danger/10 dark:bg-danger-dark/20 rounded-full p-4 w-16 h-16 mx-auto mb-4">
            <svg class="w-8 h-8 text-danger dark:text-danger-dark mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h2 class="text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2">
            {{ dialogData.title }}
          </h2>
          <p class="text-text dark:text-dark-text-secondary">{{ dialogData.message }}</p>
        </div>

        <div class="flex flex-col sm:flex-row gap-3">
          <button (click)="onDeleteCancel()"
                  class="flex-1 px-6 py-3 bg-gray-100 dark:bg-dark-bg-tertiary text-text-dark dark:text-dark-text-primary hover:bg-gray-200 dark:hover:bg-dark-bg-tertiary/80 rounded-xl transition-all duration-200 font-medium">
            Annuler
          </button>
          <button (click)="onDeleteConfirm()"
                  class="flex-1 px-6 py-3 bg-gradient-to-r from-danger to-danger-dark dark:from-danger-dark dark:to-danger text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium">
            Supprimer
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
