{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"div\", 13);\n    i0.ɵɵelementStart(3, \"p\", 14);\n    i0.ɵɵtext(4, \"Chargement en cours...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectEvaluationComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 18);\n    i0.ɵɵelement(4, \"path\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"p\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProjectEvaluationComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 23);\n    i0.ɵɵelement(4, \"path\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 20)(6, \"p\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.successMessage);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_30_a_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 56);\n    i0.ɵɵelement(2, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 58);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r8, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(fichier_r8.split(\"/\").pop());\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 50);\n    i0.ɵɵelement(3, \"path\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 52);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 53);\n    i0.ɵɵtemplate(7, ProjectEvaluationComponent_div_11_div_30_a_7_Template, 5, 2, \"a\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Fichiers joints (\", ctx_r4.rendu.fichiers.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getFieldError(\"scores.structure\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getFieldError(\"scores.pratiques\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getFieldError(\"scores.fonctionnalite\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"scores.originalite\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getFieldError(\"commentaires\"), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 93);\n    i0.ɵɵelement(2, \"path\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Soumettre l'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 94);\n    i0.ɵɵelement(2, \"circle\", 95)(3, \"path\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Soumission en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_11_form_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"form\", 59);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_11_form_49_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 60)(2, \"h3\", 61);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 62);\n    i0.ɵɵelement(4, \"path\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Crit\\u00E8res d'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\", 64)(7, \"div\", 65)(8, \"label\", 66);\n    i0.ɵɵtext(9, \" Structure du code \");\n    i0.ɵɵelementStart(10, \"span\", 67);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 68);\n    i0.ɵɵelement(13, \"input\", 69);\n    i0.ɵɵelementStart(14, \"div\", 70)(15, \"span\", 71);\n    i0.ɵɵtext(16, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, ProjectEvaluationComponent_div_11_form_49_div_17_Template, 2, 1, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"label\", 66);\n    i0.ɵɵtext(20, \" Bonnes pratiques \");\n    i0.ɵɵelementStart(21, \"span\", 67);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 68);\n    i0.ɵɵelement(24, \"input\", 73);\n    i0.ɵɵelementStart(25, \"div\", 70)(26, \"span\", 71);\n    i0.ɵɵtext(27, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, ProjectEvaluationComponent_div_11_form_49_div_28_Template, 2, 1, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 65)(30, \"label\", 66);\n    i0.ɵɵtext(31, \" Fonctionnalit\\u00E9 \");\n    i0.ɵɵelementStart(32, \"span\", 67);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 68);\n    i0.ɵɵelement(35, \"input\", 74);\n    i0.ɵɵelementStart(36, \"div\", 70)(37, \"span\", 71);\n    i0.ɵɵtext(38, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(39, ProjectEvaluationComponent_div_11_form_49_div_39_Template, 2, 1, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 65)(41, \"label\", 66);\n    i0.ɵɵtext(42, \" Originalit\\u00E9 \");\n    i0.ɵɵelementStart(43, \"span\", 67);\n    i0.ɵɵtext(44, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 68);\n    i0.ɵɵelement(46, \"input\", 75);\n    i0.ɵɵelementStart(47, \"div\", 70)(48, \"span\", 71);\n    i0.ɵɵtext(49, \"/5\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(50, ProjectEvaluationComponent_div_11_form_49_div_50_Template, 2, 1, \"div\", 72);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 76)(52, \"div\", 77)(53, \"span\", 78);\n    i0.ɵɵtext(54, \"Score total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 40)(56, \"span\", 79);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"span\", 80);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 81);\n    i0.ɵɵelement(61, \"div\", 82);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"div\", 65)(63, \"label\", 66);\n    i0.ɵɵtext(64, \" Commentaires d\\u00E9taill\\u00E9s \");\n    i0.ɵɵelementStart(65, \"span\", 67);\n    i0.ɵɵtext(66, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(67, \"textarea\", 83);\n    i0.ɵɵtemplate(68, ProjectEvaluationComponent_div_11_form_49_div_68_Template, 2, 1, \"div\", 72);\n    i0.ɵɵelementStart(69, \"p\", 84);\n    i0.ɵɵtext(70, \"D\\u00E9crivez les points forts et les axes d'am\\u00E9lioration du projet.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 85)(72, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_11_form_49_Template_button_click_72_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.annuler());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(73, \"svg\", 87);\n    i0.ɵɵelement(74, \"path\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(75, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(76, \"button\", 89);\n    i0.ɵɵtemplate(77, ProjectEvaluationComponent_div_11_form_49_span_77_Template, 4, 0, \"span\", 90);\n    i0.ɵɵtemplate(78, ProjectEvaluationComponent_div_11_form_49_span_78_Template, 5, 0, \"span\", 90);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.evaluationForm);\n    i0.ɵɵadvance(13);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r5.isFieldInvalid(\"scores.structure\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isFieldInvalid(\"scores.structure\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r5.isFieldInvalid(\"scores.pratiques\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isFieldInvalid(\"scores.pratiques\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r5.isFieldInvalid(\"scores.fonctionnalite\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isFieldInvalid(\"scores.fonctionnalite\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r5.isFieldInvalid(\"scores.originalite\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isFieldInvalid(\"scores.originalite\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.getScoreTotal());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"/\", ctx_r5.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r5.getScoreTotal() / ctx_r5.getScoreMaximum() * 100, \"%\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassMapInterpolate1(\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 \", ctx_r5.isFieldInvalid(\"commentaires\") ? \"border-red-300 bg-red-50\" : \"border-gray-300 hover:border-gray-400\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isFieldInvalid(\"commentaires\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.evaluationForm.invalid || ctx_r5.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_50_div_1_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 93);\n    i0.ɵɵelement(2, \"path\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Lancer l'\\u00E9valuation IA \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_50_div_1_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 94);\n    i0.ɵɵelement(2, \"circle\", 95)(3, \"path\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Lancement en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_50_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 27)(2, \"div\", 99);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 4);\n    i0.ɵɵelement(4, \"path\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 100);\n    i0.ɵɵtext(6, \"\\u00C9valuation automatique par IA\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 101)(8, \"div\", 102)(9, \"div\", 17);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 103);\n    i0.ɵɵelement(11, \"path\", 104);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"h4\", 105);\n    i0.ɵɵtext(14, \"Comment \\u00E7a fonctionne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 106);\n    i0.ɵɵtext(16, \"Notre syst\\u00E8me d'IA (Mistral 7B) analysera automatiquement le code soumis selon les crit\\u00E8res suivants :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ul\", 107)(18, \"li\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 108);\n    i0.ɵɵelement(20, \"path\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Structure et organisation du code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"li\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 108);\n    i0.ɵɵelement(24, \"path\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Respect des bonnes pratiques \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"li\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(27, \"svg\", 108);\n    i0.ɵɵelement(28, \"path\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29, \" Fonctionnalit\\u00E9s impl\\u00E9ment\\u00E9es \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"li\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(31, \"svg\", 108);\n    i0.ɵɵelement(32, \"path\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Originalit\\u00E9 et cr\\u00E9ativit\\u00E9 \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(34, \"div\", 110)(35, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_11_div_50_div_1_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.annuler());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 87);\n    i0.ɵɵelement(37, \"path\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_11_div_50_div_1_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.onSubmit());\n    });\n    i0.ɵɵtemplate(40, ProjectEvaluationComponent_div_11_div_50_div_1_span_40_Template, 4, 0, \"span\", 90);\n    i0.ɵɵtemplate(41, ProjectEvaluationComponent_div_11_div_50_div_1_span_41_Template, 5, 0, \"span\", 90);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(39);\n    i0.ɵɵproperty(\"disabled\", ctx_r19.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r19.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_50_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 68);\n    i0.ɵɵelement(2, \"div\", 114);\n    i0.ɵɵelementStart(3, \"div\", 115);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 116);\n    i0.ɵɵelement(5, \"path\", 42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h3\", 117);\n    i0.ɵɵtext(7, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 118);\n    i0.ɵɵtext(9, \"Notre syst\\u00E8me examine le code selon les crit\\u00E8res d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 119)(11, \"div\", 120);\n    i0.ɵɵelement(12, \"div\", 121)(13, \"div\", 122)(14, \"div\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 124);\n    i0.ɵɵtext(16, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectEvaluationComponent_div_11_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_11_div_50_div_1_Template, 42, 3, \"div\", 10);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_11_div_50_div_2_Template, 17, 0, \"div\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 29);\n    i0.ɵɵelement(5, \"path\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h2\", 31);\n    i0.ɵɵtext(7, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 32)(9, \"div\", 33)(10, \"p\", 34);\n    i0.ɵɵtext(11, \"Projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 35);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 33)(15, \"p\", 34);\n    i0.ɵɵtext(16, \"\\u00C9tudiant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 35);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 33)(20, \"p\", 34);\n    i0.ɵɵtext(21, \"Date de soumission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 35);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 33)(26, \"p\", 34);\n    i0.ɵɵtext(27, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 35);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(30, ProjectEvaluationComponent_div_11_div_30_Template, 8, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 37)(32, \"div\", 38)(33, \"div\", 39)(34, \"div\", 40)(35, \"div\", 41);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 29);\n    i0.ɵɵelement(37, \"path\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(38, \"h2\", 31);\n    i0.ɵɵtext(39, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 43)(41, \"span\");\n    i0.ɵɵtext(42, \" Manuel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵtext(44, \" IA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_11_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.toggleEvaluationMode());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(46, \"svg\", 45);\n    i0.ɵɵelement(47, \"path\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(48, \" Changer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(49, ProjectEvaluationComponent_div_11_form_49_Template, 79, 28, \"form\", 47);\n    i0.ɵɵtemplate(50, ProjectEvaluationComponent_div_11_div_50_Template, 3, 2, \"div\", 48);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r3.rendu.projet.titre);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r3.rendu.etudiant.nom, \" \", ctx_r3.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(24, 14, ctx_r3.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r3.rendu.description || \"Aucune description\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.rendu.fichiers && ctx_r3.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassMapInterpolate1(\"px-3 py-2 text-sm font-medium \", ctx_r3.evaluationMode === \"manual\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600\", \" rounded-md transition-all duration-200\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"px-3 py-2 text-sm font-medium \", ctx_r3.evaluationMode === \"ai\" ? \"bg-white text-purple-600 shadow-sm\" : \"text-gray-600\", \" rounded-md transition-all duration-200\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.successMessage = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n    console.log('Form values:', this.evaluationForm.value);\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\n      console.log('Form is invalid, marking fields as touched');\n      this.markFormGroupTouched(this.evaluationForm);\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\n      return;\n    }\n    this.isSubmitting = true;\n    this.error = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    console.log('Sending evaluation data:', evaluationData);\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/list-rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/list-rendus']);\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldError(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return 'Ce champ est obligatoire';\n      }\n      if (field.errors['min']) {\n        return `La valeur minimum est ${field.errors['min'].min}`;\n      }\n      if (field.errors['max']) {\n        return `La valeur maximum est ${field.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"p-8\"], [1, \"flex\", \"items-center\", \"mb-8\"], [1, \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"p-3\", \"rounded-lg\", \"mr-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-3xl\", \"font-bold\", \"text-gray-800\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded-r-lg\", 4, \"ngIf\"], [\"class\", \"bg-green-50 border-l-4 border-green-400 p-4 mb-6 rounded-r-lg\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"flex\", \"flex-col\", \"items-center\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-t-4\", \"border-b-4\", \"border-purple-500\"], [1, \"mt-4\", \"text-gray-600\"], [1, \"bg-red-50\", \"border-l-4\", \"border-red-400\", \"p-4\", \"mb-6\", \"rounded-r-lg\"], [1, \"flex\"], [1, \"flex-shrink-0\"], [\"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-red-400\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\", \"clip-rule\", \"evenodd\"], [1, \"ml-3\"], [1, \"text-sm\", \"text-red-700\"], [1, \"bg-green-50\", \"border-l-4\", \"border-green-400\", \"p-4\", \"mb-6\", \"rounded-r-lg\"], [\"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-green-400\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\", \"clip-rule\", \"evenodd\"], [1, \"text-sm\", \"text-green-700\"], [1, \"mb-8\", \"bg-gradient-to-r\", \"from-blue-50\", \"to-purple-50\", \"rounded-xl\", \"p-6\", \"border\", \"border-blue-100\"], [1, \"flex\", \"items-center\", \"mb-4\"], [1, \"bg-blue-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-2xl\", \"font-semibold\", \"text-gray-800\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\", \"mb-4\"], [1, \"bg-white\", \"p-4\", \"rounded-lg\", \"shadow-sm\"], [1, \"text-sm\", \"text-gray-600\", \"mb-1\"], [1, \"font-semibold\", \"text-gray-800\"], [\"class\", \"bg-white p-4 rounded-lg shadow-sm\", 4, \"ngIf\"], [1, \"mb-8\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-sm\", \"border\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-6\"], [1, \"flex\", \"items-center\"], [1, \"bg-purple-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"rounded-lg\", \"p-1\"], [1, \"ml-2\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"text-white\", \"rounded-lg\", \"hover:from-purple-600\", \"hover:to-blue-600\", \"transition-all\", \"duration-200\", \"shadow-sm\", \"hover:shadow-md\", \"transform\", \"hover:scale-105\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"], [\"class\", \"space-y-6\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-600\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"], [1, \"font-medium\", \"text-gray-800\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-2\"], [\"target\", \"_blank\", \"class\", \"flex items-center p-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200 text-blue-700 hover:text-blue-800\", 3, \"href\", 4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"flex\", \"items-center\", \"p-2\", \"bg-blue-50\", \"hover:bg-blue-100\", \"rounded-lg\", \"transition-colors\", \"duration-200\", \"text-blue-700\", \"hover:text-blue-800\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"truncate\"], [1, \"space-y-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"bg-gray-50\", \"rounded-lg\", \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"mb-4\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\", \"text-purple-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"form-group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\"], [1, \"text-red-500\"], [1, \"relative\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-3\"], [1, \"text-sm\", \"text-gray-500\"], [\"class\", \"mt-1 text-sm text-red-600\", 4, \"ngIf\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\"], [1, \"mt-6\", \"p-4\", \"bg-white\", \"rounded-lg\", \"border-2\", \"border-purple-200\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-700\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-600\"], [1, \"text-lg\", \"text-gray-500\", \"ml-1\"], [1, \"mt-2\", \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2\"], [1, \"bg-gradient-to-r\", \"from-purple-500\", \"to-blue-500\", \"h-2\", \"rounded-full\", \"transition-all\", \"duration-300\"], [\"formControlName\", \"commentaires\", \"rows\", \"6\", \"placeholder\", \"Saisissez vos commentaires d\\u00E9taill\\u00E9s sur l'\\u00E9valuation...\"], [1, \"mt-2\", \"text-sm\", \"text-gray-500\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\", \"pt-6\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", 1, \"w-full\", \"sm:w-auto\", \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"text-gray-700\", \"rounded-lg\", \"hover:bg-gray-50\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"type\", \"submit\", 1, \"w-full\", \"sm:w-auto\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-green-500\", \"to-emerald-500\", \"text-white\", \"rounded-lg\", \"hover:from-green-600\", \"hover:to-emerald-600\", \"transition-all\", \"duration-200\", \"font-semibold\", \"shadow-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", \"disabled:shadow-none\", 3, \"disabled\"], [\"class\", \"flex items-center justify-center\", 4, \"ngIf\"], [1, \"mt-1\", \"text-sm\", \"text-red-600\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"bg-gradient-to-br\", \"from-indigo-50\", \"to-purple-50\", \"rounded-xl\", \"p-6\", \"border\", \"border-indigo-200\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [1, \"bg-indigo-500\", \"p-2\", \"rounded-lg\", \"mr-3\"], [1, \"text-xl\", \"font-semibold\", \"text-gray-800\"], [1, \"bg-white\", \"rounded-lg\", \"p-4\", \"mb-6\", \"border\", \"border-indigo-100\"], [1, \"flex\", \"items-start\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-indigo-500\", \"mt-0.5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\", \"mb-1\"], [1, \"text-sm\", \"text-gray-600\", \"mb-2\"], [1, \"text-sm\", \"text-gray-600\", \"space-y-1\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"w-3\", \"h-3\", \"text-green-500\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\"], [1, \"w-full\", \"sm:w-auto\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-indigo-500\", \"to-purple-500\", \"text-white\", \"rounded-lg\", \"hover:from-indigo-600\", \"hover:to-purple-600\", \"transition-all\", \"duration-200\", \"font-semibold\", \"shadow-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:transform-none\", \"disabled:shadow-none\", 3, \"disabled\", \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"text-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-t-4\", \"border-b-4\", \"border-indigo-500\", \"mx-auto\", \"mb-6\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-indigo-500\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"mb-2\"], [1, \"text-gray-600\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"p-4\", \"max-w-md\", \"mx-auto\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-indigo-500\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"], [1, \"text-sm\", \"text-gray-500\", \"mt-2\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(4, \"svg\", 4);\n          i0.ɵɵelement(5, \"path\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"h1\", 6);\n          i0.ɵɵtext(7, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, ProjectEvaluationComponent_div_8_Template, 5, 0, \"div\", 7);\n          i0.ɵɵtemplate(9, ProjectEvaluationComponent_div_9_Template, 8, 1, \"div\", 8);\n          i0.ɵɵtemplate(10, ProjectEvaluationComponent_div_10_Template, 8, 1, \"div\", 9);\n          i0.ɵɵtemplate(11, ProjectEvaluationComponent_div_11_Template, 51, 17, \"div\", 10);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n.fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.5s ease-out;\\n}\\n\\n.pulse-animation[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);\\n  border-color: #8b5cf6;\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);\\n}\\n\\n\\n\\n.card-hover[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.card-hover[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #8b5cf6, #3b82f6);\\n  transition: width 0.5s ease;\\n}\\n\\n\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\\n}\\n\\n.alert-error[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n\\n  .grid-responsive[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n\\n\\n.icon-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ctx_r2", "successMessage", "ɵɵproperty", "fichier_r8", "ɵɵsanitizeUrl", "split", "pop", "ɵɵtemplate", "ProjectEvaluationComponent_div_11_div_30_a_7_Template", "ɵɵtextInterpolate1", "ctx_r4", "rendu", "fichiers", "length", "ctx_r9", "getFieldError", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ɵɵlistener", "ProjectEvaluationComponent_div_11_form_49_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r17", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProjectEvaluationComponent_div_11_form_49_div_17_Template", "ProjectEvaluationComponent_div_11_form_49_div_28_Template", "ProjectEvaluationComponent_div_11_form_49_div_39_Template", "ProjectEvaluationComponent_div_11_form_49_div_50_Template", "ProjectEvaluationComponent_div_11_form_49_div_68_Template", "ProjectEvaluationComponent_div_11_form_49_Template_button_click_72_listener", "ctx_r18", "annuler", "ProjectEvaluationComponent_div_11_form_49_span_77_Template", "ProjectEvaluationComponent_div_11_form_49_span_78_Template", "ctx_r5", "evaluationForm", "ɵɵclassMapInterpolate1", "isFieldInvalid", "getScoreTotal", "getScoreMaximum", "ɵɵstyleProp", "invalid", "isSubmitting", "ProjectEvaluationComponent_div_11_div_50_div_1_Template_button_click_35_listener", "_r24", "ctx_r23", "ProjectEvaluationComponent_div_11_div_50_div_1_Template_button_click_39_listener", "ctx_r25", "ProjectEvaluationComponent_div_11_div_50_div_1_span_40_Template", "ProjectEvaluationComponent_div_11_div_50_div_1_span_41_Template", "ctx_r19", "ProjectEvaluationComponent_div_11_div_50_div_1_Template", "ProjectEvaluationComponent_div_11_div_50_div_2_Template", "ctx_r6", "aiProcessing", "ProjectEvaluationComponent_div_11_div_30_Template", "ProjectEvaluationComponent_div_11_Template_button_click_45_listener", "_r27", "ctx_r26", "toggleEvaluationMode", "ProjectEvaluationComponent_div_11_form_49_Template", "ProjectEvaluationComponent_div_11_div_50_Template", "ctx_r3", "projet", "titre", "ɵɵtextInterpolate2", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "evaluationMode", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "isLoading", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "err", "console", "log", "valid", "value", "markFormGroupTouched", "evaluationData", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "field", "dirty", "touched", "errors", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ProjectEvaluationComponent_div_8_Template", "ProjectEvaluationComponent_div_9_Template", "ProjectEvaluationComponent_div_10_Template", "ProjectEvaluationComponent_div_11_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  successMessage: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\r\n    console.log('Form values:', this.evaluationForm.value);\r\n\r\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\r\n      console.log('Form is invalid, marking fields as touched');\r\n      this.markFormGroupTouched(this.evaluationForm);\r\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n    console.log('Sending evaluation data:', evaluationData);\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/list-rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    this.router.navigate(['/admin/projects/list-rendus']);\r\n  }\r\n\r\n  markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    if (field && field.errors && (field.dirty || field.touched)) {\r\n      if (field.errors['required']) {\r\n        return 'Ce champ est obligatoire';\r\n      }\r\n      if (field.errors['min']) {\r\n        return `La valeur minimum est ${field.errors['min'].min}`;\r\n      }\r\n      if (field.errors['max']) {\r\n        return `La valeur maximum est ${field.errors['max'].max}`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"container mx-auto px-4 py-8\">\r\n  <div class=\"max-w-4xl mx-auto bg-white rounded-xl shadow-lg p-8\">\r\n    <div class=\"flex items-center mb-8\">\r\n      <div class=\"bg-gradient-to-r from-purple-500 to-blue-500 p-3 rounded-lg mr-4\">\r\n        <svg class=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n        </svg>\r\n      </div>\r\n      <h1 class=\"text-3xl font-bold text-gray-800\">Évaluation du projet</h1>\r\n    </div>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\r\n      <div class=\"flex flex-col items-center\">\r\n        <div class=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-purple-500\"></div>\r\n        <p class=\"mt-4 text-gray-600\">Chargement en cours...</p>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"error\" class=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6 rounded-r-lg\">\r\n      <div class=\"flex\">\r\n        <div class=\"flex-shrink-0\">\r\n          <svg class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\" />\r\n          </svg>\r\n        </div>\r\n        <div class=\"ml-3\">\r\n          <p class=\"text-sm text-red-700\">{{ error }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"successMessage\" class=\"bg-green-50 border-l-4 border-green-400 p-4 mb-6 rounded-r-lg\">\r\n      <div class=\"flex\">\r\n        <div class=\"flex-shrink-0\">\r\n          <svg class=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\r\n          </svg>\r\n        </div>\r\n        <div class=\"ml-3\">\r\n          <p class=\"text-sm text-green-700\">{{ successMessage }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"rendu && !isLoading\">\r\n      <div class=\"mb-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100\">\r\n        <div class=\"flex items-center mb-4\">\r\n          <div class=\"bg-blue-500 p-2 rounded-lg mr-3\">\r\n            <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n            </svg>\r\n          </div>\r\n          <h2 class=\"text-2xl font-semibold text-gray-800\">Informations sur le rendu</h2>\r\n        </div>\r\n\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Projet</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.projet.titre }}</p>\r\n          </div>\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Étudiant</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n          </div>\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Date de soumission</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n          </div>\r\n          <div class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n            <p class=\"text-sm text-gray-600 mb-1\">Description</p>\r\n            <p class=\"font-semibold text-gray-800\">{{ rendu.description || 'Aucune description' }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"bg-white p-4 rounded-lg shadow-sm\">\r\n          <div class=\"flex items-center mb-3\">\r\n            <svg class=\"w-5 h-5 text-gray-600 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"></path>\r\n            </svg>\r\n            <h3 class=\"font-medium text-gray-800\">Fichiers joints ({{ rendu.fichiers.length }})</h3>\r\n          </div>\r\n          <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\r\n            <a *ngFor=\"let fichier of rendu.fichiers\"\r\n               [href]=\"'http://localhost:3000/' + fichier\"\r\n               target=\"_blank\"\r\n               class=\"flex items-center p-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200 text-blue-700 hover:text-blue-800\">\r\n              <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n              <span class=\"text-sm truncate\">{{ fichier.split('/').pop() }}</span>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-8\">\r\n        <div class=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n          <div class=\"flex items-center justify-between mb-6\">\r\n            <div class=\"flex items-center\">\r\n              <div class=\"bg-purple-500 p-2 rounded-lg mr-3\">\r\n                <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h2 class=\"text-2xl font-semibold text-gray-800\">Mode d'évaluation</h2>\r\n            </div>\r\n            <div class=\"flex items-center bg-gray-100 rounded-lg p-1\">\r\n              <span class=\"px-3 py-2 text-sm font-medium {{ evaluationMode === 'manual' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600' }} rounded-md transition-all duration-200\">\r\n                Manuel\r\n              </span>\r\n              <span class=\"px-3 py-2 text-sm font-medium {{ evaluationMode === 'ai' ? 'bg-white text-purple-600 shadow-sm' : 'text-gray-600' }} rounded-md transition-all duration-200\">\r\n                IA\r\n              </span>\r\n              <button\r\n                (click)=\"toggleEvaluationMode()\"\r\n                class=\"ml-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n              >\r\n                <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"></path>\r\n                </svg>\r\n                Changer\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\" class=\"space-y-6\">\r\n            <div class=\"bg-gray-50 rounded-lg p-6\">\r\n              <h3 class=\"text-lg font-semibold text-gray-800 mb-4 flex items-center\">\r\n                <svg class=\"w-5 h-5 mr-2 text-purple-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\r\n                </svg>\r\n                Critères d'évaluation\r\n              </h3>\r\n\r\n              <div formGroupName=\"scores\" class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Structure du code\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"structure\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.structure') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.structure')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.structure') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Bonnes pratiques\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"pratiques\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.pratiques') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.pratiques')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.pratiques') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Fonctionnalité\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"fonctionnalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.fonctionnalite') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.fonctionnalite')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.fonctionnalite') }}\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Originalité\r\n                    <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div class=\"relative\">\r\n                    <input\r\n                      type=\"number\"\r\n                      formControlName=\"originalite\"\r\n                      min=\"0\"\r\n                      max=\"5\"\r\n                      class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('scores.originalite') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                      placeholder=\"0-5\"\r\n                    >\r\n                    <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                      <span class=\"text-sm text-gray-500\">/5</span>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isFieldInvalid('scores.originalite')\" class=\"mt-1 text-sm text-red-600\">\r\n                    {{ getFieldError('scores.originalite') }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Score total display -->\r\n              <div class=\"mt-6 p-4 bg-white rounded-lg border-2 border-purple-200\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <span class=\"text-lg font-semibold text-gray-700\">Score total:</span>\r\n                  <div class=\"flex items-center\">\r\n                    <span class=\"text-2xl font-bold text-purple-600\">{{ getScoreTotal() }}</span>\r\n                    <span class=\"text-lg text-gray-500 ml-1\">/{{ getScoreMaximum() }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"mt-2 w-full bg-gray-200 rounded-full h-2\">\r\n                  <div class=\"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300\"\r\n                       [style.width.%]=\"(getScoreTotal() / getScoreMaximum()) * 100\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                Commentaires détaillés\r\n                <span class=\"text-red-500\">*</span>\r\n              </label>\r\n              <textarea\r\n                formControlName=\"commentaires\"\r\n                rows=\"6\"\r\n                class=\"w-full px-4 py-3 border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 {{ isFieldInvalid('commentaires') ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400' }}\"\r\n                placeholder=\"Saisissez vos commentaires détaillés sur l'évaluation...\"\r\n              ></textarea>\r\n              <div *ngIf=\"isFieldInvalid('commentaires')\" class=\"mt-1 text-sm text-red-600\">\r\n                {{ getFieldError('commentaires') }}\r\n              </div>\r\n              <p class=\"mt-2 text-sm text-gray-500\">Décrivez les points forts et les axes d'amélioration du projet.</p>\r\n            </div>\r\n\r\n            <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center pt-6 border-t border-gray-200\">\r\n              <button\r\n                type=\"button\"\r\n                (click)=\"annuler()\"\r\n                class=\"w-full sm:w-auto px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium\"\r\n              >\r\n                <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                </svg>\r\n                Annuler\r\n              </button>\r\n\r\n              <button\r\n                type=\"submit\"\r\n                [disabled]=\"evaluationForm.invalid || isSubmitting\"\r\n                class=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none\"\r\n              >\r\n                <span *ngIf=\"!isSubmitting\" class=\"flex items-center justify-center\">\r\n                  <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  Soumettre l'évaluation\r\n                </span>\r\n                <span *ngIf=\"isSubmitting\" class=\"flex items-center justify-center\">\r\n                  <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                    <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Soumission en cours...\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </form>\r\n\r\n          <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-200\">\r\n            <div *ngIf=\"!aiProcessing\">\r\n              <div class=\"flex items-center mb-4\">\r\n                <div class=\"bg-indigo-500 p-2 rounded-lg mr-3\">\r\n                  <svg class=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <h3 class=\"text-xl font-semibold text-gray-800\">Évaluation automatique par IA</h3>\r\n              </div>\r\n\r\n              <div class=\"bg-white rounded-lg p-4 mb-6 border border-indigo-100\">\r\n                <div class=\"flex items-start\">\r\n                  <div class=\"flex-shrink-0\">\r\n                    <svg class=\"w-5 h-5 text-indigo-500 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div class=\"ml-3\">\r\n                    <h4 class=\"text-sm font-medium text-gray-900 mb-1\">Comment ça fonctionne</h4>\r\n                    <p class=\"text-sm text-gray-600 mb-2\">Notre système d'IA (Mistral 7B) analysera automatiquement le code soumis selon les critères suivants :</p>\r\n                    <ul class=\"text-sm text-gray-600 space-y-1\">\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Structure et organisation du code\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Respect des bonnes pratiques\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Fonctionnalités implémentées\r\n                      </li>\r\n                      <li class=\"flex items-center\">\r\n                        <svg class=\"w-3 h-3 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\r\n                        </svg>\r\n                        Originalité et créativité\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"flex flex-col sm:flex-row gap-4 justify-between items-center\">\r\n                <button\r\n                  type=\"button\"\r\n                  (click)=\"annuler()\"\r\n                  class=\"w-full sm:w-auto px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium\"\r\n                >\r\n                  <svg class=\"w-4 h-4 inline mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                  Annuler\r\n                </button>\r\n\r\n                <button\r\n                  (click)=\"onSubmit()\"\r\n                  [disabled]=\"isSubmitting\"\r\n                  class=\"w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none\"\r\n                >\r\n                  <span *ngIf=\"!isSubmitting\" class=\"flex items-center justify-center\">\r\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\r\n                    </svg>\r\n                    Lancer l'évaluation IA\r\n                  </span>\r\n                  <span *ngIf=\"isSubmitting\" class=\"flex items-center justify-center\">\r\n                    <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                      <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    Lancement en cours...\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"aiProcessing\" class=\"text-center py-12\">\r\n              <div class=\"relative\">\r\n                <div class=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-500 mx-auto mb-6\"></div>\r\n                <div class=\"absolute inset-0 flex items-center justify-center\">\r\n                  <svg class=\"w-6 h-6 text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <h3 class=\"text-lg font-semibold text-gray-800 mb-2\">L'IA analyse le projet...</h3>\r\n              <p class=\"text-gray-600 mb-4\">Notre système examine le code selon les critères d'évaluation</p>\r\n              <div class=\"bg-white rounded-lg p-4 max-w-md mx-auto\">\r\n                <div class=\"flex items-center justify-center space-x-2\">\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\"></div>\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\" style=\"animation-delay: 0.1s\"></div>\r\n                  <div class=\"w-2 h-2 bg-indigo-500 rounded-full animate-bounce\" style=\"animation-delay: 0.2s\"></div>\r\n                </div>\r\n                <p class=\"text-sm text-gray-500 mt-2\">Cela peut prendre quelques instants</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;;;ICU/DC,EAAA,CAAAC,cAAA,cAAyD;IAErDD,EAAA,CAAAE,SAAA,cAA+F;IAC/FF,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAI5DJ,EAAA,CAAAC,cAAA,cAAqF;IAG/ED,EAAA,CAAAK,cAAA,EAA0E;IAA1EL,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAE,SAAA,eAA4Q;IAC9QF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAkB;IAAlBN,EAAA,CAAAC,cAAA,cAAkB;IACgBD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAfJ,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAKjDV,EAAA,CAAAC,cAAA,cAAkG;IAG5FD,EAAA,CAAAK,cAAA,EAA4E;IAA5EL,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,SAAA,eAA0L;IAC5LF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAkB;IAAlBN,EAAA,CAAAC,cAAA,cAAkB;IACkBD,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAxBJ,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,iBAAA,CAAAG,MAAA,CAAAC,cAAA,CAAoB;;;;;IA2CpDZ,EAAA,CAAAC,cAAA,YAG0I;IACxID,EAAA,CAAAK,cAAA,EAAgF;IAAhFL,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,eAAiN;IACnNF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,eAAA,EAA+B;IAA/BN,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IANnEJ,EAAA,CAAAa,UAAA,oCAAAC,UAAA,EAAAd,EAAA,CAAAe,aAAA,CAA2C;IAMbf,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAQ,iBAAA,CAAAM,UAAA,CAAAE,KAAA,MAAAC,GAAA,GAA8B;;;;;IAfnEjB,EAAA,CAAAC,cAAA,cAAmG;IAE/FD,EAAA,CAAAK,cAAA,EAA8F;IAA9FL,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,eAAA,EAAsC;IAAtCN,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,GAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE1FJ,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAkB,UAAA,IAAAC,qDAAA,gBAQI;IACNnB,EAAA,CAAAI,YAAA,EAAM;;;;IAZkCJ,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAoB,kBAAA,sBAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,MAAA,MAA6C;IAG5DxB,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAa,UAAA,YAAAQ,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;IAuElCvB,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAoB,kBAAA,MAAAK,MAAA,CAAAC,aAAA,0BACF;;;;;IAqBA1B,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAoB,kBAAA,MAAAO,OAAA,CAAAD,aAAA,0BACF;;;;;IAqBA1B,EAAA,CAAAC,cAAA,cAAuF;IACrFD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAoB,kBAAA,MAAAQ,OAAA,CAAAF,aAAA,+BACF;;;;;IAqBA1B,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAoB,kBAAA,MAAAS,OAAA,CAAAH,aAAA,4BACF;;;;;IA+BJ1B,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAoB,kBAAA,MAAAU,OAAA,CAAAJ,aAAA,sBACF;;;;;IAqBE1B,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAK,cAAA,EAAgF;IAAhFL,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,cAA+H;IACjIF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAK,cAAA,EAA2H;IAA3HL,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,iBAAkG;IAEpGF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,+BACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;;IArKbJ,EAAA,CAAAM,eAAA,EAAiH;IAAjHN,EAAA,CAAAC,cAAA,eAAiH;IAA9ED,EAAA,CAAA+B,UAAA,sBAAAC,4EAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAoC,aAAA;MAAA,OAAYpC,EAAA,CAAAqC,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxDtC,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAK,cAAA,EAAgG;IAAhGL,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,SAAA,eAAsR;IACxRF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,wCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAM,eAAA,EAA0E;IAA1EN,EAAA,CAAAC,cAAA,cAA0E;IAGpED,EAAA,CAAAG,MAAA,0BACA;IAAAH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErCJ,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAG,MAAA,UAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGjDJ,EAAA,CAAAkB,UAAA,KAAAqB,yDAAA,kBAEM;IACRvC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAG,MAAA,0BACA;IAAAH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErCJ,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAG,MAAA,UAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGjDJ,EAAA,CAAAkB,UAAA,KAAAsB,yDAAA,kBAEM;IACRxC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAG,MAAA,6BACA;IAAAH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErCJ,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAG,MAAA,UAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGjDJ,EAAA,CAAAkB,UAAA,KAAAuB,yDAAA,kBAEM;IACRzC,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAG,MAAA,0BACA;IAAAH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErCJ,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,iBAOC;IACDF,EAAA,CAAAC,cAAA,eAA+D;IACzBD,EAAA,CAAAG,MAAA,UAAE;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGjDJ,EAAA,CAAAkB,UAAA,KAAAwB,yDAAA,kBAEM;IACR1C,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,eAAqE;IAEfD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrEJ,EAAA,CAAAC,cAAA,eAA+B;IACoBD,EAAA,CAAAG,MAAA,IAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC7EJ,EAAA,CAAAC,cAAA,gBAAyC;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAG5EJ,EAAA,CAAAC,cAAA,eAAsD;IACpDD,EAAA,CAAAE,SAAA,eACyE;IAC3EF,EAAA,CAAAI,YAAA,EAAM;IAIVJ,EAAA,CAAAC,cAAA,eAAwB;IAEpBD,EAAA,CAAAG,MAAA,0CACA;IAAAH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErCJ,EAAA,CAAAE,SAAA,oBAKY;IACZF,EAAA,CAAAkB,UAAA,KAAAyB,yDAAA,kBAEM;IACN3C,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAG,MAAA,iFAA+D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAG3GJ,EAAA,CAAAC,cAAA,eAAwG;IAGpGD,EAAA,CAAA+B,UAAA,mBAAAa,4EAAA;MAAA5C,EAAA,CAAAiC,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAA7C,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAQ,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnB9C,EAAA,CAAAK,cAAA,EAAuF;IAAvFL,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAsG;IACxGF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAM,eAAA,EAIC;IAJDN,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAAkB,UAAA,KAAA6B,0DAAA,mBAKO;IACP/C,EAAA,CAAAkB,UAAA,KAAA8B,0DAAA,mBAMO;IACThD,EAAA,CAAAI,YAAA,EAAS;;;;IAtKPJ,EAAA,CAAAa,UAAA,cAAAoC,MAAA,CAAAC,cAAA,CAA4B;IAqBtBlD,EAAA,CAAAO,SAAA,IAAuQ;IAAvQP,EAAA,CAAAmD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,gGAAuQ;IAOrQpD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAa,UAAA,SAAAoC,MAAA,CAAAG,cAAA,qBAAwC;IAgB1CpD,EAAA,CAAAO,SAAA,GAAuQ;IAAvQP,EAAA,CAAAmD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,gGAAuQ;IAOrQpD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAa,UAAA,SAAAoC,MAAA,CAAAG,cAAA,qBAAwC;IAgB1CpD,EAAA,CAAAO,SAAA,GAA4Q;IAA5QP,EAAA,CAAAmD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,qGAA4Q;IAO1QpD,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAa,UAAA,SAAAoC,MAAA,CAAAG,cAAA,0BAA6C;IAgB/CpD,EAAA,CAAAO,SAAA,GAAyQ;IAAzQP,EAAA,CAAAmD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,kGAAyQ;IAOvQpD,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAa,UAAA,SAAAoC,MAAA,CAAAG,cAAA,uBAA0C;IAWGpD,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAyC,MAAA,CAAAI,aAAA,GAAqB;IAC7BrD,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAoB,kBAAA,MAAA6B,MAAA,CAAAK,eAAA,OAAwB;IAK9DtD,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAuD,WAAA,UAAAN,MAAA,CAAAI,aAAA,KAAAJ,MAAA,CAAAK,eAAA,cAA6D;IAapEtD,EAAA,CAAAO,SAAA,GAAmQ;IAAnQP,EAAA,CAAAmD,sBAAA,qJAAAF,MAAA,CAAAG,cAAA,4FAAmQ;IAG/PpD,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAa,UAAA,SAAAoC,MAAA,CAAAG,cAAA,iBAAoC;IAoBxCpD,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAa,UAAA,aAAAoC,MAAA,CAAAC,cAAA,CAAAM,OAAA,IAAAP,MAAA,CAAAQ,YAAA,CAAmD;IAG5CzD,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAa,UAAA,UAAAoC,MAAA,CAAAQ,YAAA,CAAmB;IAMnBzD,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAa,UAAA,SAAAoC,MAAA,CAAAQ,YAAA,CAAkB;;;;;IA+EvBzD,EAAA,CAAAC,cAAA,eAAqE;IACnED,EAAA,CAAAK,cAAA,EAAgF;IAAhFL,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,gBAA4G;IAC9GF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,oCACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAK,cAAA,EAA2H;IAA3HL,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,SAAA,iBAAkG;IAEpGF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IA/EbJ,EAAA,CAAAC,cAAA,UAA2B;IAGrBD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,aAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAgD;IAAhDN,EAAA,CAAAC,cAAA,cAAgD;IAAAD,EAAA,CAAAG,MAAA,yCAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAGpFJ,EAAA,CAAAC,cAAA,eAAmE;IAG7DD,EAAA,CAAAK,cAAA,EAAkG;IAAlGL,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAE,SAAA,iBAA2I;IAC7IF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAkB;IAAlBN,EAAA,CAAAC,cAAA,eAAkB;IACmCD,EAAA,CAAAG,MAAA,kCAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7EJ,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAG,MAAA,wHAAsG;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChJJ,EAAA,CAAAC,cAAA,eAA4C;IAExCD,EAAA,CAAAK,cAAA,EAAiF;IAAjFL,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,SAAA,iBAA4K;IAC9KF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,2CACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAM,eAAA,EAA8B;IAA9BN,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAK,cAAA,EAAiF;IAAjFL,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,SAAA,iBAA4K;IAC9KF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAM,eAAA,EAA8B;IAA9BN,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAK,cAAA,EAAiF;IAAjFL,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,SAAA,iBAA4K;IAC9KF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,qDACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAM,eAAA,EAA8B;IAA9BN,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAK,cAAA,EAAiF;IAAjFL,EAAA,CAAAC,cAAA,gBAAiF;IAC/ED,EAAA,CAAAE,SAAA,iBAA4K;IAC9KF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,kDACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAMbJ,EAAA,CAAAM,eAAA,EAA0E;IAA1EN,EAAA,CAAAC,cAAA,gBAA0E;IAGtED,EAAA,CAAA+B,UAAA,mBAAA2B,iFAAA;MAAA1D,EAAA,CAAAiC,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAuB,OAAA,CAAAd,OAAA,EAAS;IAAA,EAAC;IAGnB9C,EAAA,CAAAK,cAAA,EAAuF;IAAvFL,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAsG;IACxGF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAM,eAAA,EAIC;IAJDN,EAAA,CAAAC,cAAA,mBAIC;IAHCD,EAAA,CAAA+B,UAAA,mBAAA8B,iFAAA;MAAA7D,EAAA,CAAAiC,aAAA,CAAA0B,IAAA;MAAA,MAAAG,OAAA,GAAA9D,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAyB,OAAA,CAAAxB,QAAA,EAAU;IAAA,EAAC;IAIpBtC,EAAA,CAAAkB,UAAA,KAAA6C,+DAAA,mBAKO;IACP/D,EAAA,CAAAkB,UAAA,KAAA8C,+DAAA,mBAMO;IACThE,EAAA,CAAAI,YAAA,EAAS;;;;IAhBPJ,EAAA,CAAAO,SAAA,IAAyB;IAAzBP,EAAA,CAAAa,UAAA,aAAAoD,OAAA,CAAAR,YAAA,CAAyB;IAGlBzD,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAa,UAAA,UAAAoD,OAAA,CAAAR,YAAA,CAAmB;IAMnBzD,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAa,UAAA,SAAAoD,OAAA,CAAAR,YAAA,CAAkB;;;;;IAW/BzD,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,SAAA,eAA4G;IAC5GF,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAK,cAAA,EAA2F;IAA3FL,EAAA,CAAAC,cAAA,eAA2F;IACzFD,EAAA,CAAAE,SAAA,eAAkS;IACpSF,EAAA,CAAAI,YAAA,EAAM;IAGVJ,EAAA,CAAAM,eAAA,EAAqD;IAArDN,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnFJ,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAG,MAAA,mFAA6D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC/FJ,EAAA,CAAAC,cAAA,gBAAsD;IAElDD,EAAA,CAAAE,SAAA,gBAAqE;IAGvEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAsC;IAAAD,EAAA,CAAAG,MAAA,2CAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAtGnFJ,EAAA,CAAAM,eAAA,EAAmI;IAAnIN,EAAA,CAAAC,cAAA,cAAmI;IACjID,EAAA,CAAAkB,UAAA,IAAAgD,uDAAA,mBAkFM;IAENlE,EAAA,CAAAkB,UAAA,IAAAiD,uDAAA,mBAmBM;IACRnE,EAAA,CAAAI,YAAA,EAAM;;;;IAxGEJ,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAa,UAAA,UAAAuD,MAAA,CAAAC,YAAA,CAAmB;IAoFnBrE,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAa,UAAA,SAAAuD,MAAA,CAAAC,YAAA,CAAkB;;;;;;IAhVhCrE,EAAA,CAAAC,cAAA,UAAiC;IAIzBD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAiD;IAAjDN,EAAA,CAAAC,cAAA,aAAiD;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAGjFJ,EAAA,CAAAC,cAAA,cAAwD;IAEdD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAErEJ,EAAA,CAAAC,cAAA,eAA+C;IACPD,EAAA,CAAAG,MAAA,qBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,IAAoD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjGJ,EAAA,CAAAC,cAAA,eAA+C;IACPD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC5DJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,IAAoD;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEjGJ,EAAA,CAAAC,cAAA,eAA+C;IACPD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrDJ,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,IAA+C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAI9FJ,EAAA,CAAAkB,UAAA,KAAAoD,iDAAA,kBAkBM;IACRtE,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAkB;IAKRD,EAAA,CAAAK,cAAA,EAAsF;IAAtFL,EAAA,CAAAC,cAAA,eAAsF;IACpFD,EAAA,CAAAE,SAAA,gBAAkS;IACpSF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAM,eAAA,EAAiD;IAAjDN,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAG,MAAA,8BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEzEJ,EAAA,CAAAC,cAAA,eAA0D;IAEtDD,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,YAA0K;IACxKD,EAAA,CAAAG,MAAA,YACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAA+B,UAAA,mBAAAwC,oEAAA;MAAAvE,EAAA,CAAAiC,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAAzE,EAAA,CAAAoC,aAAA;MAAA,OAASpC,EAAA,CAAAqC,WAAA,CAAAoC,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC1E,EAAA,CAAAK,cAAA,EAAuF;IAAvFL,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,SAAA,gBAAkI;IACpIF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAIbJ,EAAA,CAAAkB,UAAA,KAAAyD,kDAAA,qBAwKO;IAEP3E,EAAA,CAAAkB,UAAA,KAAA0D,iDAAA,kBAyGM;IACR5E,EAAA,CAAAI,YAAA,EAAM;;;;IAvVqCJ,EAAA,CAAAO,SAAA,IAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAqE,MAAA,CAAAvD,KAAA,CAAAwD,MAAA,CAAAC,KAAA,CAAwB;IAIxB/E,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAgF,kBAAA,KAAAH,MAAA,CAAAvD,KAAA,CAAA2D,QAAA,CAAAC,GAAA,OAAAL,MAAA,CAAAvD,KAAA,CAAA2D,QAAA,CAAAE,MAAA,KAAoD;IAIpDnF,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAoF,WAAA,SAAAP,MAAA,CAAAvD,KAAA,CAAA+D,cAAA,sBAAoD;IAIpDrF,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAQ,iBAAA,CAAAqE,MAAA,CAAAvD,KAAA,CAAAgE,WAAA,yBAA+C;IAIpFtF,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAa,UAAA,SAAAgE,MAAA,CAAAvD,KAAA,CAAAC,QAAA,IAAAsD,MAAA,CAAAvD,KAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;IAiC3CxB,EAAA,CAAAO,SAAA,IAAuK;IAAvKP,EAAA,CAAAmD,sBAAA,mCAAA0B,MAAA,CAAAU,cAAA,kHAAuK;IAGvKvF,EAAA,CAAAO,SAAA,GAAmK;IAAnKP,EAAA,CAAAmD,sBAAA,mCAAA0B,MAAA,CAAAU,cAAA,8GAAmK;IAejHvF,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAa,UAAA,SAAAgE,MAAA,CAAAU,cAAA,cAAiC;IA0KvFvF,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAa,UAAA,SAAAgE,MAAA,CAAAU,cAAA,UAA6B;;;AD7R7C,OAAM,MAAOC,0BAA0B;EAWrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAdvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAxE,KAAK,GAAQ,IAAI;IAEjB,KAAAyE,SAAS,GAAY,IAAI;IACzB,KAAAtC,YAAY,GAAY,KAAK;IAC7B,KAAA/C,KAAK,GAAW,EAAE;IAClB,KAAAE,cAAc,GAAW,EAAE;IAC3B,KAAA2E,cAAc,GAAoB,QAAQ;IAC1C,KAAAlB,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAACnB,cAAc,GAAG,IAAI,CAACwC,EAAE,CAACM,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACP,EAAE,CAACM,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAACnG,UAAU,CAACoG,QAAQ,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,EAAErG,UAAU,CAACsG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACvG,UAAU,CAACoG,QAAQ,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,EAAErG,UAAU,CAACsG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACxG,UAAU,CAACoG,QAAQ,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,EAAErG,UAAU,CAACsG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACzG,UAAU,CAACoG,QAAQ,EAAEpG,UAAU,CAACqG,GAAG,CAAC,CAAC,CAAC,EAAErG,UAAU,CAACsG,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAE1G,UAAU,CAACoG,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACb,OAAO,GAAG,IAAI,CAACH,KAAK,CAACiB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACpB,KAAK,CAACiB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACxB,cAAc,GAAGwB,IAAI;MAC1B,IAAI,CAAC7D,cAAc,CAAC+D,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC7B,cAAc,GAAG6B,UAAU;QAChC,IAAI,CAAClE,cAAc,CAAC+D,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACtB,OAAO,EAAE;MAChB,IAAI,CAACwB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAC5G,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACqF,SAAS,GAAG,KAAK;;EAE1B;EAEAuB,SAASA,CAAA;IACP,IAAI,CAACvB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAAC0B,YAAY,CAAC,IAAI,CAACzB,OAAO,CAAC,CAAC0B,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACpG,KAAK,GAAGoG,IAAI;QACjB,IAAI,CAAC3B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDrF,KAAK,EAAGiH,GAAQ,IAAI;QAClB,IAAI,CAACjH,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACqF,SAAS,GAAG,KAAK;QACtB6B,OAAO,CAAClH,KAAK,CAACiH,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAjD,oBAAoBA,CAAA;IAClB,IAAI,CAACa,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAACrC,cAAc,CAAC+D,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACnB,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E2B,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC5B,cAAc,CAAC;EAC7D;EAEAjD,QAAQA,CAAA;IACNsF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC3E,cAAc,CAAC4E,KAAK,CAAC;IACrEF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC3E,cAAc,CAAC6E,KAAK,CAAC;IAEtD,IAAI,IAAI,CAACxC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACrC,cAAc,CAACM,OAAO,EAAE;MACnEoE,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACG,oBAAoB,CAAC,IAAI,CAAC9E,cAAc,CAAC;MAC9C,IAAI,CAACxC,KAAK,GAAG,gDAAgD;MAC7D;;IAGF,IAAI,CAAC+C,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC/C,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAAC6E,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAACrC,cAAc,CAAC+D,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACrC,YAAY,GAAG,IAAI;;IAG1B,MAAM4D,cAAc,GAAG,IAAI,CAAC/E,cAAc,CAAC6E,KAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,cAAc,CAAC;IAEvD,IAAI,CAACpC,aAAa,CAACqC,aAAa,CAAC,IAAI,CAACpC,OAAO,EAAEmC,cAAc,CAAC,CAACT,SAAS,CAAC;MACvEC,IAAI,EAAGU,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC5C,cAAc,KAAK,IAAI,IAAI4C,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACnC,MAAM;UAC3C,MAAMqC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAAC3B,YAAY;UAEvD,IAAI,CAACvD,cAAc,CAAC+D,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEmC,QAAQ,CAACnC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAE+B,QAAQ,CAAC/B,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE8B,QAAQ,CAAC9B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE6B,QAAQ,CAAC7B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE6B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAACjE,YAAY,GAAG,KAAK;UACzB,IAAI,CAACZ,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAAC/C,KAAK,GAAG,EAAE;UACf6H,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAAC9E,YAAY,GAAG,KAAK;UACzB8E,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;;MAEzD,CAAC;MACD9H,KAAK,EAAGiH,GAAQ,IAAI;QAClB,IAAI,CAACjH,KAAK,GAAG,yCAAyC,IAAIiH,GAAG,CAACjH,KAAK,EAAE+H,OAAO,IAAId,GAAG,CAACc,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAAChF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACY,YAAY,GAAG,KAAK;QACzBuD,OAAO,CAAClH,KAAK,CAACiH,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAtE,aAAaA,CAAA;IACX,MAAM4C,MAAM,GAAG,IAAI,CAAC/C,cAAc,CAAC4D,GAAG,CAAC,QAAQ,CAAC,EAAEiB,KAAK;IACvD,IAAI,CAAC9B,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAlD,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAR,OAAOA,CAAA;IACL,IAAI,CAAC8C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EACvD;EAEAR,oBAAoBA,CAACU,SAAoB;IACvCC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAC5B,GAAG,CAACiC,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAYlJ,SAAS,EAAE;QAChC,IAAI,CAACkI,oBAAoB,CAACgB,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEA5F,cAAcA,CAAC8F,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAACjG,cAAc,CAAC4D,GAAG,CAACoC,SAAS,CAAC;IAChD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAAC3F,OAAO,KAAK2F,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACrE;EAEA3H,aAAaA,CAACwH,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACjG,cAAc,CAAC4D,GAAG,CAACoC,SAAS,CAAC;IAChD,IAAIC,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAKH,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,EAAE;MAC3D,IAAIF,KAAK,CAACG,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,0BAA0B;;MAEnC,IAAIH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,CAAClD,GAAG,EAAE;;MAE3D,IAAI+C,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBH,KAAK,CAACG,MAAM,CAAC,KAAK,CAAC,CAACjD,GAAG,EAAE;;;IAG7D,OAAO,EAAE;EACX;;;uBAxLWb,0BAA0B,EAAAxF,EAAA,CAAAuJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzJ,EAAA,CAAAuJ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3J,EAAA,CAAAuJ,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA5J,EAAA,CAAAuJ,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1BtE,0BAA0B;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvCrK,EAAA,CAAAC,cAAA,aAAyC;UAIjCD,EAAA,CAAAK,cAAA,EAAsF;UAAtFL,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAE,SAAA,cAA+H;UACjIF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAM,eAAA,EAA6C;UAA7CN,EAAA,CAAAC,cAAA,YAA6C;UAAAD,EAAA,CAAAG,MAAA,gCAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGxEJ,EAAA,CAAAkB,UAAA,IAAAqJ,yCAAA,iBAKM;UAENvK,EAAA,CAAAkB,UAAA,IAAAsJ,yCAAA,iBAWM;UAENxK,EAAA,CAAAkB,UAAA,KAAAuJ,0CAAA,iBAWM;UAENzK,EAAA,CAAAkB,UAAA,KAAAwJ,0CAAA,oBAuWM;UACR1K,EAAA,CAAAI,YAAA,EAAM;;;UAzYEJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAa,UAAA,SAAAyJ,GAAA,CAAAvE,SAAA,CAAe;UAOf/F,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAa,UAAA,SAAAyJ,GAAA,CAAA5J,KAAA,CAAW;UAaXV,EAAA,CAAAO,SAAA,GAAoB;UAApBP,EAAA,CAAAa,UAAA,SAAAyJ,GAAA,CAAA1J,cAAA,CAAoB;UAapBZ,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAa,UAAA,SAAAyJ,GAAA,CAAAhJ,KAAA,KAAAgJ,GAAA,CAAAvE,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}