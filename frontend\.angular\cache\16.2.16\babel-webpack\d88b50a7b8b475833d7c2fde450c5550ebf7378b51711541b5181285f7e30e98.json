{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/projets.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/services/data.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"confirmDialog\"];\nfunction ListProjectComponent_div_40_div_1_div_33_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 66);\n    i0.ɵɵelement(3, \"path\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 68);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 69);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 70);\n    i0.ɵɵelement(8, \"path\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.getFileName(file_r9));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r8.getFileUrl(file_r9), i0.ɵɵsanitizeUrl)(\"download\", ctx_r8.getFileName(file_r9));\n  }\n}\nfunction ListProjectComponent_div_40_div_1_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"h4\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 52);\n    i0.ɵɵelement(3, \"path\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵtemplate(6, ListProjectComponent_div_40_div_1_div_33_div_6_Template, 10, 3, \"div\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projet_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Fichiers (\", projet_r5.fichiers.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", projet_r5.fichiers);\n  }\n}\nfunction ListProjectComponent_div_40_div_1_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"h4\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 52);\n    i0.ɵɵelement(3, \"path\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Fichiers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 73);\n    i0.ɵɵelement(7, \"path\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Aucun fichier joint \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c2 = function (a1) {\n  return [\"/admin/projects/details\", a1];\n};\nconst _c3 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c4 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nfunction ListProjectComponent_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"div\", 34)(3, \"div\", 35)(4, \"h3\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"div\", 38);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 39);\n    i0.ɵɵelement(9, \"path\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"span\", 41);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 38);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 42);\n    i0.ɵɵelement(14, \"path\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(15, \"span\", 16);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 43)(19, \"a\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 45);\n    i0.ɵɵelement(21, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_40_div_1_Template_button_click_22_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const projet_r5 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(projet_r5._id && ctx_r11.openDeleteDialog(projet_r5._id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 45);\n    i0.ɵɵelement(24, \"path\", 48);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(25, \"div\", 49)(26, \"div\", 50)(27, \"h4\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 52);\n    i0.ɵɵelement(29, \"path\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Description \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(31, \"p\", 54);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, ListProjectComponent_div_40_div_1_div_33_Template, 7, 2, \"div\", 55);\n    i0.ɵɵtemplate(34, ListProjectComponent_div_40_div_1_div_34_Template, 9, 0, \"div\", 55);\n    i0.ɵɵelementStart(35, \"div\", 56)(36, \"a\", 57);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(37, \"svg\", 58);\n    i0.ɵɵelement(38, \"path\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(40, \"a\", 59);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(41, \"svg\", 58);\n    i0.ɵɵelement(42, \"path\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43, \" Rendus \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const projet_r5 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.titre, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.groupe || \"Tous\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(17, 10, projet_r5.dateLimite, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(13, _c1, projet_r5._id));\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.description || \"Aucune description fournie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", projet_r5.fichiers && projet_r5.fichiers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !projet_r5.fichiers || projet_r5.fichiers.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c2, projet_r5._id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c3))(\"queryParams\", i0.ɵɵpureFunction1(18, _c4, projet_r5._id));\n  }\n}\nfunction ListProjectComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, ListProjectComponent_div_40_div_1_Template, 44, 20, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projets);\n  }\n}\nfunction ListProjectComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵelement(1, \"div\", 75);\n    i0.ɵɵelementStart(2, \"p\", 76);\n    i0.ɵɵtext(3, \"Chargement des projets...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ListProjectComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 79);\n    i0.ɵɵelement(3, \"path\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 81);\n    i0.ɵɵtext(5, \" Aucun projet disponible \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 82);\n    i0.ɵɵtext(7, \" Commencez par cr\\u00E9er votre premier projet en cliquant sur le bouton ci-dessous \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 83);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 84);\n    i0.ɵɵelement(10, \"path\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Ajouter un projet \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ListProjectComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"h2\", 88);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 89);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 90)(7, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_43_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDeleteCancel());\n    });\n    i0.ɵɵtext(8, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_43_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onDeleteConfirm());\n    });\n    i0.ɵɵtext(10, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.dialogData.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.dialogData.message);\n  }\n}\nexport class ListProjectComponent {\n  constructor(projetService, router, dialog, authService) {\n    this.projetService = projetService;\n    this.router = router;\n    this.dialog = dialog;\n    this.authService = authService;\n    this.projets = [];\n    this.isLoading = true;\n    this.isAdmin = false;\n    this.showDeleteDialog = false;\n    this.projectIdToDelete = null;\n    this.dialogData = {\n      title: 'Confirmer la suppression',\n      message: 'Êtes-vous sûr de vouloir supprimer ce projet?'\n    };\n  }\n  ngOnInit() {\n    console.log('Initialisation du composant ListProjectComponent');\n    this.loadProjets();\n    this.checkAdminStatus();\n  }\n  loadProjets() {\n    this.isLoading = true;\n    console.log('Chargement des projets...');\n    this.projetService.getProjets().subscribe({\n      next: projets => {\n        console.log('Projets reçus:', projets);\n        this.projets = projets;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Error loading projects', err);\n        this.isLoading = false;\n        // Afficher un message d'erreur à l'utilisateur\n        alert('Erreur lors du chargement des projets: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n      }\n    });\n  }\n  // Alias pour loadProjets pour assurer la compatibilité avec les méthodes existantes\n  loadProjects() {\n    this.loadProjets();\n  }\n  checkAdminStatus() {\n    this.isAdmin = this.authService.isAdmin();\n    console.log('Statut admin:', this.isAdmin);\n  }\n  editProjet(projetId) {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Édition du projet:', projetId);\n    this.router.navigate(['/admin/projects/edit', projetId]);\n  }\n  viewProjetDetails(projetId) {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Affichage des détails du projet:', projetId);\n    this.router.navigate(['/admin/projects/detail', projetId]);\n  }\n  deleteProjet(projetId) {\n    if (!projetId) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n      console.log('Suppression du projet confirmée:', projetId);\n      this.projetService.deleteProjet(projetId).subscribe({\n        next: () => {\n          console.log('Projet supprimé avec succès');\n          alert('Projet supprimé avec succès');\n          this.loadProjets(); // Reload the projects after deletion\n        },\n\n        error: err => {\n          console.error('Error deleting project', err);\n          alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n        }\n      });\n    }\n  }\n  openDeleteDialog(id) {\n    if (!id) {\n      console.error('ID du projet non défini');\n      return;\n    }\n    console.log('Ouverture de la boîte de dialogue de confirmation pour la suppression du projet:', id);\n    this.showDeleteDialog = true; // Show confirmation dialog\n    this.projectIdToDelete = id; // Store the ID of the project to be deleted\n  }\n\n  onDeleteConfirm() {\n    if (this.projectIdToDelete) {\n      console.log('Suppression du projet confirmée:', this.projectIdToDelete);\n      this.projetService.deleteProjet(this.projectIdToDelete).subscribe({\n        next: () => {\n          console.log('Projet supprimé avec succès');\n          alert('Projet supprimé avec succès');\n          this.loadProjets(); // Reload the projects after deletion\n          this.showDeleteDialog = false; // Close the confirmation dialog\n        },\n\n        error: err => {\n          console.error('Error deleting project', err);\n          alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n          this.showDeleteDialog = false;\n        }\n      });\n    }\n  }\n  onDeleteCancel() {\n    console.log('Suppression du projet annulée');\n    this.showDeleteDialog = false; // Close the confirmation dialog without deleting\n  }\n\n  getFileUrl(filePath) {\n    if (!filePath) return '';\n    // Extraire uniquement le nom du fichier\n    let fileName = filePath;\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      fileName = parts[parts.length - 1];\n    }\n    // Utiliser la route spécifique pour le téléchargement\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\n  }\n  getFileName(filePath) {\n    if (!filePath) return 'Fichier';\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\n      const parts = filePath.split(/[\\/\\\\]/);\n      return parts[parts.length - 1];\n    }\n    return filePath;\n  }\n  static {\n    this.ɵfac = function ListProjectComponent_Factory(t) {\n      return new (t || ListProjectComponent)(i0.ɵɵdirectiveInject(i1.ProjetService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListProjectComponent,\n      selectors: [[\"app-list-project\"]],\n      viewQuery: function ListProjectComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.confirmDialog = _t.first);\n        }\n      },\n      decls: 44,\n      vars: 6,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-6\", \"lg:mb-0\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-6\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"bg-success/10\", \"dark:bg-dark-accent-secondary/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-success\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-3\"], [\"routerLink\", \"/admin/projects/new\", 1, \"group\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md p-8 text-center mb-8\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\", \"mb-8\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group overflow-hidden\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"group\", \"overflow-hidden\"], [1, \"relative\", \"p-6\", \"bg-gradient-to-r\", \"from-primary/5\", \"to-secondary/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\"], [1, \"flex\", \"items-start\", \"justify-between\"], [1, \"flex-1\", \"pr-4\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\", \"line-clamp-2\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [1, \"flex\", \"space-x-2\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-all\", \"duration-200\"], [1, \"p-2\", \"bg-white/80\", \"dark:bg-dark-bg-tertiary/80\", \"backdrop-blur-sm\", \"rounded-lg\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"routerLink\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"p-2\", \"bg-white/80\", \"dark:bg-dark-bg-tertiary/80\", \"backdrop-blur-sm\", \"rounded-lg\", \"text-danger\", \"dark:text-danger-dark\", \"hover:bg-danger\", \"hover:text-white\", \"dark:hover:bg-danger-dark\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"p-5\", \"border-t\", \"border-[#edf1f4]\"], [1, \"mb-5\"], [1, \"text-xs\", \"font-semibold\", \"text-[#6d6870]\", \"uppercase\", \"tracking-wider\", \"mb-2\", \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\", \"text-[#4f5fad]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-[#6d6870]\", \"text-sm\", \"line-clamp-3\"], [\"class\", \"mb-5\", 4, \"ngIf\"], [1, \"mt-6\", \"flex\", \"space-x-3\"], [1, \"flex-1\", \"bg-[#edf1f4]\", \"hover:bg-[#dce4ec]\", \"text-[#4f5fad]\", \"py-2\", \"px-4\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-center\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"routerLink\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1.5\"], [1, \"flex-1\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"py-2\", \"px-4\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-center\", \"flex\", \"items-center\", \"justify-center\", \"transition-colors\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"space-y-2\", \"bg-[#edf1f4]\", \"rounded-lg\", \"p-3\"], [\"class\", \"flex items-center justify-between text-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"text-sm\"], [1, \"flex\", \"items-center\", \"truncate\", \"max-w-[70%]\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"text-[#7826b5]\", \"mr-2\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"truncate\", \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\", \"hover:text-[#7826b5]\", \"flex\", \"items-center\", 3, \"href\", \"download\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"bg-[#edf1f4]\", \"rounded-lg\", \"p-3\", \"text-sm\", \"text-[#6d6870]\", \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\", \"text-[#bdc6cc]\"], [1, \"text-center\", \"py-16\"], [1, \"animate-spin\", \"rounded-full\", \"h-10\", \"w-10\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\", \"mx-auto\"], [1, \"mt-4\", \"text-[#6d6870]\", \"font-medium\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"p-8\", \"text-center\", \"mb-8\"], [1, \"w-20\", \"h-20\", \"mx-auto\", \"mb-6\", \"bg-[#f0e6ff]\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-10\", \"w-10\", \"text-[#7826b5]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-2\"], [1, \"text-[#6d6870]\", \"mb-6\", \"max-w-md\", \"mx-auto\"], [\"routerLink\", \"/admin/projects/new\", 1, \"inline-flex\", \"items-center\", \"px-4\", \"py-2\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"rounded-lg\", \"shadow\", \"transition-all\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-50\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"w-full\", \"max-w-md\", \"p-6\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"text-[#6d6870]\", \"mb-6\"], [1, \"flex\", \"justify-end\", \"space-x-4\"], [1, \"px-4\", \"py-2\", \"bg-[#edf1f4]\", \"text-[#4f5fad]\", \"hover:bg-[#dce4ec]\", \"rounded-lg\", 3, \"click\"], [1, \"px-4\", \"py-2\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"rounded-lg\", 3, \"click\"]],\n      template: function ListProjectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(7, \"svg\", 7);\n          i0.ɵɵelement(8, \"path\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(9, \"div\")(10, \"h1\", 9);\n          i0.ɵɵtext(11, \" Gestion des Projets \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\", 10);\n          i0.ɵɵtext(13, \" Cr\\u00E9ez, g\\u00E9rez et suivez vos projets acad\\u00E9miques \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"div\", 13);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(17, \"svg\", 14);\n          i0.ɵɵelement(18, \"path\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(19, \"div\")(20, \"p\", 16);\n          i0.ɵɵtext(21, \"Total projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 17);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"div\", 18);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 19);\n          i0.ɵɵelement(27, \"path\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"div\")(29, \"p\", 16);\n          i0.ɵɵtext(30, \"Projets actifs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"p\", 17);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(33, \"div\", 21)(34, \"a\", 22)(35, \"div\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(36, \"svg\", 24);\n          i0.ɵɵelement(37, \"path\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(38, \"span\");\n          i0.ɵɵtext(39, \"Nouveau projet\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(40, ListProjectComponent_div_40_Template, 2, 1, \"div\", 26);\n          i0.ɵɵtemplate(41, ListProjectComponent_div_41_Template, 4, 0, \"div\", 27);\n          i0.ɵɵtemplate(42, ListProjectComponent_div_42_Template, 12, 0, \"div\", 28);\n          i0.ɵɵtemplate(43, ListProjectComponent_div_43_Template, 11, 2, \"div\", 29);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(23);\n          i0.ɵɵtextInterpolate((ctx.projets == null ? null : ctx.projets.length) || 0);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.getActiveProjectsCount());\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets && ctx.projets.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (!ctx.projets || ctx.projets.length === 0));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDeleteDialog);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i2.RouterLink, i5.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJsaXN0LXByb2plY3QuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcHJvamVjdHMvbGlzdC1wcm9qZWN0L2xpc3QtcHJvamVjdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r8", "getFileName", "file_r9", "ɵɵproperty", "getFileUrl", "ɵɵsanitizeUrl", "ɵɵtemplate", "ListProjectComponent_div_40_div_1_div_33_div_6_Template", "ɵɵtextInterpolate1", "projet_r5", "fichiers", "length", "ɵɵlistener", "ListProjectComponent_div_40_div_1_Template_button_click_22_listener", "restoredCtx", "ɵɵrestoreView", "_r12", "$implicit", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "_id", "openDeleteDialog", "ListProjectComponent_div_40_div_1_div_33_Template", "ListProjectComponent_div_40_div_1_div_34_Template", "titre", "groupe", "ɵɵpipeBind2", "dateLimite", "ɵɵpureFunction1", "_c1", "description", "_c2", "ɵɵpureFunction0", "_c3", "_c4", "ListProjectComponent_div_40_div_1_Template", "ctx_r0", "projets", "ListProjectComponent_div_43_Template_button_click_7_listener", "_r14", "ctx_r13", "onDeleteCancel", "ListProjectComponent_div_43_Template_button_click_9_listener", "ctx_r15", "onDeleteConfirm", "ctx_r3", "dialogData", "title", "message", "ListProjectComponent", "constructor", "projetService", "router", "dialog", "authService", "isLoading", "isAdmin", "showDeleteDialog", "projectIdToDelete", "ngOnInit", "console", "log", "loadProjets", "checkAdminStatus", "getProjets", "subscribe", "next", "error", "err", "alert", "loadProjects", "editProjet", "projetId", "navigate", "viewProjetDetails", "deleteProjet", "confirm", "id", "filePath", "fileName", "includes", "parts", "split", "urlBackend", "ɵɵdirectiveInject", "i1", "ProjetService", "i2", "Router", "i3", "MatDialog", "i4", "DataService", "selectors", "viewQuery", "ListProjectComponent_Query", "rf", "ctx", "ListProjectComponent_div_40_Template", "ListProjectComponent_div_41_Template", "ListProjectComponent_div_42_Template", "ListProjectComponent_div_43_Template", "getActiveProjectsCount"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\list-project\\list-project.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\list-project\\list-project.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Projet } from 'src/app/models/projet.model';\r\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { DataService } from 'src/app/services/data.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-list-project',\r\n  templateUrl: './list-project.component.html',\r\n  styleUrls: ['./list-project.component.css'],\r\n})\r\nexport class ListProjectComponent implements OnInit {\r\n  projets: Projet[] = [];\r\n  isLoading = true;\r\n  isAdmin = false;\r\n  showDeleteDialog = false;\r\n  projectIdToDelete: string | null = null;\r\n\r\n  // For the confirmation dialog\r\n  dialogRef?: MatDialogRef<any>;\r\n  dialogData = {\r\n    title: 'Confirmer la suppression',\r\n    message: 'Êtes-vous sûr de vouloir supprimer ce projet?',\r\n  };\r\n\r\n  @ViewChild('confirmDialog') confirmDialog!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    private projetService: ProjetService,\r\n    private router: Router,\r\n    private dialog: MatDialog,\r\n    private authService: DataService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    console.log('Initialisation du composant ListProjectComponent');\r\n    this.loadProjets();\r\n    this.checkAdminStatus();\r\n  }\r\n\r\n  loadProjets(): void {\r\n    this.isLoading = true;\r\n    console.log('Chargement des projets...');\r\n    this.projetService.getProjets().subscribe({\r\n      next: (projets) => {\r\n        console.log('Projets reçus:', projets);\r\n        this.projets = projets;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading projects', err);\r\n        this.isLoading = false;\r\n        // Afficher un message d'erreur à l'utilisateur\r\n        alert(\r\n          'Erreur lors du chargement des projets: ' +\r\n            (err.error?.message || err.message || 'Erreur inconnue')\r\n        );\r\n      },\r\n    });\r\n  }\r\n\r\n  // Alias pour loadProjets pour assurer la compatibilité avec les méthodes existantes\r\n  loadProjects(): void {\r\n    this.loadProjets();\r\n  }\r\n\r\n  checkAdminStatus(): void {\r\n    this.isAdmin = this.authService.isAdmin();\r\n    console.log('Statut admin:', this.isAdmin);\r\n  }\r\n\r\n  editProjet(projetId: string | undefined): void {\r\n    if (!projetId) {\r\n      console.error('ID du projet non défini');\r\n      return;\r\n    }\r\n    console.log('Édition du projet:', projetId);\r\n    this.router.navigate(['/admin/projects/edit', projetId]);\r\n  }\r\n\r\n  viewProjetDetails(projetId: string | undefined): void {\r\n    if (!projetId) {\r\n      console.error('ID du projet non défini');\r\n      return;\r\n    }\r\n    console.log('Affichage des détails du projet:', projetId);\r\n    this.router.navigate(['/admin/projects/detail', projetId]);\r\n  }\r\n\r\n  deleteProjet(projetId: string | undefined): void {\r\n    if (!projetId) {\r\n      console.error('ID du projet non défini');\r\n      return;\r\n    }\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\r\n      console.log('Suppression du projet confirmée:', projetId);\r\n      this.projetService.deleteProjet(projetId).subscribe({\r\n        next: () => {\r\n          console.log('Projet supprimé avec succès');\r\n          alert('Projet supprimé avec succès');\r\n          this.loadProjets(); // Reload the projects after deletion\r\n        },\r\n        error: (err) => {\r\n          console.error('Error deleting project', err);\r\n          alert(\r\n            'Erreur lors de la suppression du projet: ' +\r\n              (err.error?.message || err.message || 'Erreur inconnue')\r\n          );\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  openDeleteDialog(id: string): void {\r\n    if (!id) {\r\n      console.error('ID du projet non défini');\r\n      return;\r\n    }\r\n    console.log(\r\n      'Ouverture de la boîte de dialogue de confirmation pour la suppression du projet:',\r\n      id\r\n    );\r\n    this.showDeleteDialog = true; // Show confirmation dialog\r\n    this.projectIdToDelete = id; // Store the ID of the project to be deleted\r\n  }\r\n\r\n  onDeleteConfirm(): void {\r\n    if (this.projectIdToDelete) {\r\n      console.log('Suppression du projet confirmée:', this.projectIdToDelete);\r\n      this.projetService.deleteProjet(this.projectIdToDelete).subscribe({\r\n        next: () => {\r\n          console.log('Projet supprimé avec succès');\r\n          alert('Projet supprimé avec succès');\r\n          this.loadProjets(); // Reload the projects after deletion\r\n          this.showDeleteDialog = false; // Close the confirmation dialog\r\n        },\r\n        error: (err) => {\r\n          console.error('Error deleting project', err);\r\n          alert(\r\n            'Erreur lors de la suppression du projet: ' +\r\n              (err.error?.message || err.message || 'Erreur inconnue')\r\n          );\r\n          this.showDeleteDialog = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  onDeleteCancel(): void {\r\n    console.log('Suppression du projet annulée');\r\n    this.showDeleteDialog = false; // Close the confirmation dialog without deleting\r\n  }\r\n\r\n  getFileUrl(filePath: string): string {\r\n    if (!filePath) return '';\r\n\r\n    // Extraire uniquement le nom du fichier\r\n    let fileName = filePath;\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      fileName = parts[parts.length - 1];\r\n    }\r\n\r\n    // Utiliser la route spécifique pour le téléchargement\r\n    return `${environment.urlBackend}projets/telecharger/${fileName}`;\r\n  }\r\n\r\n  getFileName(filePath: string): string {\r\n    if (!filePath) return 'Fichier';\r\n\r\n    // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\r\n    if (filePath.includes('/') || filePath.includes('\\\\')) {\r\n      const parts = filePath.split(/[\\/\\\\]/);\r\n      return parts[parts.length - 1];\r\n    }\r\n\r\n    return filePath;\r\n  }\r\n}\r\n", "<!-- Begin Page Content -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary\">\r\n  <div class=\"container mx-auto px-4 py-8\">\r\n\r\n    <!-- Header moderne avec gradient -->\r\n    <div class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-8 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\">\r\n      <div class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\r\n        <div class=\"mb-6 lg:mb-0\">\r\n          <div class=\"flex items-center space-x-4 mb-4\">\r\n            <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary flex items-center justify-center shadow-lg\">\r\n              <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h1 class=\"text-3xl font-bold text-text-dark dark:text-dark-text-primary\">\r\n                Gestion des Projets\r\n              </h1>\r\n              <p class=\"text-text dark:text-dark-text-secondary\">\r\n                Créez, gérez et suivez vos projets académiques\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Statistiques -->\r\n          <div class=\"flex items-center space-x-6\">\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div class=\"bg-success/10 dark:bg-dark-accent-secondary/10 p-2 rounded-lg\">\r\n                <svg class=\"w-5 h-5 text-success dark:text-dark-accent-secondary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <p class=\"text-sm text-text dark:text-dark-text-secondary\">Total projets</p>\r\n                <p class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary\">{{ projets?.length || 0 }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"flex items-center space-x-2\">\r\n              <div class=\"bg-info/10 dark:bg-dark-accent-primary/10 p-2 rounded-lg\">\r\n                <svg class=\"w-5 h-5 text-info dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <p class=\"text-sm text-text dark:text-dark-text-secondary\">Projets actifs</p>\r\n                <p class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary\">{{ getActiveProjectsCount() }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Bouton d'ajout moderne -->\r\n        <div class=\"flex flex-col sm:flex-row gap-3\">\r\n          <a routerLink=\"/admin/projects/new\"\r\n             class=\"group px-6 py-3 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium\">\r\n            <div class=\"flex items-center justify-center space-x-2\">\r\n              <svg class=\"w-5 h-5 group-hover:scale-110 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\r\n              </svg>\r\n              <span>Nouveau projet</span>\r\n            </div>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Liste des projets en cartes modernes -->\r\n    <div *ngIf=\"!isLoading && projets && projets.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\r\n      <!-- Carte de projet moderne -->\r\n      <div *ngFor=\"let projet of projets\" class=\"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group overflow-hidden\">\r\n\r\n        <!-- Header du projet avec gradient -->\r\n        <div class=\"relative p-6 bg-gradient-to-r from-primary/5 to-secondary/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10\">\r\n          <div class=\"flex items-start justify-between\">\r\n            <div class=\"flex-1 pr-4\">\r\n              <h3 class=\"text-xl font-bold text-text-dark dark:text-dark-text-primary mb-2 line-clamp-2\">\r\n                {{ projet.titre }}\r\n              </h3>\r\n              <div class=\"flex items-center space-x-4\">\r\n                <!-- Badge groupe -->\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-primary dark:text-dark-accent-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm font-medium text-primary dark:text-dark-accent-primary\">\r\n                    {{ projet.groupe || \"Tous\" }}\r\n                  </span>\r\n                </div>\r\n                <!-- Date limite -->\r\n                <div class=\"flex items-center space-x-1\">\r\n                  <svg class=\"w-4 h-4 text-warning dark:text-warning\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  <span class=\"text-sm text-text dark:text-dark-text-secondary\">\r\n                    {{ projet.dateLimite | date : \"dd/MM/yyyy\" }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Actions flottantes -->\r\n            <div class=\"flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-200\">\r\n              <a [routerLink]=\"['/admin/projects/editProjet', projet._id]\"\r\n                 class=\"p-2 bg-white/80 dark:bg-dark-bg-tertiary/80 backdrop-blur-sm rounded-lg text-primary dark:text-dark-accent-primary hover:bg-primary hover:text-white dark:hover:bg-dark-accent-primary transition-all duration-200 shadow-lg\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\r\n                </svg>\r\n              </a>\r\n              <button (click)=\"projet._id && openDeleteDialog(projet._id)\"\r\n                      class=\"p-2 bg-white/80 dark:bg-dark-bg-tertiary/80 backdrop-blur-sm rounded-lg text-danger dark:text-danger-dark hover:bg-danger hover:text-white dark:hover:bg-danger-dark transition-all duration-200 shadow-lg\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      <!-- Project Content -->\r\n      <div class=\"p-5 border-t border-[#edf1f4]\">\r\n        <!-- Description -->\r\n        <div class=\"mb-5\">\r\n          <h4\r\n            class=\"text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-1 text-[#4f5fad]\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n              />\r\n            </svg>\r\n            Description\r\n          </h4>\r\n          <p class=\"text-[#6d6870] text-sm line-clamp-3\">\r\n            {{ projet.description || \"Aucune description fournie\" }}\r\n          </p>\r\n        </div>\r\n\r\n        <!-- Files Section -->\r\n        <div *ngIf=\"projet.fichiers && projet.fichiers.length > 0\" class=\"mb-5\">\r\n          <h4\r\n            class=\"text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-1 text-[#4f5fad]\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n              />\r\n            </svg>\r\n            Fichiers ({{ projet.fichiers.length }})\r\n          </h4>\r\n          <div class=\"space-y-2 bg-[#edf1f4] rounded-lg p-3\">\r\n            <div\r\n              *ngFor=\"let file of projet.fichiers\"\r\n              class=\"flex items-center justify-between text-sm\"\r\n            >\r\n              <div class=\"flex items-center truncate max-w-[70%]\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  class=\"h-4 w-4 text-[#7826b5] mr-2 flex-shrink-0\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  stroke=\"currentColor\"\r\n                >\r\n                  <path\r\n                    stroke-linecap=\"round\"\r\n                    stroke-linejoin=\"round\"\r\n                    stroke-width=\"2\"\r\n                    d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\r\n                  />\r\n                </svg>\r\n                <span class=\"truncate text-[#6d6870]\">{{\r\n                  getFileName(file)\r\n                }}</span>\r\n              </div>\r\n              <a\r\n                [href]=\"getFileUrl(file)\"\r\n                [download]=\"getFileName(file)\"\r\n                class=\"text-[#4f5fad] hover:text-[#7826b5] flex items-center\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  class=\"h-4 w-4 mr-1\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  stroke=\"currentColor\"\r\n                >\r\n                  <path\r\n                    stroke-linecap=\"round\"\r\n                    stroke-linejoin=\"round\"\r\n                    stroke-width=\"2\"\r\n                    d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"\r\n                  />\r\n                </svg>\r\n                Télécharger\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- No Files Message -->\r\n        <div\r\n          *ngIf=\"!projet.fichiers || projet.fichiers.length === 0\"\r\n          class=\"mb-5\"\r\n        >\r\n          <h4\r\n            class=\"text-xs font-semibold text-[#6d6870] uppercase tracking-wider mb-2 flex items-center\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-1 text-[#4f5fad]\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n              />\r\n            </svg>\r\n            Fichiers\r\n          </h4>\r\n          <div\r\n            class=\"bg-[#edf1f4] rounded-lg p-3 text-sm text-[#6d6870] flex items-center justify-center\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-1 text-[#bdc6cc]\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n              />\r\n            </svg>\r\n            Aucun fichier joint\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Actions -->\r\n        <div class=\"mt-6 flex space-x-3\">\r\n          <a\r\n            [routerLink]=\"['/admin/projects/details', projet._id]\"\r\n            class=\"flex-1 bg-[#edf1f4] hover:bg-[#dce4ec] text-[#4f5fad] py-2 px-4 rounded-lg text-sm font-medium text-center flex items-center justify-center transition-colors\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-1.5\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n              />\r\n            </svg>\r\n            Détails\r\n          </a>\r\n          <a\r\n            [routerLink]=\"['/admin/projects/rendus']\"\r\n            [queryParams]=\"{ projetId: projet._id }\"\r\n            class=\"flex-1 bg-[#7826b5] hover:bg-[#5f1d8f] text-white py-2 px-4 rounded-lg text-sm font-medium text-center flex items-center justify-center transition-colors\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              class=\"h-4 w-4 mr-1.5\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              stroke=\"currentColor\"\r\n            >\r\n              <path\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n                stroke-width=\"2\"\r\n                d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\r\n              />\r\n            </svg>\r\n            Rendus\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"isLoading\" class=\"text-center py-16\">\r\n    <div\r\n      class=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#4f5fad] mx-auto\"\r\n    ></div>\r\n    <p class=\"mt-4 text-[#6d6870] font-medium\">Chargement des projets...</p>\r\n  </div>\r\n\r\n  <!-- Empty State -->\r\n  <div\r\n    *ngIf=\"!isLoading && (!projets || projets.length === 0)\"\r\n    class=\"bg-white rounded-xl shadow-md p-8 text-center mb-8\"\r\n  >\r\n    <div\r\n      class=\"w-20 h-20 mx-auto mb-6 bg-[#f0e6ff] rounded-full flex items-center justify-center\"\r\n    >\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        class=\"h-10 w-10 text-[#7826b5]\"\r\n        fill=\"none\"\r\n        viewBox=\"0 0 24 24\"\r\n        stroke=\"currentColor\"\r\n      >\r\n        <path\r\n          stroke-linecap=\"round\"\r\n          stroke-linejoin=\"round\"\r\n          stroke-width=\"1.5\"\r\n          d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n        />\r\n      </svg>\r\n    </div>\r\n    <h3 class=\"text-xl font-bold text-[#4f5fad] mb-2\">\r\n      Aucun projet disponible\r\n    </h3>\r\n    <p class=\"text-[#6d6870] mb-6 max-w-md mx-auto\">\r\n      Commencez par créer votre premier projet en cliquant sur le bouton\r\n      ci-dessous\r\n    </p>\r\n    <a\r\n      routerLink=\"/admin/projects/new\"\r\n      class=\"inline-flex items-center px-4 py-2 bg-[#7826b5] hover:bg-[#5f1d8f] text-white rounded-lg shadow transition-all\"\r\n    >\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        class=\"h-5 w-5 mr-2\"\r\n        viewBox=\"0 0 20 20\"\r\n        fill=\"currentColor\"\r\n      >\r\n        <path\r\n          d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\"\r\n        />\r\n      </svg>\r\n      Ajouter un projet\r\n    </a>\r\n  </div>\r\n\r\n  <!-- Confirmation Dialog -->\r\n  <div\r\n    *ngIf=\"showDeleteDialog\"\r\n    class=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\"\r\n  >\r\n    <div class=\"bg-white rounded-xl shadow-lg w-full max-w-md p-6\">\r\n      <h2 class=\"text-xl font-bold text-[#4f5fad] mb-4\">\r\n        {{ dialogData.title }}\r\n      </h2>\r\n      <p class=\"text-[#6d6870] mb-6\">{{ dialogData.message }}</p>\r\n      <div class=\"flex justify-end space-x-4\">\r\n        <button\r\n          class=\"px-4 py-2 bg-[#edf1f4] text-[#4f5fad] hover:bg-[#dce4ec] rounded-lg\"\r\n          (click)=\"onDeleteCancel()\"\r\n        >\r\n          Annuler\r\n        </button>\r\n        <button\r\n          class=\"px-4 py-2 bg-[#7826b5] hover:bg-[#5f1d8f] text-white rounded-lg\"\r\n          (click)=\"onDeleteConfirm()\"\r\n        >\r\n          Supprimer\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAMA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;;;;ICkK9CC,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAsC;IAAtCL,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAM,MAAA,GAEpC;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAEXJ,EAAA,CAAAC,cAAA,YAIC;IACCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,8BACF;IAA<PERSON>,EAAA,CAAAI,YAAA,EAAI;;;;;IAxBoCJ,EAAA,CAAAO,SAAA,GAEpC;IAFoCP,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,OAAA,EAEpC;IAGFX,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAY,UAAA,SAAAH,MAAA,CAAAI,UAAA,CAAAF,OAAA,GAAAX,EAAA,CAAAc,aAAA,CAAyB,aAAAL,MAAA,CAAAC,WAAA,CAAAC,OAAA;;;;;IA7CjCX,EAAA,CAAAC,cAAA,cAAwE;IAIpED,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAK,eAAA,EAAmD;IAAnDL,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAe,UAAA,IAAAC,uDAAA,mBA4CM;IACRhB,EAAA,CAAAI,YAAA,EAAM;;;;IAhDJJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,gBAAAC,SAAA,CAAAC,QAAA,CAAAC,MAAA,OACF;IAGqBpB,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,UAAA,YAAAM,SAAA,CAAAC,QAAA,CAAkB;;;;;IAgDzCnB,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,iBACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAK,eAAA,EAEC;IAFDL,EAAA,CAAAC,cAAA,cAEC;IACCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,4BACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;;;;;;;;;;;;;;;;;IA7LVJ,EAAA,CAAAC,cAAA,cAAwP;IAO9OD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,cAAyC;IAGrCD,EAAA,CAAAE,cAAA,EAAsH;IAAtHF,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAG,SAAA,eAAwV;IAC1VH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAA6E;IAA7EL,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAGTJ,EAAA,CAAAC,cAAA,eAAyC;IACvCD,EAAA,CAAAE,cAAA,EAA0G;IAA1GF,EAAA,CAAAC,cAAA,eAA0G;IACxGD,EAAA,CAAAG,SAAA,gBAA6H;IAC/HH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAA8D;IAA9DL,EAAA,CAAAC,cAAA,gBAA8D;IAC5DD,EAAA,CAAAM,MAAA,IACF;;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAMbJ,EAAA,CAAAC,cAAA,eAA0F;IAGtFD,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAG,SAAA,gBAAwM;IAC1MH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAC2N;IAD3NL,EAAA,CAAAC,cAAA,kBAC2N;IADnND,EAAA,CAAAqB,UAAA,mBAAAC,oEAAA;MAAA,MAAAC,WAAA,GAAAvB,EAAA,CAAAwB,aAAA,CAAAC,IAAA;MAAA,MAAAP,SAAA,GAAAK,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAX,SAAA,CAAAY,GAAA,IAAcH,OAAA,CAAAI,gBAAA,CAAAb,SAAA,CAAAY,GAAA,CAA4B;IAAA,EAAC;IAE1D9B,EAAA,CAAAE,cAAA,EAA2E;IAA3EF,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAG,SAAA,gBAA8M;IAChNH,EAAA,CAAAI,YAAA,EAAM;IAOhBJ,EAAA,CAAAK,eAAA,EAA2C;IAA3CL,EAAA,CAAAC,cAAA,eAA2C;IAMrCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAG,SAAA,gBAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAK,eAAA,EAA+C;IAA/CL,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAM,MAAA,IACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAINJ,EAAA,CAAAe,UAAA,KAAAiB,iDAAA,kBAmEM;IAGNhC,EAAA,CAAAe,UAAA,KAAAkB,iDAAA,kBA0CM;IAGNjC,EAAA,CAAAC,cAAA,eAAiC;IAK7BD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAG,SAAA,gBAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,sBACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAK,eAAA,EAIC;IAJDL,EAAA,CAAAC,cAAA,aAIC;IACCD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAG,SAAA,gBAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,gBACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;IAnOEJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAC,SAAA,CAAAgB,KAAA,MACF;IAQMlC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAC,SAAA,CAAAiB,MAAA,gBACF;IAQEnC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAoC,WAAA,SAAAlB,SAAA,CAAAmB,UAAA,qBACF;IAODrC,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAsC,eAAA,KAAAC,GAAA,EAAArB,SAAA,CAAAY,GAAA,EAAyD;IAwC9D9B,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAC,SAAA,CAAAsB,WAAA,sCACF;IAIIxC,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAY,UAAA,SAAAM,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAC,MAAA,KAAmD;IAuEtDpB,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAY,UAAA,UAAAM,SAAA,CAAAC,QAAA,IAAAD,SAAA,CAAAC,QAAA,CAAAC,MAAA,OAAsD;IA8CrDpB,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAsC,eAAA,KAAAG,GAAA,EAAAvB,SAAA,CAAAY,GAAA,EAAsD;IAoBtD9B,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAA0C,eAAA,KAAAC,GAAA,EAAyC,gBAAA3C,EAAA,CAAAsC,eAAA,KAAAM,GAAA,EAAA1B,SAAA,CAAAY,GAAA;;;;;IAzNjD9B,EAAA,CAAAC,cAAA,cAA2H;IAEzHD,EAAA,CAAAe,UAAA,IAAA8B,0CAAA,oBA6OI;IACR7C,EAAA,CAAAI,YAAA,EAAM;;;;IA9OsBJ,EAAA,CAAAO,SAAA,GAAU;IAAVP,EAAA,CAAAY,UAAA,YAAAkC,MAAA,CAAAC,OAAA,CAAU;;;;;IAiPtC/C,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAG,SAAA,cAEO;IACPH,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAM,MAAA,gCAAyB;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;IAI1EJ,EAAA,CAAAC,cAAA,cAGC;IAIGD,EAAA,CAAAE,cAAA,EAMC;IANDF,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAG,SAAA,eAKE;IACJH,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAK,eAAA,EAAkD;IAAlDL,EAAA,CAAAC,cAAA,aAAkD;IAChDD,EAAA,CAAAM,MAAA,gCACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAAgD;IAC9CD,EAAA,CAAAM,MAAA,2FAEF;IAAAN,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAC,cAAA,YAGC;IACCD,EAAA,CAAAE,cAAA,EAKC;IALDF,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAG,SAAA,gBAEE;IACJH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,2BACF;IAAAN,EAAA,CAAAI,YAAA,EAAI;;;;;;IAINJ,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAM,MAAA,GAAwB;IAAAN,EAAA,CAAAI,YAAA,EAAI;IAC3DJ,EAAA,CAAAC,cAAA,cAAwC;IAGpCD,EAAA,CAAAqB,UAAA,mBAAA2B,6DAAA;MAAAhD,EAAA,CAAAwB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAqB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAE1BnD,EAAA,CAAAM,MAAA,gBACF;IAAAN,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAGC;IADCD,EAAA,CAAAqB,UAAA,mBAAA+B,6DAAA;MAAApD,EAAA,CAAAwB,aAAA,CAAAyB,IAAA;MAAA,MAAAI,OAAA,GAAArD,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAwB,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAE3BtD,EAAA,CAAAM,MAAA,mBACF;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAfTJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiB,kBAAA,MAAAsC,MAAA,CAAAC,UAAA,CAAAC,KAAA,MACF;IAC+BzD,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAA+C,MAAA,CAAAC,UAAA,CAAAE,OAAA,CAAwB;;;ADzW7D,OAAM,MAAOC,oBAAoB;EAgB/BC,YACUC,aAA4B,EAC5BC,MAAc,EACdC,MAAiB,EACjBC,WAAwB;IAHxB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAnBrB,KAAAjB,OAAO,GAAa,EAAE;IACtB,KAAAkB,SAAS,GAAG,IAAI;IAChB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAkB,IAAI;IAIvC,KAAAZ,UAAU,GAAG;MACXC,KAAK,EAAE,0BAA0B;MACjCC,OAAO,EAAE;KACV;EASE;EAEHW,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAC/D,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAD,WAAWA,CAAA;IACT,IAAI,CAACP,SAAS,GAAG,IAAI;IACrBK,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxC,IAAI,CAACV,aAAa,CAACa,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAG7B,OAAO,IAAI;QAChBuB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAExB,OAAO,CAAC;QACtC,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACkB,SAAS,GAAG,KAAK;MACxB,CAAC;MACDY,KAAK,EAAGC,GAAG,IAAI;QACbR,OAAO,CAACO,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;QAC5C,IAAI,CAACb,SAAS,GAAG,KAAK;QACtB;QACAc,KAAK,CACH,yCAAyC,IACtCD,GAAG,CAACD,KAAK,EAAEnB,OAAO,IAAIoB,GAAG,CAACpB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;MACH;KACD,CAAC;EACJ;EAEA;EACAsB,YAAYA,CAAA;IACV,IAAI,CAACR,WAAW,EAAE;EACpB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACP,OAAO,GAAG,IAAI,CAACF,WAAW,CAACE,OAAO,EAAE;IACzCI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACL,OAAO,CAAC;EAC5C;EAEAe,UAAUA,CAACC,QAA4B;IACrC,IAAI,CAACA,QAAQ,EAAE;MACbZ,OAAO,CAACO,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAEFP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEW,QAAQ,CAAC;IAC3C,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,sBAAsB,EAAED,QAAQ,CAAC,CAAC;EAC1D;EAEAE,iBAAiBA,CAACF,QAA4B;IAC5C,IAAI,CAACA,QAAQ,EAAE;MACbZ,OAAO,CAACO,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAEFP,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEW,QAAQ,CAAC;IACzD,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,wBAAwB,EAAED,QAAQ,CAAC,CAAC;EAC5D;EAEAG,YAAYA,CAACH,QAA4B;IACvC,IAAI,CAACA,QAAQ,EAAE;MACbZ,OAAO,CAACO,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAGF,IAAIS,OAAO,CAAC,gDAAgD,CAAC,EAAE;MAC7DhB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEW,QAAQ,CAAC;MACzD,IAAI,CAACrB,aAAa,CAACwB,YAAY,CAACH,QAAQ,CAAC,CAACP,SAAS,CAAC;QAClDC,IAAI,EAAEA,CAAA,KAAK;UACTN,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;UAC1CQ,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAACP,WAAW,EAAE,CAAC,CAAC;QACtB,CAAC;;QACDK,KAAK,EAAGC,GAAG,IAAI;UACbR,OAAO,CAACO,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;UAC5CC,KAAK,CACH,2CAA2C,IACxCD,GAAG,CAACD,KAAK,EAAEnB,OAAO,IAAIoB,GAAG,CAACpB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;QACH;OACD,CAAC;;EAEN;EAEA3B,gBAAgBA,CAACwD,EAAU;IACzB,IAAI,CAACA,EAAE,EAAE;MACPjB,OAAO,CAACO,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAEFP,OAAO,CAACC,GAAG,CACT,kFAAkF,EAClFgB,EAAE,CACH;IACD,IAAI,CAACpB,gBAAgB,GAAG,IAAI,CAAC,CAAC;IAC9B,IAAI,CAACC,iBAAiB,GAAGmB,EAAE,CAAC,CAAC;EAC/B;;EAEAjC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACc,iBAAiB,EAAE;MAC1BE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACH,iBAAiB,CAAC;MACvE,IAAI,CAACP,aAAa,CAACwB,YAAY,CAAC,IAAI,CAACjB,iBAAiB,CAAC,CAACO,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACTN,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;UAC1CQ,KAAK,CAAC,6BAA6B,CAAC;UACpC,IAAI,CAACP,WAAW,EAAE,CAAC,CAAC;UACpB,IAAI,CAACL,gBAAgB,GAAG,KAAK,CAAC,CAAC;QACjC,CAAC;;QACDU,KAAK,EAAGC,GAAG,IAAI;UACbR,OAAO,CAACO,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;UAC5CC,KAAK,CACH,2CAA2C,IACxCD,GAAG,CAACD,KAAK,EAAEnB,OAAO,IAAIoB,GAAG,CAACpB,OAAO,IAAI,iBAAiB,CAAC,CAC3D;UACD,IAAI,CAACS,gBAAgB,GAAG,KAAK;QAC/B;OACD,CAAC;;EAEN;EAEAhB,cAAcA,CAAA;IACZmB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACJ,gBAAgB,GAAG,KAAK,CAAC,CAAC;EACjC;;EAEAtD,UAAUA,CAAC2E,QAAgB;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIC,QAAQ,GAAGD,QAAQ;IAEvB;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtCH,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAACvE,MAAM,GAAG,CAAC,CAAC;;IAGpC;IACA,OAAO,GAAGrB,WAAW,CAAC8F,UAAU,uBAAuBJ,QAAQ,EAAE;EACnE;EAEA/E,WAAWA,CAAC8E,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAE/B;IACA,IAAIA,QAAQ,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD,MAAMC,KAAK,GAAGH,QAAQ,CAACI,KAAK,CAAC,QAAQ,CAAC;MACtC,OAAOD,KAAK,CAACA,KAAK,CAACvE,MAAM,GAAG,CAAC,CAAC;;IAGhC,OAAOoE,QAAQ;EACjB;;;uBAzKW7B,oBAAoB,EAAA3D,EAAA,CAAA8F,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAhG,EAAA,CAAA8F,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAlG,EAAA,CAAA8F,iBAAA,CAAAK,EAAA,CAAAC,SAAA,GAAApG,EAAA,CAAA8F,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApB3C,oBAAoB;MAAA4C,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCZjC1G,EAAA,CAAAC,cAAA,aAAiK;UASnJD,EAAA,CAAAE,cAAA,EAAsF;UAAtFF,EAAA,CAAAC,cAAA,aAAsF;UACpFD,EAAA,CAAAG,SAAA,cAA4J;UAC9JH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,UAAK;UAEDD,EAAA,CAAAM,MAAA,6BACF;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAAmD;UACjDD,EAAA,CAAAM,MAAA,uEACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAC,cAAA,eAAyC;UAGnCD,EAAA,CAAAE,cAAA,EAAwH;UAAxHF,EAAA,CAAAC,cAAA,eAAwH;UACtHD,EAAA,CAAAG,SAAA,gBAA+H;UACjIH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UACwDD,EAAA,CAAAM,MAAA,qBAAa;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAC5EJ,EAAA,CAAAC,cAAA,aAAwE;UAAAD,EAAA,CAAAM,MAAA,IAA0B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAG1GJ,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAE,cAAA,EAAmH;UAAnHF,EAAA,CAAAC,cAAA,eAAmH;UACjHD,EAAA,CAAAG,SAAA,gBAA6H;UAC/HH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UACwDD,EAAA,CAAAM,MAAA,sBAAc;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAC7EJ,EAAA,CAAAC,cAAA,aAAwE;UAAAD,EAAA,CAAAM,MAAA,IAA8B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAOlHJ,EAAA,CAAAC,cAAA,eAA6C;UAIvCD,EAAA,CAAAE,cAAA,EAAsH;UAAtHF,EAAA,CAAAC,cAAA,eAAsH;UACpHD,EAAA,CAAAG,SAAA,gBAA4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAAM;UAANL,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAM,MAAA,sBAAc;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAQrCJ,EAAA,CAAAe,UAAA,KAAA6F,oCAAA,kBAgPI;UAGN5G,EAAA,CAAAe,UAAA,KAAA8F,oCAAA,kBAKM;UAGN7G,EAAA,CAAAe,UAAA,KAAA+F,oCAAA,mBA6CM;UAGN9G,EAAA,CAAAe,UAAA,KAAAgG,oCAAA,mBAwBM;UACR/G,EAAA,CAAAI,YAAA,EAAM;;;UArWkFJ,EAAA,CAAAO,SAAA,IAA0B;UAA1BP,EAAA,CAAAQ,iBAAA,EAAAmG,GAAA,CAAA5D,OAAA,kBAAA4D,GAAA,CAAA5D,OAAA,CAAA3B,MAAA,OAA0B;UAW1BpB,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAQ,iBAAA,CAAAmG,GAAA,CAAAK,sBAAA,GAA8B;UAsB5GhH,EAAA,CAAAO,SAAA,GAAiD;UAAjDP,EAAA,CAAAY,UAAA,UAAA+F,GAAA,CAAA1C,SAAA,IAAA0C,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAA5D,OAAA,CAAA3B,MAAA,KAAiD;UAmPnDpB,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAY,UAAA,SAAA+F,GAAA,CAAA1C,SAAA,CAAe;UASlBjE,EAAA,CAAAO,SAAA,GAAsD;UAAtDP,EAAA,CAAAY,UAAA,UAAA+F,GAAA,CAAA1C,SAAA,MAAA0C,GAAA,CAAA5D,OAAA,IAAA4D,GAAA,CAAA5D,OAAA,CAAA3B,MAAA,QAAsD;UAgDtDpB,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAY,UAAA,SAAA+F,GAAA,CAAAxC,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}