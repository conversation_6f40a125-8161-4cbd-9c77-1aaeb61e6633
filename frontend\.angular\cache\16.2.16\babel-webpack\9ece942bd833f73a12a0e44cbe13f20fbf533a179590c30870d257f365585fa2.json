{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_24_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r7, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fichier_r7.split(\"/\").pop(), \" \");\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"h3\", 23);\n    i0.ɵɵtext(2, \"Fichiers joints:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 24);\n    i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_6_div_24_li_4_Template, 3, 2, \"li\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_34_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre l'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_34_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_form_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 27);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_6_form_34_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29)(3, \"label\", 30);\n    i0.ɵɵtext(4, \"Structure du code (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 29)(7, \"label\", 30);\n    i0.ɵɵtext(8, \"Bonnes pratiques (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"label\", 30);\n    i0.ɵɵtext(12, \"Fonctionnalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"label\", 30);\n    i0.ɵɵtext(16, \"Originalit\\u00E9 (0-5)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 35)(19, \"label\", 30);\n    i0.ɵɵtext(20, \"Commentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 37)(23, \"p\")(24, \"strong\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 38)(27, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_form_34_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.annuler());\n    });\n    i0.ɵɵtext(28, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 40);\n    i0.ɵɵtemplate(30, ProjectEvaluationComponent_div_6_form_34_span_30_Template, 2, 0, \"span\", 5);\n    i0.ɵɵtemplate(31, ProjectEvaluationComponent_div_6_form_34_span_31_Template, 2, 0, \"span\", 5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.evaluationForm);\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate2(\"Score total: \", ctx_r4.getScoreTotal(), \"/\", ctx_r4.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_35_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancer l'\\u00E9valuation IA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_35_div_1_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancement en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 43);\n    i0.ɵɵtext(2, \"L'\\u00E9valuation sera r\\u00E9alis\\u00E9e automatiquement par notre syst\\u00E8me d'IA (Mistral 7B).\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 14);\n    i0.ɵɵtext(4, \"L'IA analysera le code soumis et fournira une \\u00E9valuation bas\\u00E9e sur les crit\\u00E8res standards.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 38)(6, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_div_35_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.annuler());\n    });\n    i0.ɵɵtext(7, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_div_35_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.onSubmit());\n    });\n    i0.ɵɵtemplate(9, ProjectEvaluationComponent_div_6_div_35_div_1_span_9_Template, 2, 0, \"span\", 5);\n    i0.ɵɵtemplate(10, ProjectEvaluationComponent_div_6_div_35_div_1_span_10_Template, 2, 0, \"span\", 5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r13.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_35_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"p\", 47);\n    i0.ɵɵtext(3, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 48);\n    i0.ɵɵtext(5, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectEvaluationComponent_div_6_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_6_div_35_div_1_Template, 11, 3, \"div\", 5);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_6_div_35_div_2_Template, 6, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 9)(2, \"h2\", 10);\n    i0.ɵɵtext(3, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 11)(5, \"div\")(6, \"p\")(7, \"span\", 12);\n    i0.ɵɵtext(8, \"Projet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"span\", 12);\n    i0.ɵɵtext(12, \"\\u00C9tudiant:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\")(15, \"p\")(16, \"span\", 12);\n    i0.ɵɵtext(17, \"Date de soumission:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\")(21, \"span\", 12);\n    i0.ɵɵtext(22, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, ProjectEvaluationComponent_div_6_div_24_Template, 5, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 14)(26, \"div\", 15)(27, \"h2\", 16);\n    i0.ɵɵtext(28, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 17)(30, \"span\", 18);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_6_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.toggleEvaluationMode());\n    });\n    i0.ɵɵtext(33, \" Changer de mode \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(34, ProjectEvaluationComponent_div_6_form_34_Template, 32, 6, \"form\", 20);\n    i0.ɵɵtemplate(35, ProjectEvaluationComponent_div_6_div_35_Template, 3, 2, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.projet.titre, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(19, 9, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.rendu.description || \"Aucune description\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.evaluationMode === \"manual\" ? \"Manuel\" : \"IA\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"ai\");\n  }\n}\nexport class ProjectEvaluationComponent {\n  constructor(fb, route, router, rendusService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.rendusService = rendusService;\n    this.renduId = '';\n    this.rendu = null;\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.error = '';\n    this.successMessage = '';\n    this.evaluationMode = 'manual';\n    this.aiProcessing = false;\n    this.evaluationForm = this.fb.group({\n      scores: this.fb.group({\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n      }),\n      commentaires: ['', Validators.required],\n      utiliserIA: [false]\n    });\n  }\n  ngOnInit() {\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n    // Récupérer le mode d'évaluation des query params\n    const mode = this.route.snapshot.queryParamMap.get('mode');\n    if (mode === 'ai' || mode === 'manual') {\n      this.evaluationMode = mode;\n      this.evaluationForm.patchValue({\n        utiliserIA: mode === 'ai'\n      });\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\n      localStorage.setItem('evaluationMode', mode);\n    } else {\n      // Récupérer le mode d'évaluation du localStorage\n      const storedMode = localStorage.getItem('evaluationMode');\n      if (storedMode === 'ai' || storedMode === 'manual') {\n        this.evaluationMode = storedMode;\n        this.evaluationForm.patchValue({\n          utiliserIA: storedMode === 'ai'\n        });\n      }\n    }\n    if (this.renduId) {\n      this.loadRendu();\n    } else {\n      this.error = 'ID de rendu manquant';\n      this.isLoading = false;\n    }\n  }\n  loadRendu() {\n    this.isLoading = true;\n    this.rendusService.getRenduById(this.renduId).subscribe({\n      next: data => {\n        this.rendu = data;\n        this.isLoading = false;\n      },\n      error: err => {\n        this.error = 'Erreur lors du chargement du rendu';\n        this.isLoading = false;\n        console.error(err);\n      }\n    });\n  }\n  toggleEvaluationMode() {\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n    this.evaluationForm.patchValue({\n      utiliserIA: this.evaluationMode === 'ai'\n    });\n    localStorage.setItem('evaluationMode', this.evaluationMode);\n  }\n  onSubmit() {\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n    console.log('Form values:', this.evaluationForm.value);\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\n      console.log('Form is invalid, marking fields as touched');\n      this.markFormGroupTouched(this.evaluationForm);\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\n      return;\n    }\n    this.isSubmitting = true;\n    this.error = '';\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n    if (this.evaluationMode === 'ai') {\n      this.evaluationForm.patchValue({\n        utiliserIA: true\n      });\n      this.aiProcessing = true;\n    }\n    const evaluationData = this.evaluationForm.value;\n    console.log('Sending evaluation data:', evaluationData);\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n      next: response => {\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n        if (this.evaluationMode === 'ai' && response.evaluation) {\n          const aiScores = response.evaluation.scores;\n          const aiCommentaires = response.evaluation.commentaires;\n          this.evaluationForm.patchValue({\n            scores: {\n              structure: aiScores.structure || 0,\n              pratiques: aiScores.pratiques || 0,\n              fonctionnalite: aiScores.fonctionnalite || 0,\n              originalite: aiScores.originalite || 0\n            },\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\n          });\n          this.aiProcessing = false;\n          this.isSubmitting = false;\n          // Afficher un message de succès\n          this.error = '';\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n        } else {\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n          this.isSubmitting = false;\n          alert('Évaluation soumise avec succès!');\n          this.router.navigate(['/admin/projects/list-rendus']);\n        }\n      },\n      error: err => {\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n        this.isSubmitting = false;\n        this.aiProcessing = false;\n        console.error(err);\n      }\n    });\n  }\n  getScoreTotal() {\n    const scores = this.evaluationForm.get('scores')?.value;\n    if (!scores) return 0;\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n  }\n  getScoreMaximum() {\n    return 20; // 4 critères x 5 points maximum\n  }\n\n  annuler() {\n    this.router.navigate(['/admin/projects/list-rendus']);\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldError(fieldName) {\n    const field = this.evaluationForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return 'Ce champ est obligatoire';\n      }\n      if (field.errors['min']) {\n        return `La valeur minimum est ${field.errors['min'].min}`;\n      }\n      if (field.errors['max']) {\n        return `La valeur maximum est ${field.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n      return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectEvaluationComponent,\n      selectors: [[\"app-project-evaluation\"]],\n      decls: 7,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"max-w-4xl\", \"mx-auto\", \"bg-white\", \"rounded-lg\", \"shadow\", \"p-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-6\"], [\"class\", \"flex justify-center my-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-blue-500\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"mb-6\", \"p-4\", \"bg-gray-50\", \"rounded-lg\"], [1, \"text-xl\", \"font-semibold\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"font-medium\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-xl\", \"font-semibold\"], [1, \"flex\", \"items-center\"], [1, \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-blue-600\", \"text-white\", \"rounded\", \"hover:bg-blue-700\", \"transition-colors\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gray-50 p-4 rounded-lg\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"font-medium\", \"mb-2\"], [1, \"list-disc\", \"pl-5\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", 3, \"href\"], [3, \"formGroup\", \"ngSubmit\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\", \"mb-6\"], [1, \"form-group\"], [1, \"block\", \"text-gray-700\", \"mb-2\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [1, \"form-group\", \"mb-6\"], [\"formControlName\", \"commentaires\", \"rows\", \"5\", 1, \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-blue-500\"], [1, \"mb-4\", \"p-3\", \"bg-blue-50\", \"rounded\"], [1, \"flex\", \"justify-between\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"bg-gray-500\", \"text-white\", \"rounded\", \"hover:bg-gray-600\", \"transition-colors\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-6\", \"py-2\", \"bg-green-600\", \"text-white\", \"rounded\", \"hover:bg-green-700\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-blue-600\", \"text-white\", \"rounded\", \"hover:bg-blue-700\", \"transition-colors\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-blue-500\", \"mx-auto\", \"mb-4\"], [1, \"text-gray-700\"], [1, \"text-sm\", \"text-gray-500\", \"mt-2\"]],\n      template: function ProjectEvaluationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"\\u00C9valuation du projet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ProjectEvaluationComponent_div_4_Template, 2, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ProjectEvaluationComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵtemplate(6, ProjectEvaluationComponent_div_6_Template, 36, 12, \"div\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n      styles: [\"\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-top: 0.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin: 2rem 0;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n.fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.5s ease-out;\\n}\\n\\n.pulse-animation[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);\\n  border-color: #8b5cf6;\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);\\n}\\n\\n\\n\\n.card-hover[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.card-hover[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #8b5cf6, #3b82f6);\\n  transition: width 0.5s ease;\\n}\\n\\n\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\\n}\\n\\n.alert-error[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n\\n  .grid-responsive[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n\\n\\n.icon-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵproperty", "fichier_r7", "ɵɵsanitizeUrl", "split", "pop", "ɵɵtemplate", "ProjectEvaluationComponent_div_6_div_24_li_4_Template", "ctx_r3", "rendu", "fichiers", "ɵɵlistener", "ProjectEvaluationComponent_div_6_form_34_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ProjectEvaluationComponent_div_6_form_34_Template_button_click_27_listener", "ctx_r12", "annuler", "ProjectEvaluationComponent_div_6_form_34_span_30_Template", "ProjectEvaluationComponent_div_6_form_34_span_31_Template", "ctx_r4", "evaluationForm", "ɵɵtextInterpolate2", "getScoreTotal", "getScoreMaximum", "isSubmitting", "ProjectEvaluationComponent_div_6_div_35_div_1_Template_button_click_6_listener", "_r18", "ctx_r17", "ProjectEvaluationComponent_div_6_div_35_div_1_Template_button_click_8_listener", "ctx_r19", "ProjectEvaluationComponent_div_6_div_35_div_1_span_9_Template", "ProjectEvaluationComponent_div_6_div_35_div_1_span_10_Template", "ctx_r13", "ProjectEvaluationComponent_div_6_div_35_div_1_Template", "ProjectEvaluationComponent_div_6_div_35_div_2_Template", "ctx_r5", "aiProcessing", "ProjectEvaluationComponent_div_6_div_24_Template", "ProjectEvaluationComponent_div_6_Template_button_click_32_listener", "_r21", "ctx_r20", "toggleEvaluationMode", "ProjectEvaluationComponent_div_6_form_34_Template", "ProjectEvaluationComponent_div_6_div_35_Template", "ctx_r2", "projet", "titre", "etudiant", "nom", "prenom", "ɵɵpipeBind2", "dateSoumission", "description", "length", "ɵɵtextInterpolate", "evaluationMode", "ProjectEvaluationComponent", "constructor", "fb", "route", "router", "rendusService", "renduId", "isLoading", "successMessage", "group", "scores", "structure", "required", "min", "max", "pratiques", "fonctionnalite", "originalite", "commentaires", "utiliserIA", "ngOnInit", "snapshot", "paramMap", "get", "mode", "queryParamMap", "patchValue", "localStorage", "setItem", "storedMode", "getItem", "loadRendu", "getRenduById", "subscribe", "next", "data", "err", "console", "log", "valid", "value", "invalid", "markFormGroupTouched", "evaluationData", "evaluateRendu", "response", "evaluation", "aiScores", "aiCommentaires", "alert", "navigate", "message", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "isFieldInvalid", "fieldName", "field", "dirty", "touched", "getFieldError", "errors", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "RendusService", "selectors", "decls", "vars", "consts", "template", "ProjectEvaluationComponent_Template", "rf", "ctx", "ProjectEvaluationComponent_div_4_Template", "ProjectEvaluationComponent_div_5_Template", "ProjectEvaluationComponent_div_6_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\admin\\projects\\project-evaluation\\project-evaluation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormB<PERSON>er, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RendusService } from '@app/services/rendus.service';\r\n\r\n@Component({\r\n  selector: 'app-project-evaluation',\r\n  templateUrl: './project-evaluation.component.html',\r\n  styleUrls: ['./project-evaluation.component.css']\r\n})\r\nexport class ProjectEvaluationComponent implements OnInit {\r\n  renduId: string = '';\r\n  rendu: any = null;\r\n  evaluationForm: FormGroup;\r\n  isLoading: boolean = true;\r\n  isSubmitting: boolean = false;\r\n  error: string = '';\r\n  successMessage: string = '';\r\n  evaluationMode: 'manual' | 'ai' = 'manual';\r\n  aiProcessing: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private rendusService: RendusService\r\n  ) {\r\n    this.evaluationForm = this.fb.group({\r\n      scores: this.fb.group({\r\n        structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\r\n        originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\r\n      }),\r\n      commentaires: ['', Validators.required],\r\n      utiliserIA: [false]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\r\n\r\n    // Récupérer le mode d'évaluation des query params\r\n    const mode = this.route.snapshot.queryParamMap.get('mode');\r\n    if (mode === 'ai' || mode === 'manual') {\r\n      this.evaluationMode = mode;\r\n      this.evaluationForm.patchValue({ utiliserIA: mode === 'ai' });\r\n      // Sauvegarder le mode dans localStorage pour les futures évaluations\r\n      localStorage.setItem('evaluationMode', mode);\r\n    } else {\r\n      // Récupérer le mode d'évaluation du localStorage\r\n      const storedMode = localStorage.getItem('evaluationMode');\r\n      if (storedMode === 'ai' || storedMode === 'manual') {\r\n        this.evaluationMode = storedMode;\r\n        this.evaluationForm.patchValue({ utiliserIA: storedMode === 'ai' });\r\n      }\r\n    }\r\n\r\n    if (this.renduId) {\r\n      this.loadRendu();\r\n    } else {\r\n      this.error = 'ID de rendu manquant';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  loadRendu(): void {\r\n    this.isLoading = true;\r\n    this.rendusService.getRenduById(this.renduId).subscribe({\r\n      next: (data: any) => {\r\n        this.rendu = data;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors du chargement du rendu';\r\n        this.isLoading = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleEvaluationMode(): void {\r\n    this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\r\n    this.evaluationForm.patchValue({ utiliserIA: this.evaluationMode === 'ai' });\r\n    localStorage.setItem('evaluationMode', this.evaluationMode);\r\n  }\r\n\r\n  onSubmit(): void {\r\n    console.log('Submit clicked, form valid:', this.evaluationForm.valid);\r\n    console.log('Form values:', this.evaluationForm.value);\r\n\r\n    if (this.evaluationMode === 'manual' && this.evaluationForm.invalid) {\r\n      console.log('Form is invalid, marking fields as touched');\r\n      this.markFormGroupTouched(this.evaluationForm);\r\n      this.error = 'Veuillez remplir tous les champs obligatoires.';\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    this.error = '';\r\n\r\n    // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\r\n    if (this.evaluationMode === 'ai') {\r\n      this.evaluationForm.patchValue({ utiliserIA: true });\r\n      this.aiProcessing = true;\r\n    }\r\n\r\n    const evaluationData = this.evaluationForm.value;\r\n    console.log('Sending evaluation data:', evaluationData);\r\n\r\n    this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\r\n      next: (response: any) => {\r\n        // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\r\n        if (this.evaluationMode === 'ai' && response.evaluation) {\r\n          const aiScores = response.evaluation.scores;\r\n          const aiCommentaires = response.evaluation.commentaires;\r\n\r\n          this.evaluationForm.patchValue({\r\n            scores: {\r\n              structure: aiScores.structure || 0,\r\n              pratiques: aiScores.pratiques || 0,\r\n              fonctionnalite: aiScores.fonctionnalite || 0,\r\n              originalite: aiScores.originalite || 0\r\n            },\r\n            commentaires: aiCommentaires || 'Évaluation générée par IA'\r\n          });\r\n\r\n          this.aiProcessing = false;\r\n          this.isSubmitting = false;\r\n\r\n          // Afficher un message de succès\r\n          this.error = '';\r\n          alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\r\n        } else {\r\n          // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\r\n          this.isSubmitting = false;\r\n          alert('Évaluation soumise avec succès!');\r\n          this.router.navigate(['/admin/projects/list-rendus']);\r\n        }\r\n      },\r\n      error: (err: any) => {\r\n        this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\r\n        this.isSubmitting = false;\r\n        this.aiProcessing = false;\r\n        console.error(err);\r\n      }\r\n    });\r\n  }\r\n\r\n  getScoreTotal(): number {\r\n    const scores = this.evaluationForm.get('scores')?.value;\r\n    if (!scores) return 0;\r\n\r\n    return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\r\n  }\r\n\r\n  getScoreMaximum(): number {\r\n    return 20; // 4 critères x 5 points maximum\r\n  }\r\n\r\n  annuler(): void {\r\n    this.router.navigate(['/admin/projects/list-rendus']);\r\n  }\r\n\r\n  markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.evaluationForm.get(fieldName);\r\n    if (field && field.errors && (field.dirty || field.touched)) {\r\n      if (field.errors['required']) {\r\n        return 'Ce champ est obligatoire';\r\n      }\r\n      if (field.errors['min']) {\r\n        return `La valeur minimum est ${field.errors['min'].min}`;\r\n      }\r\n      if (field.errors['max']) {\r\n        return `La valeur maximum est ${field.errors['max'].max}`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n", "<div class=\"container mx-auto px-4 py-6\">\r\n  <div class=\"max-w-4xl mx-auto bg-white rounded-lg shadow p-6\">\r\n    <h1 class=\"text-2xl font-bold text-gray-800 mb-6\">Évaluation du projet</h1>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-8\">\r\n      <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n    </div>\r\n\r\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n      {{ error }}\r\n    </div>\r\n\r\n    <div *ngIf=\"rendu && !isLoading\">\r\n      <div class=\"mb-6 p-4 bg-gray-50 rounded-lg\">\r\n        <h2 class=\"text-xl font-semibold mb-4\">Informations sur le rendu</h2>\r\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <p><span class=\"font-medium\">Projet:</span> {{ rendu.projet.titre }}</p>\r\n            <p><span class=\"font-medium\">Étudiant:</span> {{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>\r\n          </div>\r\n          <div>\r\n            <p><span class=\"font-medium\">Date de soumission:</span> {{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>\r\n            <p><span class=\"font-medium\">Description:</span> {{ rendu.description || 'Aucune description' }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"rendu.fichiers && rendu.fichiers.length > 0\" class=\"mt-4\">\r\n          <h3 class=\"font-medium mb-2\">Fichiers joints:</h3>\r\n          <ul class=\"list-disc pl-5\">\r\n            <li *ngFor=\"let fichier of rendu.fichiers\">\r\n              <a [href]=\"'http://localhost:3000/' + fichier\" target=\"_blank\" class=\"text-blue-600 hover:underline\">\r\n                {{ fichier.split('/').pop() }}\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"mb-6\">\r\n        <div class=\"flex items-center justify-between mb-4\">\r\n          <h2 class=\"text-xl font-semibold\">Mode d'évaluation</h2>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"mr-2\">{{ evaluationMode === 'manual' ? 'Manuel' : 'IA' }}</span>\r\n            <button\r\n              (click)=\"toggleEvaluationMode()\"\r\n              class=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\r\n            >\r\n              Changer de mode\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <form [formGroup]=\"evaluationForm\" (ngSubmit)=\"onSubmit()\" *ngIf=\"evaluationMode === 'manual'\">\r\n          <div formGroupName=\"scores\" class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Structure du code (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"structure\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Bonnes pratiques (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"pratiques\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Fonctionnalité (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"fonctionnalite\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label class=\"block text-gray-700 mb-2\">Originalité (0-5)</label>\r\n              <input\r\n                type=\"number\"\r\n                formControlName=\"originalite\"\r\n                min=\"0\"\r\n                max=\"5\"\r\n                class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-group mb-6\">\r\n            <label class=\"block text-gray-700 mb-2\">Commentaires</label>\r\n            <textarea\r\n              formControlName=\"commentaires\"\r\n              rows=\"5\"\r\n              class=\"w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            ></textarea>\r\n          </div>\r\n\r\n          <div class=\"mb-4 p-3 bg-blue-50 rounded\">\r\n            <p><strong>Score total: {{ getScoreTotal() }}/{{ getScoreMaximum() }}</strong></p>\r\n          </div>\r\n\r\n          <div class=\"flex justify-between\">\r\n            <button\r\n              type=\"button\"\r\n              (click)=\"annuler()\"\r\n              class=\"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors\"\r\n            >\r\n              Annuler\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              [disabled]=\"isSubmitting\"\r\n              class=\"px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50\"\r\n            >\r\n              <span *ngIf=\"!isSubmitting\">Soumettre l'évaluation</span>\r\n              <span *ngIf=\"isSubmitting\">Soumission en cours...</span>\r\n            </button>\r\n          </div>\r\n        </form>\r\n\r\n        <div *ngIf=\"evaluationMode === 'ai'\" class=\"bg-gray-50 p-4 rounded-lg\">\r\n          <div *ngIf=\"!aiProcessing\">\r\n            <p class=\"mb-4\">L'évaluation sera réalisée automatiquement par notre système d'IA (Mistral 7B).</p>\r\n            <p class=\"mb-6\">L'IA analysera le code soumis et fournira une évaluation basée sur les critères standards.</p>\r\n\r\n            <div class=\"flex justify-between\">\r\n              <button\r\n                type=\"button\"\r\n                (click)=\"annuler()\"\r\n                class=\"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors\"\r\n              >\r\n                Annuler\r\n              </button>\r\n              <button\r\n                (click)=\"onSubmit()\"\r\n                [disabled]=\"isSubmitting\"\r\n                class=\"px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50\"\r\n              >\r\n                <span *ngIf=\"!isSubmitting\">Lancer l'évaluation IA</span>\r\n                <span *ngIf=\"isSubmitting\">Lancement en cours...</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div *ngIf=\"aiProcessing\" class=\"text-center py-8\">\r\n            <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"></div>\r\n            <p class=\"text-gray-700\">L'IA analyse le projet...</p>\r\n            <p class=\"text-sm text-gray-500 mt-2\">Cela peut prendre quelques instants</p>\r\n          </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n</div>\r\n"], "mappings": "AACA,SAAsBA,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;;;ICG/DC,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,SAAA,aAA6F;IAC/FF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAAgG;IAC9FD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAmBQR,EAAA,CAAAC,cAAA,SAA2C;IAEvCD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IAFDH,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAS,UAAA,oCAAAC,UAAA,EAAAV,EAAA,CAAAW,aAAA,CAA2C;IAC5CX,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAI,UAAA,CAAAE,KAAA,MAAAC,GAAA,QACF;;;;;IANNb,EAAA,CAAAC,cAAA,cAAsE;IACvCD,EAAA,CAAAI,MAAA,uBAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,aAA2B;IACzBD,EAAA,CAAAc,UAAA,IAAAC,qDAAA,iBAIK;IACPf,EAAA,CAAAG,YAAA,EAAK;;;;IALqBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAS,UAAA,YAAAO,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAiB;;;;;IA6FvClB,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAI,MAAA,kCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAvE9DH,EAAA,CAAAC,cAAA,eAA+F;IAA5DD,EAAA,CAAAmB,UAAA,sBAAAC,2EAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAYxB,EAAA,CAAAyB,WAAA,CAAAF,OAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxD1B,EAAA,CAAAC,cAAA,cAA+E;IAEnCD,EAAA,CAAAI,MAAA,8BAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACvEH,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwB;IACkBD,EAAA,CAAAI,MAAA,6BAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACtEH,EAAA,CAAAE,SAAA,gBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAI,MAAA,iCAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACpEH,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACkBD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAE,SAAA,iBAMC;IACHF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAA6B;IACaD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAE,SAAA,oBAIY;IACdF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAyC;IAC5BD,EAAA,CAAAI,MAAA,IAA0D;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAGhFH,EAAA,CAAAC,cAAA,eAAkC;IAG9BD,EAAA,CAAAmB,UAAA,mBAAAQ,2EAAA;MAAA3B,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAA5B,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAG,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAGnB7B,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAAc,UAAA,KAAAgB,yDAAA,kBAAyD;IACzD9B,EAAA,CAAAc,UAAA,KAAAiB,yDAAA,kBAAwD;IAC1D/B,EAAA,CAAAG,YAAA,EAAS;;;;IAxEPH,EAAA,CAAAS,UAAA,cAAAuB,MAAA,CAAAC,cAAA,CAA4B;IAsDnBjC,EAAA,CAAAK,SAAA,IAA0D;IAA1DL,EAAA,CAAAkC,kBAAA,kBAAAF,MAAA,CAAAG,aAAA,SAAAH,MAAA,CAAAI,eAAA,OAA0D;IAanEpC,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAS,UAAA,aAAAuB,MAAA,CAAAK,YAAA,CAAyB;IAGlBrC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAuB,MAAA,CAAAK,YAAA,CAAmB;IACnBrC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAuB,MAAA,CAAAK,YAAA,CAAkB;;;;;IAuBvBrC,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAI,MAAA,kCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACzDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAI,MAAA,4BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlB7DH,EAAA,CAAAC,cAAA,UAA2B;IACTD,EAAA,CAAAI,MAAA,0GAA+E;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACnGH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAI,MAAA,gHAA0F;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAE9GH,EAAA,CAAAC,cAAA,cAAkC;IAG9BD,EAAA,CAAAmB,UAAA,mBAAAmB,+EAAA;MAAAtC,EAAA,CAAAqB,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAe,OAAA,CAAAX,OAAA,EAAS;IAAA,EAAC;IAGnB7B,EAAA,CAAAI,MAAA,gBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAIC;IAHCD,EAAA,CAAAmB,UAAA,mBAAAsB,+EAAA;MAAAzC,EAAA,CAAAqB,aAAA,CAAAkB,IAAA;MAAA,MAAAG,OAAA,GAAA1C,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAiB,OAAA,CAAAhB,QAAA,EAAU;IAAA,EAAC;IAIpB1B,EAAA,CAAAc,UAAA,IAAA6B,6DAAA,kBAAyD;IACzD3C,EAAA,CAAAc,UAAA,KAAA8B,8DAAA,kBAAuD;IACzD5C,EAAA,CAAAG,YAAA,EAAS;;;;IALPH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAS,UAAA,aAAAoC,OAAA,CAAAR,YAAA,CAAyB;IAGlBrC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAoC,OAAA,CAAAR,YAAA,CAAmB;IACnBrC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAoC,OAAA,CAAAR,YAAA,CAAkB;;;;;IAK/BrC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAA0G;IAC1GF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAI,MAAA,0CAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;IA3BjFH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAc,UAAA,IAAAgC,sDAAA,kBAqBM;IAEN9C,EAAA,CAAAc,UAAA,IAAAiC,sDAAA,kBAIM;IACV/C,EAAA,CAAAG,YAAA,EAAM;;;;IA5BIH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,UAAAuC,MAAA,CAAAC,YAAA,CAAmB;IAuBnBjD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,SAAAuC,MAAA,CAAAC,YAAA,CAAkB;;;;;;IA5I9BjD,EAAA,CAAAC,cAAA,UAAiC;IAEUD,EAAA,CAAAI,MAAA,gCAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,cAAmD;IAElBD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,GAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,sBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAExGH,EAAA,CAAAC,cAAA,WAAK;IAC0BD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAAoD;;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAChHH,EAAA,CAAAC,cAAA,SAAG;IAA0BD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAI,MAAA,IAA+C;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAIxGH,EAAA,CAAAc,UAAA,KAAAoC,gDAAA,kBASM;IACRlD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAkB;IAEoBD,EAAA,CAAAI,MAAA,8BAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,eAA+B;IACVD,EAAA,CAAAI,MAAA,IAAmD;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAmB,UAAA,mBAAAgC,mEAAA;MAAAnD,EAAA,CAAAqB,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAA4B,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCtD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAIbH,EAAA,CAAAc,UAAA,KAAAyC,iDAAA,oBA0EO;IAEPvD,EAAA,CAAAc,UAAA,KAAA0C,gDAAA,kBA6BI;IACRxD,EAAA,CAAAG,YAAA,EAAM;;;;IA7I8CH,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,kBAAA,MAAAmD,MAAA,CAAAxC,KAAA,CAAAyC,MAAA,CAAAC,KAAA,KAAwB;IACtB3D,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAkC,kBAAA,MAAAuB,MAAA,CAAAxC,KAAA,CAAA2C,QAAA,CAAAC,GAAA,OAAAJ,MAAA,CAAAxC,KAAA,CAAA2C,QAAA,CAAAE,MAAA,KAAoD;IAG1C9D,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAM,kBAAA,MAAAN,EAAA,CAAA+D,WAAA,QAAAN,MAAA,CAAAxC,KAAA,CAAA+C,cAAA,0BAAoD;IAC3DhE,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAM,kBAAA,MAAAmD,MAAA,CAAAxC,KAAA,CAAAgD,WAAA,6BAA+C;IAI9FjE,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAS,UAAA,SAAAgD,MAAA,CAAAxC,KAAA,CAAAC,QAAA,IAAAuC,MAAA,CAAAxC,KAAA,CAAAC,QAAA,CAAAgD,MAAA,KAAiD;IAgBhClE,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAmE,iBAAA,CAAAV,MAAA,CAAAW,cAAA,gCAAmD;IAUdpE,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAS,UAAA,SAAAgD,MAAA,CAAAW,cAAA,cAAiC;IA4EvFpE,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAS,UAAA,SAAAgD,MAAA,CAAAW,cAAA,UAA6B;;;ADtH3C,OAAM,MAAOC,0BAA0B;EAWrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAdvB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAA1D,KAAK,GAAQ,IAAI;IAEjB,KAAA2D,SAAS,GAAY,IAAI;IACzB,KAAAvC,YAAY,GAAY,KAAK;IAC7B,KAAA7B,KAAK,GAAW,EAAE;IAClB,KAAAqE,cAAc,GAAW,EAAE;IAC3B,KAAAT,cAAc,GAAoB,QAAQ;IAC1C,KAAAnB,YAAY,GAAY,KAAK;IAQ3B,IAAI,CAAChB,cAAc,GAAG,IAAI,CAACsC,EAAE,CAACO,KAAK,CAAC;MAClCC,MAAM,EAAE,IAAI,CAACR,EAAE,CAACO,KAAK,CAAC;QACpBE,SAAS,EAAE,CAAC,CAAC,EAAE,CAACjF,UAAU,CAACkF,QAAQ,EAAElF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EC,SAAS,EAAE,CAAC,CAAC,EAAE,CAACrF,UAAU,CAACkF,QAAQ,EAAElF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3EE,cAAc,EAAE,CAAC,CAAC,EAAE,CAACtF,UAAU,CAACkF,QAAQ,EAAElF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChFG,WAAW,EAAE,CAAC,CAAC,EAAE,CAACvF,UAAU,CAACkF,QAAQ,EAAElF,UAAU,CAACmF,GAAG,CAAC,CAAC,CAAC,EAAEnF,UAAU,CAACoF,GAAG,CAAC,CAAC,CAAC,CAAC;OAC7E,CAAC;MACFI,YAAY,EAAE,CAAC,EAAE,EAAExF,UAAU,CAACkF,QAAQ,CAAC;MACvCO,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACd,OAAO,GAAG,IAAI,CAACH,KAAK,CAACkB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE;IACA,MAAMC,IAAI,GAAG,IAAI,CAACrB,KAAK,CAACkB,QAAQ,CAACI,aAAa,CAACF,GAAG,CAAC,MAAM,CAAC;IAC1D,IAAIC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI,CAACzB,cAAc,GAAGyB,IAAI;MAC1B,IAAI,CAAC5D,cAAc,CAAC8D,UAAU,CAAC;QAAEP,UAAU,EAAEK,IAAI,KAAK;MAAI,CAAE,CAAC;MAC7D;MACAG,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;KAC7C,MAAM;MACL;MACA,MAAMK,UAAU,GAAGF,YAAY,CAACG,OAAO,CAAC,gBAAgB,CAAC;MACzD,IAAID,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,QAAQ,EAAE;QAClD,IAAI,CAAC9B,cAAc,GAAG8B,UAAU;QAChC,IAAI,CAACjE,cAAc,CAAC8D,UAAU,CAAC;UAAEP,UAAU,EAAEU,UAAU,KAAK;QAAI,CAAE,CAAC;;;IAIvE,IAAI,IAAI,CAACvB,OAAO,EAAE;MAChB,IAAI,CAACyB,SAAS,EAAE;KACjB,MAAM;MACL,IAAI,CAAC5F,KAAK,GAAG,sBAAsB;MACnC,IAAI,CAACoE,SAAS,GAAG,KAAK;;EAE1B;EAEAwB,SAASA,CAAA;IACP,IAAI,CAACxB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,aAAa,CAAC2B,YAAY,CAAC,IAAI,CAAC1B,OAAO,CAAC,CAAC2B,SAAS,CAAC;MACtDC,IAAI,EAAGC,IAAS,IAAI;QAClB,IAAI,CAACvF,KAAK,GAAGuF,IAAI;QACjB,IAAI,CAAC5B,SAAS,GAAG,KAAK;MACxB,CAAC;MACDpE,KAAK,EAAGiG,GAAQ,IAAI;QAClB,IAAI,CAACjG,KAAK,GAAG,oCAAoC;QACjD,IAAI,CAACoE,SAAS,GAAG,KAAK;QACtB8B,OAAO,CAAClG,KAAK,CAACiG,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAnD,oBAAoBA,CAAA;IAClB,IAAI,CAACc,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,QAAQ,GAAG,IAAI,GAAG,QAAQ;IACxE,IAAI,CAACnC,cAAc,CAAC8D,UAAU,CAAC;MAAEP,UAAU,EAAE,IAAI,CAACpB,cAAc,KAAK;IAAI,CAAE,CAAC;IAC5E4B,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC7B,cAAc,CAAC;EAC7D;EAEA1C,QAAQA,CAAA;IACNgF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC1E,cAAc,CAAC2E,KAAK,CAAC;IACrEF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC1E,cAAc,CAAC4E,KAAK,CAAC;IAEtD,IAAI,IAAI,CAACzC,cAAc,KAAK,QAAQ,IAAI,IAAI,CAACnC,cAAc,CAAC6E,OAAO,EAAE;MACnEJ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAAC9E,cAAc,CAAC;MAC9C,IAAI,CAACzB,KAAK,GAAG,gDAAgD;MAC7D;;IAGF,IAAI,CAAC6B,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC7B,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAAC4D,cAAc,KAAK,IAAI,EAAE;MAChC,IAAI,CAACnC,cAAc,CAAC8D,UAAU,CAAC;QAAEP,UAAU,EAAE;MAAI,CAAE,CAAC;MACpD,IAAI,CAACvC,YAAY,GAAG,IAAI;;IAG1B,MAAM+D,cAAc,GAAG,IAAI,CAAC/E,cAAc,CAAC4E,KAAK;IAChDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,cAAc,CAAC;IAEvD,IAAI,CAACtC,aAAa,CAACuC,aAAa,CAAC,IAAI,CAACtC,OAAO,EAAEqC,cAAc,CAAC,CAACV,SAAS,CAAC;MACvEC,IAAI,EAAGW,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC9C,cAAc,KAAK,IAAI,IAAI8C,QAAQ,CAACC,UAAU,EAAE;UACvD,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,UAAU,CAACpC,MAAM;UAC3C,MAAMsC,cAAc,GAAGH,QAAQ,CAACC,UAAU,CAAC5B,YAAY;UAEvD,IAAI,CAACtD,cAAc,CAAC8D,UAAU,CAAC;YAC7BhB,MAAM,EAAE;cACNC,SAAS,EAAEoC,QAAQ,CAACpC,SAAS,IAAI,CAAC;cAClCI,SAAS,EAAEgC,QAAQ,CAAChC,SAAS,IAAI,CAAC;cAClCC,cAAc,EAAE+B,QAAQ,CAAC/B,cAAc,IAAI,CAAC;cAC5CC,WAAW,EAAE8B,QAAQ,CAAC9B,WAAW,IAAI;aACtC;YACDC,YAAY,EAAE8B,cAAc,IAAI;WACjC,CAAC;UAEF,IAAI,CAACpE,YAAY,GAAG,KAAK;UACzB,IAAI,CAACZ,YAAY,GAAG,KAAK;UAEzB;UACA,IAAI,CAAC7B,KAAK,GAAG,EAAE;UACf8G,KAAK,CAAC,mFAAmF,CAAC;SAC3F,MAAM;UACL;UACA,IAAI,CAACjF,YAAY,GAAG,KAAK;UACzBiF,KAAK,CAAC,iCAAiC,CAAC;UACxC,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;;MAEzD,CAAC;MACD/G,KAAK,EAAGiG,GAAQ,IAAI;QAClB,IAAI,CAACjG,KAAK,GAAG,yCAAyC,IAAIiG,GAAG,CAACjG,KAAK,EAAEgH,OAAO,IAAIf,GAAG,CAACe,OAAO,IAAI,iBAAiB,CAAC;QACjH,IAAI,CAACnF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACY,YAAY,GAAG,KAAK;QACzByD,OAAO,CAAClG,KAAK,CAACiG,GAAG,CAAC;MACpB;KACD,CAAC;EACJ;EAEAtE,aAAaA,CAAA;IACX,MAAM4C,MAAM,GAAG,IAAI,CAAC9C,cAAc,CAAC2D,GAAG,CAAC,QAAQ,CAAC,EAAEiB,KAAK;IACvD,IAAI,CAAC9B,MAAM,EAAE,OAAO,CAAC;IAErB,OAAOA,MAAM,CAACC,SAAS,GAAGD,MAAM,CAACK,SAAS,GAAGL,MAAM,CAACM,cAAc,GAAGN,MAAM,CAACO,WAAW;EACzF;EAEAlD,eAAeA,CAAA;IACb,OAAO,EAAE,CAAC,CAAC;EACb;;EAEAP,OAAOA,CAAA;IACL,IAAI,CAAC4C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,6BAA6B,CAAC,CAAC;EACvD;EAEAR,oBAAoBA,CAACU,SAAoB;IACvCC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAAC7B,GAAG,CAACkC,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;MAExB,IAAID,OAAO,YAAYjI,SAAS,EAAE;QAChC,IAAI,CAACiH,oBAAoB,CAACgB,OAAO,CAAC;;IAEtC,CAAC,CAAC;EACJ;EAEAE,cAAcA,CAACC,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAAClG,cAAc,CAAC2D,GAAG,CAACsC,SAAS,CAAC;IAChD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACrB,OAAO,KAAKqB,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACrE;EAEAC,aAAaA,CAACJ,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAClG,cAAc,CAAC2D,GAAG,CAACsC,SAAS,CAAC;IAChD,IAAIC,KAAK,IAAIA,KAAK,CAACI,MAAM,KAAKJ,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,EAAE;MAC3D,IAAIF,KAAK,CAACI,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,0BAA0B;;MAEnC,IAAIJ,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBJ,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,CAACrD,GAAG,EAAE;;MAE3D,IAAIiD,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,yBAAyBJ,KAAK,CAACI,MAAM,CAAC,KAAK,CAAC,CAACpD,GAAG,EAAE;;;IAG7D,OAAO,EAAE;EACX;;;uBAxLWd,0BAA0B,EAAArE,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAA7I,EAAA,CAAAwI,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1B1E,0BAA0B;MAAA2E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVvCtJ,EAAA,CAAAC,cAAA,aAAyC;UAEaD,EAAA,CAAAI,MAAA,gCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAE3EH,EAAA,CAAAc,UAAA,IAAA0I,yCAAA,iBAEM;UAENxJ,EAAA,CAAAc,UAAA,IAAA2I,yCAAA,iBAEM;UAENzJ,EAAA,CAAAc,UAAA,IAAA4I,yCAAA,mBAmJI;UACR1J,EAAA,CAAAG,YAAA,EAAM;;;UA5JIH,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAA3E,SAAA,CAAe;UAIf5E,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAA/I,KAAA,CAAW;UAIXR,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAAtI,KAAA,KAAAsI,GAAA,CAAA3E,SAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}