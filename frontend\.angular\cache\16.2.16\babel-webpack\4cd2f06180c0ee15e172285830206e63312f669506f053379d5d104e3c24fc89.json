{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/projets.service\";\nimport * as i4 from \"src/app/services/rendus.service\";\nimport * as i5 from \"src/app/services/authuser.service\";\nimport * as i6 from \"@angular/common\";\nfunction ProjectSubmissionComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 23)(3, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La description est requise et doit contenir au moins 10 caract\\u00E8res.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Veuillez s\\u00E9lectionner au moins un fichier.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_52_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"div\", 75)(2, \"div\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 45);\n    i0.ɵɵelement(4, \"path\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\")(6, \"p\", 112);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 113);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function ProjectSubmissionComponent_div_28_div_52_div_7_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const i_r13 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.removeFile(i_r13));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 8);\n    i0.ɵɵelement(12, \"path\", 115);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(file_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.getFileSize(file_r12.size));\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 91)(2, \"p\", 95);\n    i0.ɵɵtext(3, \"Fichiers s\\u00E9lectionn\\u00E9s :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 109);\n    i0.ɵɵtemplate(7, ProjectSubmissionComponent_div_28_div_52_div_7_Template, 13, 2, \"div\", 110);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedFiles.length, \" fichier\", ctx_r4.selectedFiles.length > 1 ? \"s\" : \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedFiles);\n  }\n}\nfunction ProjectSubmissionComponent_div_28__svg_svg_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 63);\n    i0.ɵɵelement(1, \"path\", 16);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 116);\n  }\n}\nfunction ProjectSubmissionComponent_div_28_span_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre le projet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_span_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Compl\\u00E9tez le formulaire pour soumettre\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Pr\\u00EAt \\u00E0 soumettre\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/detail\", a1];\n};\nfunction ProjectSubmissionComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 30);\n    i0.ɵɵelement(6, \"path\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 32);\n    i0.ɵɵtext(8, \"Description de votre travail\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"form\", 33);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectSubmissionComponent_div_28_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onSubmit());\n    });\n    i0.ɵɵelementStart(10, \"div\", 34)(11, \"label\", 35);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 36);\n    i0.ɵɵelement(13, \"path\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Rapport de projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 38);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 22);\n    i0.ɵɵelement(19, \"textarea\", 39);\n    i0.ɵɵelementStart(20, \"div\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 41);\n    i0.ɵɵelement(22, \"path\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"div\", 42)(24, \"span\");\n    i0.ɵɵtext(25, \"Minimum 10 caract\\u00E8res requis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, ProjectSubmissionComponent_div_28_div_28_Template, 5, 0, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 34)(30, \"label\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(31, \"svg\", 45);\n    i0.ɵɵelement(32, \"path\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34, \"Fichiers du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 38);\n    i0.ɵɵtext(36, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 22)(38, \"input\", 46);\n    i0.ɵɵlistener(\"change\", function ProjectSubmissionComponent_div_28_Template_input_change_38_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onFileChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 47)(40, \"div\", 48)(41, \"div\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(42, \"svg\", 50);\n    i0.ɵɵelement(43, \"path\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(44, \"div\")(45, \"p\", 51);\n    i0.ɵɵtext(46, \"Glissez vos fichiers ici\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\", 52);\n    i0.ɵɵtext(48, \"ou cliquez pour parcourir\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"p\", 53);\n    i0.ɵɵtext(50, \"Tous types de fichiers accept\\u00E9s\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(51, ProjectSubmissionComponent_div_28_div_51_Template, 5, 0, \"div\", 43);\n    i0.ɵɵtemplate(52, ProjectSubmissionComponent_div_28_div_52_Template, 8, 3, \"div\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 27)(54, \"div\", 28)(55, \"div\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(56, \"svg\", 56);\n    i0.ɵɵelement(57, \"path\", 57)(58, \"path\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(59, \"h3\", 32);\n    i0.ɵɵtext(60, \"Actions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 59)(62, \"div\", 60)(63, \"a\", 61)(64, \"div\", 62);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(65, \"svg\", 63);\n    i0.ɵɵelement(66, \"path\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(67, \"span\");\n    i0.ɵɵtext(68, \"Retour aux d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(69, \"button\", 65)(70, \"div\", 62);\n    i0.ɵɵtemplate(71, ProjectSubmissionComponent_div_28__svg_svg_71_Template, 2, 0, \"svg\", 66);\n    i0.ɵɵtemplate(72, ProjectSubmissionComponent_div_28_div_72_Template, 1, 0, \"div\", 67);\n    i0.ɵɵtemplate(73, ProjectSubmissionComponent_div_28_span_73_Template, 2, 0, \"span\", 68);\n    i0.ɵɵtemplate(74, ProjectSubmissionComponent_div_28_span_74_Template, 2, 0, \"span\", 68);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(75, \"div\", 69);\n    i0.ɵɵtemplate(76, ProjectSubmissionComponent_div_28_div_76_Template, 5, 0, \"div\", 70);\n    i0.ɵɵtemplate(77, ProjectSubmissionComponent_div_28_div_77_Template, 5, 0, \"div\", 71);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(78, \"div\", 72)(79, \"div\", 73)(80, \"div\", 74)(81, \"div\", 75)(82, \"div\", 76);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(83, \"svg\", 77);\n    i0.ɵɵelement(84, \"path\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(85, \"div\")(86, \"h3\", 79);\n    i0.ɵɵtext(87, \"Informations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"p\", 80);\n    i0.ɵɵtext(89, \"D\\u00E9tails du projet\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(90, \"div\", 81)(91, \"div\", 82)(92, \"div\", 83)(93, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(94, \"svg\", 85);\n    i0.ɵɵelement(95, \"path\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(96, \"div\", 87)(97, \"p\", 88);\n    i0.ɵɵtext(98, \"Titre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"p\", 89);\n    i0.ɵɵtext(100);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(101, \"div\", 82)(102, \"div\", 83)(103, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(104, \"svg\", 36);\n    i0.ɵɵelement(105, \"path\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(106, \"div\", 87)(107, \"p\", 88);\n    i0.ɵɵtext(108, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"p\", 90);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(111, \"div\", 82)(112, \"div\", 91)(113, \"div\", 75)(114, \"div\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(115, \"svg\", 93);\n    i0.ɵɵelement(116, \"path\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(117, \"div\")(118, \"p\", 88);\n    i0.ɵɵtext(119, \"Date limite\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"p\", 95);\n    i0.ɵɵtext(121);\n    i0.ɵɵpipe(122, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(123, \"div\", 96)(124, \"p\", 53);\n    i0.ɵɵtext(125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"p\", 97);\n    i0.ɵɵtext(127, \"restants\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(128, \"div\", 82)(129, \"div\", 75)(130, \"div\", 98);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(131, \"svg\", 99);\n    i0.ɵɵelement(132, \"path\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(133, \"div\")(134, \"p\", 88);\n    i0.ɵɵtext(135, \"Groupe cible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(136, \"p\", 95);\n    i0.ɵɵtext(137);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(138, \"div\", 27)(139, \"div\", 28)(140, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(141, \"svg\", 30);\n    i0.ɵɵelement(142, \"path\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(143, \"h3\", 32);\n    i0.ɵɵtext(144, \"Conseils\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(145, \"div\", 101)(146, \"div\", 102);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(147, \"svg\", 103);\n    i0.ɵɵelement(148, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(149, \"span\");\n    i0.ɵɵtext(150, \"D\\u00E9crivez clairement votre travail et les technologies utilis\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(151, \"div\", 102);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(152, \"svg\", 103);\n    i0.ɵɵelement(153, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(154, \"span\");\n    i0.ɵɵtext(155, \"Incluez tous les fichiers sources et la documentation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(156, \"div\", 102);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(157, \"svg\", 103);\n    i0.ɵɵelement(158, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(159, \"span\");\n    i0.ɵɵtext(160, \"Mentionnez les difficult\\u00E9s rencontr\\u00E9es et solutions apport\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(161, \"div\", 102);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(162, \"svg\", 105);\n    i0.ɵɵelement(163, \"path\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(164, \"span\");\n    i0.ɵɵtext(165, \"V\\u00E9rifiez que tous vos fichiers sont bien s\\u00E9lectionn\\u00E9s\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.submissionForm);\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_1_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_1_0.value == null ? null : tmp_1_0.value.length) || 0, \" caract\\u00E8res\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length === 0 && ctx_r1.submissionForm.touched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length > 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.projetId));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.submissionForm.invalid || ctx_r1.selectedFiles.length === 0 || ctx_r1.isSubmitting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submissionForm.invalid || ctx_r1.selectedFiles.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submissionForm.valid && ctx_r1.selectedFiles.length > 0);\n    i0.ɵɵadvance(23);\n    i0.ɵɵtextInterpolate(ctx_r1.projet.titre);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r1.projet.description || \"Aucune description\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(122, 18, ctx_r1.projet.dateLimite, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getRemainingDays(), \" jours\");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r1.projet.groupe || \"Tous les groupes\");\n  }\n}\n// Composant pour soumettre un projet\nexport class ProjectSubmissionComponent {\n  constructor(fb, route, router, projetService, rendusService, authService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.projetService = projetService;\n    this.rendusService = rendusService;\n    this.authService = authService;\n    this.projetId = '';\n    this.selectedFiles = [];\n    this.isLoading = true;\n    this.isSubmitting = false;\n    this.submissionForm = this.fb.group({\n      description: ['', [Validators.required, Validators.minLength(10)]]\n    });\n  }\n  ngOnInit() {\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\n    this.loadProjetDetails();\n  }\n  loadProjetDetails() {\n    this.isLoading = true;\n    this.projetService.getProjetById(this.projetId).subscribe({\n      next: projet => {\n        this.projet = projet;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Erreur lors du chargement du projet', err);\n        this.isLoading = false;\n        this.router.navigate(['/projects']);\n      }\n    });\n  }\n  onFileChange(event) {\n    const input = event.target;\n    if (input.files) {\n      this.selectedFiles = Array.from(input.files);\n    }\n  }\n  onSubmit() {\n    if (this.submissionForm.invalid || this.selectedFiles.length === 0) {\n      return;\n    }\n    this.isSubmitting = true;\n    const formData = new FormData();\n    formData.append('projet', this.projetId);\n    formData.append('etudiant', this.authService.getCurrentUserId() || '');\n    formData.append('description', this.submissionForm.value.description);\n    this.selectedFiles.forEach(file => {\n      formData.append('fichiers', file);\n    });\n    this.rendusService.submitRendu(formData).subscribe({\n      next: response => {\n        alert('Votre projet a été soumis avec succès');\n        this.router.navigate(['/projects']);\n      },\n      error: err => {\n        console.error('Erreur lors de la soumission du projet', err);\n        alert('Une erreur est survenue lors de la soumission du projet');\n        this.isSubmitting = false;\n      }\n    });\n  }\n  // Méthode pour supprimer un fichier de la sélection\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n  }\n  // Méthode pour formater la taille des fichiers\n  getFileSize(bytes) {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n  // Méthode pour calculer les jours restants\n  getRemainingDays() {\n    if (!this.projet?.dateLimite) return 0;\n    const now = new Date();\n    const deadline = new Date(this.projet.dateLimite);\n    const diffTime = deadline.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  }\n  static {\n    this.ɵfac = function ProjectSubmissionComponent_Factory(t) {\n      return new (t || ProjectSubmissionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ProjetService), i0.ɵɵdirectiveInject(i4.RendusService), i0.ɵɵdirectiveInject(i5.AuthuserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectSubmissionComponent,\n      selectors: [[\"app-project-submission\"]],\n      decls: 29,\n      vars: 6,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\"], [\"routerLink\", \"/projects\", 1, \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"routerLink\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-green-500\", \"to-green-600\", \"dark:from-green-600\", \"dark:to-green-700\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-green-600\", \"to-green-700\", \"dark:from-green-400\", \"dark:to-green-500\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 lg:grid-cols-3 gap-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-blue-100\", \"dark:bg-blue-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-blue-600\", \"dark:text-blue-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [\"id\", \"submissionForm\", 1, \"space-y-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"space-y-2\"], [\"for\", \"description\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-blue-600\", \"dark:text-blue-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-red-500\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"6\", \"placeholder\", \"D\\u00E9crivez votre travail : fonctionnalit\\u00E9s impl\\u00E9ment\\u00E9es, technologies utilis\\u00E9es, difficult\\u00E9s rencontr\\u00E9es, solutions apport\\u00E9es...\", 1, \"w-full\", \"px-4\", \"py-3\", \"pl-12\", \"bg-white\", \"dark:bg-[#2a2a2a]\", \"border-2\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-4\", \"focus:ring-[#4f5fad]/10\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"duration-200\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"placeholder-[#6d6870]\", \"dark:placeholder-[#a0a0a0]\", \"resize-none\"], [1, \"absolute\", \"top-3\", \"left-0\", \"pl-3\", \"flex\", \"items-start\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-0.5\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"flex items-center space-x-2 text-red-500 text-sm\", 4, \"ngIf\"], [\"for\", \"fichiers\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-green-600\", \"dark:text-green-400\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"absolute\", \"inset-0\", \"w-full\", \"h-full\", \"opacity-0\", \"cursor-pointer\", \"z-10\", 3, \"change\"], [1, \"w-full\", \"px-6\", \"py-8\", \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"border-2\", \"border-dashed\", \"border-[#4f5fad]/30\", \"dark:border-[#6d78c9]/30\", \"rounded-xl\", \"hover:border-[#4f5fad]\", \"dark:hover:border-[#6d78c9]\", \"transition-all\", \"duration-200\", \"text-center\"], [1, \"space-y-3\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-3\", \"rounded-xl\", \"w-fit\", \"mx-auto\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [1, \"bg-green-100\", \"dark:bg-green-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-green-600\", \"dark:text-green-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"space-y-4\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [1, \"flex\", \"items-center\", \"justify-center\", \"px-6\", \"py-3\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", \"order-2\", \"sm:order-1\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [\"type\", \"submit\", \"form\", \"submissionForm\", 1, \"flex\", \"items-center\", \"justify-center\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-green-500\", \"to-green-600\", \"dark:from-green-600\", \"dark:to-green-700\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", \"disabled:hover:shadow-none\", \"order-1\", \"sm:order-2\", 3, \"disabled\"], [\"class\", \"w-5 h-5\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\"], [\"class\", \"flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400 text-sm\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center space-x-2 text-green-600 dark:text-green-400 text-sm\", 4, \"ngIf\"], [1, \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"p-6\", \"text-white\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"bg-white/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-lg\", \"font-semibold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"p-6\", \"space-y-4\"], [1, \"p-3\", \"bg-[#edf1f4]/50\", \"dark:bg-[#2a2a2a]/50\", \"rounded-xl\"], [1, \"flex\", \"items-start\", \"space-x-3\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"], [1, \"flex-1\"], [1, \"text-xs\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\"], [1, \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"leading-tight\"], [1, \"text-sm\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"leading-tight\", \"line-clamp-3\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-600\", \"dark:text-orange-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"text-right\"], [1, \"text-xs\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"bg-purple-100\", \"dark:bg-purple-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-purple-600\", \"dark:text-purple-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"space-y-3\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-start\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-green-600\", \"dark:text-green-400\", \"mt-0.5\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-600\", \"dark:text-orange-400\", \"mt-0.5\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-red-500\", \"text-sm\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-3\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800/30\", \"rounded-xl\"], [1, \"text-sm\", \"font-medium\", \"text-green-800\", \"dark:text-green-400\"], [1, \"text-xs\", \"text-green-600\", \"dark:text-green-500\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"w-5\", \"h-5\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"text-orange-600\", \"dark:text-orange-400\", \"text-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"text-green-600\", \"dark:text-green-400\", \"text-sm\"]],\n      template: function ProjectSubmissionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"nav\", 6)(7, \"a\", 7);\n          i0.ɵɵtext(8, \"Mes Projets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(9, \"svg\", 8);\n          i0.ɵɵelement(10, \"path\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(11, \"a\", 10);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(13, \"svg\", 8);\n          i0.ɵɵelement(14, \"path\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(15, \"span\", 11);\n          i0.ɵɵtext(16, \"Soumettre\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\", 13)(19, \"div\", 14);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(20, \"svg\", 15);\n          i0.ɵɵelement(21, \"path\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(22, \"div\")(23, \"h1\", 17);\n          i0.ɵɵtext(24, \" Soumettre mon projet \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 18);\n          i0.ɵɵtext(26, \" T\\u00E9l\\u00E9chargez vos fichiers et d\\u00E9crivez votre travail \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(27, ProjectSubmissionComponent_div_27_Template, 4, 0, \"div\", 19);\n          i0.ɵɵtemplate(28, ProjectSubmissionComponent_div_28_Template, 166, 23, \"div\", 20);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, ctx.projetId));\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"Projet\");\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.projet && !ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.DatePipe],\n      styles: [\"\\n\\n.submission-form[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.file-upload[_ngcontent-%COMP%] {\\n  border: 2px dashed #ccc;\\n  padding: 1.5rem;\\n  text-align: center;\\n  border-radius: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.file-upload[_ngcontent-%COMP%]:hover {\\n  border-color: #6366f1;\\n}\\n\\n.file-list[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem;\\n  background-color: #f9fafb;\\n  border-radius: 0.25rem;\\n  margin-bottom: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2plY3Qtc3VibWlzc2lvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHFEQUFxRDtBQUNyRDtFQUNFLGdCQUFnQjtFQUNoQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsdUJBQXVCO0VBQ3ZCLGVBQWU7RUFDZixrQkFBa0I7RUFDbEIscUJBQXFCO0VBQ3JCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsOEJBQThCO0VBQzlCLGVBQWU7RUFDZix5QkFBeUI7RUFDekIsc0JBQXNCO0VBQ3RCLHFCQUFxQjtBQUN2QiIsImZpbGUiOiJwcm9qZWN0LXN1Ym1pc3Npb24uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBkZSBzb3VtaXNzaW9uIGRlIHByb2pldCAqL1xyXG4uc3VibWlzc2lvbi1mb3JtIHtcclxuICBtYXgtd2lkdGg6IDgwMHB4O1xyXG4gIG1hcmdpbjogMCBhdXRvO1xyXG59XHJcblxyXG4uZm9ybS1zZWN0aW9uIHtcclxuICBtYXJnaW4tYm90dG9tOiAycmVtO1xyXG59XHJcblxyXG4uZmlsZS11cGxvYWQge1xyXG4gIGJvcmRlcjogMnB4IGRhc2hlZCAjY2NjO1xyXG4gIHBhZGRpbmc6IDEuNXJlbTtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgYm9yZGVyLXJhZGl1czogMC41cmVtO1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5maWxlLXVwbG9hZDpob3ZlciB7XHJcbiAgYm9yZGVyLWNvbG9yOiAjNjM2NmYxO1xyXG59XHJcblxyXG4uZmlsZS1saXN0IHtcclxuICBtYXJnaW4tdG9wOiAxcmVtO1xyXG59XHJcblxyXG4uZmlsZS1pdGVtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIHBhZGRpbmc6IDAuNXJlbTtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmYWZiO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcHJvamVjdHMvcHJvamVjdC1zdWJtaXNzaW9uL3Byb2plY3Qtc3VibWlzc2lvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHFEQUFxRDtBQUNyRDtFQUNFLGdCQUFnQjtFQUNoQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsdUJBQXVCO0VBQ3ZCLGVBQWU7RUFDZixrQkFBa0I7RUFDbEIscUJBQXFCO0VBQ3JCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsOEJBQThCO0VBQzlCLGVBQWU7RUFDZix5QkFBeUI7RUFDekIsc0JBQXNCO0VBQ3RCLHFCQUFxQjtBQUN2QjtBQUNBLGcvQ0FBZy9DIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bGVzIHBvdXIgbGUgY29tcG9zYW50IGRlIHNvdW1pc3Npb24gZGUgcHJvamV0ICovXHJcbi5zdWJtaXNzaW9uLWZvcm0ge1xyXG4gIG1heC13aWR0aDogODAwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbn1cclxuXHJcbi5mb3JtLXNlY3Rpb24ge1xyXG4gIG1hcmdpbi1ib3R0b206IDJyZW07XHJcbn1cclxuXHJcbi5maWxlLXVwbG9hZCB7XHJcbiAgYm9yZGVyOiAycHggZGFzaGVkICNjY2M7XHJcbiAgcGFkZGluZzogMS41cmVtO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XHJcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLmZpbGUtdXBsb2FkOmhvdmVyIHtcclxuICBib3JkZXItY29sb3I6ICM2MzY2ZjE7XHJcbn1cclxuXHJcbi5maWxlLWxpc3Qge1xyXG4gIG1hcmdpbi10b3A6IDFyZW07XHJcbn1cclxuXHJcbi5maWxlLWl0ZW0ge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgcGFkZGluZzogMC41cmVtO1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7XHJcbiAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "ProjectSubmissionComponent_div_28_div_52_div_7_Template_button_click_10_listener", "restoredCtx", "ɵɵrestoreView", "_r15", "i_r13", "index", "ctx_r14", "ɵɵnextContext", "ɵɵresetView", "removeFile", "ɵɵadvance", "ɵɵtextInterpolate", "file_r12", "name", "ctx_r11", "getFileSize", "size", "ɵɵtemplate", "ProjectSubmissionComponent_div_28_div_52_div_7_Template", "ɵɵtextInterpolate2", "ctx_r4", "selectedFiles", "length", "ɵɵproperty", "ProjectSubmissionComponent_div_28_Template_form_ngSubmit_9_listener", "_r17", "ctx_r16", "onSubmit", "ProjectSubmissionComponent_div_28_div_28_Template", "ProjectSubmissionComponent_div_28_Template_input_change_38_listener", "$event", "ctx_r18", "onFileChange", "ProjectSubmissionComponent_div_28_div_51_Template", "ProjectSubmissionComponent_div_28_div_52_Template", "ProjectSubmissionComponent_div_28__svg_svg_71_Template", "ProjectSubmissionComponent_div_28_div_72_Template", "ProjectSubmissionComponent_div_28_span_73_Template", "ProjectSubmissionComponent_div_28_span_74_Template", "ProjectSubmissionComponent_div_28_div_76_Template", "ProjectSubmissionComponent_div_28_div_77_Template", "ctx_r1", "submissionForm", "ɵɵtextInterpolate1", "tmp_1_0", "get", "value", "tmp_2_0", "invalid", "touched", "ɵɵpureFunction1", "_c0", "projetId", "isSubmitting", "valid", "projet", "titre", "description", "ɵɵpipeBind2", "dateLimite", "getRemainingDays", "groupe", "ProjectSubmissionComponent", "constructor", "fb", "route", "router", "projetService", "rendusService", "authService", "isLoading", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "snapshot", "paramMap", "loadProjetDetails", "getProjetById", "subscribe", "next", "error", "err", "console", "navigate", "event", "input", "target", "files", "Array", "from", "formData", "FormData", "append", "getCurrentUserId", "for<PERSON>ach", "file", "submitRendu", "response", "alert", "splice", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "now", "Date", "deadline", "diffTime", "getTime", "diffDays", "ceil", "max", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "ProjetService", "i4", "RendusService", "i5", "AuthuserService", "selectors", "decls", "vars", "consts", "template", "ProjectSubmissionComponent_Template", "rf", "ctx", "ProjectSubmissionComponent_div_27_Template", "ProjectSubmissionComponent_div_28_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\front\\projects\\project-submission\\project-submission.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\DevBridge\\frontend\\src\\app\\views\\front\\projects\\project-submission\\project-submission.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProjetService } from '@app/services/projets.service';\r\nimport { RendusService } from 'src/app/services/rendus.service';\r\nimport { AuthuserService } from 'src/app/services/authuser.service';\r\n\r\n// Composant pour soumettre un projet\r\n@Component({\r\n  selector: 'app-project-submission',\r\n  templateUrl: './project-submission.component.html',\r\n  styleUrls: ['./project-submission.component.css'],\r\n})\r\nexport class ProjectSubmissionComponent implements OnInit {\r\n  projetId: string = '';\r\n  projet: any;\r\n  submissionForm: FormGroup;\r\n  selectedFiles: File[] = [];\r\n  isLoading = true;\r\n  isSubmitting = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private projetService: ProjetService,\r\n    private rendusService: RendusService,\r\n    private authService: AuthuserService\r\n  ) {\r\n    this.submissionForm = this.fb.group({\r\n      description: ['', [Validators.required, Validators.minLength(10)]],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.projetId = this.route.snapshot.paramMap.get('id') || '';\r\n    this.loadProjetDetails();\r\n  }\r\n\r\n  loadProjetDetails(): void {\r\n    this.isLoading = true;\r\n    this.projetService.getProjetById(this.projetId).subscribe({\r\n      next: (projet: any) => {\r\n        this.projet = projet;\r\n        this.isLoading = false;\r\n      },\r\n      error: (err: Error) => {\r\n        console.error('Erreur lors du chargement du projet', err);\r\n        this.isLoading = false;\r\n        this.router.navigate(['/projects']);\r\n      },\r\n    });\r\n  }\r\n\r\n  onFileChange(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files) {\r\n      this.selectedFiles = Array.from(input.files);\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.submissionForm.invalid || this.selectedFiles.length === 0) {\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    const formData = new FormData();\r\n    formData.append('projet', this.projetId);\r\n    formData.append('etudiant', this.authService.getCurrentUserId() || '');\r\n    formData.append('description', this.submissionForm.value.description);\r\n\r\n    this.selectedFiles.forEach((file) => {\r\n      formData.append('fichiers', file);\r\n    });\r\n\r\n    this.rendusService.submitRendu(formData).subscribe({\r\n      next: (response: any) => {\r\n        alert('Votre projet a été soumis avec succès');\r\n        this.router.navigate(['/projects']);\r\n      },\r\n      error: (err: Error) => {\r\n        console.error('Erreur lors de la soumission du projet', err);\r\n        alert('Une erreur est survenue lors de la soumission du projet');\r\n        this.isSubmitting = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  // Méthode pour supprimer un fichier de la sélection\r\n  removeFile(index: number): void {\r\n    this.selectedFiles.splice(index, 1);\r\n  }\r\n\r\n  // Méthode pour formater la taille des fichiers\r\n  getFileSize(bytes: number): string {\r\n    if (bytes === 0) return '0 B';\r\n\r\n    const k = 1024;\r\n    const sizes = ['B', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  }\r\n\r\n  // Méthode pour calculer les jours restants\r\n  getRemainingDays(): number {\r\n    if (!this.projet?.dateLimite) return 0;\r\n\r\n    const now = new Date();\r\n    const deadline = new Date(this.projet.dateLimite);\r\n    const diffTime = deadline.getTime() - now.getTime();\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n\r\n    return Math.max(0, diffDays);\r\n  }\r\n}\r\n", "<div class=\"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-dark-bg-primary dark:via-dark-bg-secondary dark:to-dark-bg-tertiary relative\">\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\r\n    <div class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"></div>\r\n  </div>\r\n\r\n  <div class=\"container mx-auto px-4 py-8 relative z-10\">\r\n\r\n    <!-- Header moderne avec breadcrumb -->\r\n    <div class=\"mb-8\">\r\n      <nav class=\"flex items-center space-x-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-4\">\r\n        <a routerLink=\"/projects\" class=\"hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\">Mes Projets</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <a [routerLink]=\"['/projects/detail', projetId]\" class=\"hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors\">{{ projet?.titre || 'Projet' }}</a>\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n        <span class=\"text-[#4f5fad] dark:text-[#6d78c9] font-medium\">Soumettre</span>\r\n      </nav>\r\n\r\n      <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"h-16 w-16 rounded-2xl bg-gradient-to-br from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 flex items-center justify-center shadow-lg\">\r\n            <svg class=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\r\n            </svg>\r\n          </div>\r\n          <div>\r\n            <h1 class=\"text-3xl font-bold bg-gradient-to-r from-green-600 to-green-700 dark:from-green-400 dark:to-green-500 bg-clip-text text-transparent\">\r\n              Soumettre mon projet\r\n            </h1>\r\n            <p class=\"text-[#6d6870] dark:text-[#a0a0a0]\">\r\n              Téléchargez vos fichiers et décrivez votre travail\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Loading Indicator -->\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center my-12\">\r\n      <div class=\"relative\">\r\n        <div class=\"w-14 h-14 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin\"></div>\r\n        <!-- Glow effect -->\r\n        <div class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Contenu principal -->\r\n    <div *ngIf=\"projet && !isLoading\" class=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\r\n\r\n      <!-- Formulaire de soumission -->\r\n      <div class=\"lg:col-span-2 space-y-6\">\r\n\r\n        <!-- Description du travail -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Description de votre travail</h3>\r\n          </div>\r\n\r\n          <form id=\"submissionForm\" [formGroup]=\"submissionForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-6\">\r\n            <div class=\"space-y-2\">\r\n              <label for=\"description\" class=\"flex items-center space-x-2 text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">\r\n                <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                </svg>\r\n                <span>Rapport de projet</span>\r\n                <span class=\"text-red-500\">*</span>\r\n              </label>\r\n              <div class=\"relative\">\r\n                <textarea\r\n                  id=\"description\"\r\n                  formControlName=\"description\"\r\n                  rows=\"6\"\r\n                  placeholder=\"Décrivez votre travail : fonctionnalités implémentées, technologies utilisées, difficultés rencontrées, solutions apportées...\"\r\n                  class=\"w-full px-4 py-3 pl-12 bg-white dark:bg-[#2a2a2a] border-2 border-[#edf1f4] dark:border-[#2a2a2a] rounded-xl focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-4 focus:ring-[#4f5fad]/10 dark:focus:ring-[#6d78c9]/20 transition-all duration-200 text-[#3d4a85] dark:text-[#6d78c9] placeholder-[#6d6870] dark:placeholder-[#a0a0a0] resize-none\"\r\n                ></textarea>\r\n                <div class=\"absolute top-3 left-0 pl-3 flex items-start pointer-events-none\">\r\n                  <svg class=\"w-5 h-5 text-[#6d6870] dark:text-[#a0a0a0] mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <div class=\"flex justify-between text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\r\n                <span>Minimum 10 caractères requis</span>\r\n                <span>{{ submissionForm.get('description')?.value?.length || 0 }} caractères</span>\r\n              </div>\r\n              <div *ngIf=\"submissionForm.get('description')?.invalid && submissionForm.get('description')?.touched\"\r\n                   class=\"flex items-center space-x-2 text-red-500 text-sm\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n                <span>La description est requise et doit contenir au moins 10 caractères.</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Upload de fichiers -->\r\n            <div class=\"space-y-2\">\r\n              <label for=\"fichiers\" class=\"flex items-center space-x-2 text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">\r\n                <svg class=\"w-4 h-4 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\r\n                </svg>\r\n                <span>Fichiers du projet</span>\r\n                <span class=\"text-red-500\">*</span>\r\n              </label>\r\n              <div class=\"relative\">\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"fichiers\"\r\n                  multiple\r\n                  (change)=\"onFileChange($event)\"\r\n                  class=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10\"\r\n                />\r\n                <div class=\"w-full px-6 py-8 bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 border-2 border-dashed border-[#4f5fad]/30 dark:border-[#6d78c9]/30 rounded-xl hover:border-[#4f5fad] dark:hover:border-[#6d78c9] transition-all duration-200 text-center\">\r\n                  <div class=\"space-y-3\">\r\n                    <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-3 rounded-xl w-fit mx-auto\">\r\n                      <svg class=\"w-8 h-8 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p class=\"text-[#3d4a85] dark:text-[#6d78c9] font-medium\">Glissez vos fichiers ici</p>\r\n                      <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">ou cliquez pour parcourir</p>\r\n                    </div>\r\n                    <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">Tous types de fichiers acceptés</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Validation des fichiers -->\r\n              <div *ngIf=\"selectedFiles.length === 0 && submissionForm.touched\" class=\"flex items-center space-x-2 text-red-500 text-sm\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n                <span>Veuillez sélectionner au moins un fichier.</span>\r\n              </div>\r\n\r\n              <!-- Liste des fichiers sélectionnés -->\r\n              <div *ngIf=\"selectedFiles.length > 0\" class=\"space-y-3\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Fichiers sélectionnés :</p>\r\n                  <span class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">{{ selectedFiles.length }} fichier{{ selectedFiles.length > 1 ? 's' : '' }}</span>\r\n                </div>\r\n                <div class=\"grid grid-cols-1 gap-2\">\r\n                  <div *ngFor=\"let file of selectedFiles; let i = index\" class=\"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl\">\r\n                    <div class=\"flex items-center space-x-3\">\r\n                      <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\r\n                        <svg class=\"w-4 h-4 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p class=\"text-sm font-medium text-green-800 dark:text-green-400\">{{ file.name }}</p>\r\n                        <p class=\"text-xs text-green-600 dark:text-green-500\">{{ getFileSize(file.size) }}</p>\r\n                      </div>\r\n                    </div>\r\n                    <button type=\"button\" (click)=\"removeFile(i)\" class=\"text-red-500 hover:text-red-700 transition-colors\">\r\n                      <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n\r\n        <!-- Boutons d'action horizontaux -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-green-100 dark:bg-green-900/30 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"></path>\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Actions</h3>\r\n          </div>\r\n\r\n          <div class=\"space-y-4\">\r\n            <!-- Boutons d'action horizontaux -->\r\n            <div class=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n              <!-- Bouton retour -->\r\n              <a [routerLink]=\"['/projects/detail', projetId]\"\r\n                 class=\"flex items-center justify-center px-6 py-3 bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#3d4a85] dark:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-xl transition-all duration-200 font-medium order-2 sm:order-1\">\r\n                <div class=\"flex items-center space-x-2\">\r\n                  <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\r\n                  </svg>\r\n                  <span>Retour aux détails</span>\r\n                </div>\r\n              </a>\r\n\r\n              <!-- Bouton de soumission -->\r\n              <button\r\n                type=\"submit\"\r\n                form=\"submissionForm\"\r\n                [disabled]=\"submissionForm.invalid || selectedFiles.length === 0 || isSubmitting\"\r\n                class=\"flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none order-1 sm:order-2\"\r\n              >\r\n                <div class=\"flex items-center space-x-2\">\r\n                  <svg *ngIf=\"!isSubmitting\" class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\r\n                  </svg>\r\n                  <div *ngIf=\"isSubmitting\" class=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\r\n                  <span *ngIf=\"!isSubmitting\">Soumettre le projet</span>\r\n                  <span *ngIf=\"isSubmitting\">Soumission en cours...</span>\r\n                </div>\r\n              </button>\r\n            </div>\r\n\r\n            <!-- Indicateur de validation -->\r\n            <div class=\"text-center\">\r\n              <div *ngIf=\"submissionForm.invalid || selectedFiles.length === 0\" class=\"flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400 text-sm\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"></path>\r\n                </svg>\r\n                <span>Complétez le formulaire pour soumettre</span>\r\n              </div>\r\n              <div *ngIf=\"submissionForm.valid && selectedFiles.length > 0\" class=\"flex items-center justify-center space-x-2 text-green-600 dark:text-green-400 text-sm\">\r\n                <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n                <span>Prêt à soumettre</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <!-- Sidebar avec informations du projet -->\r\n      <div class=\"space-y-6\">\r\n\r\n        <!-- Informations du projet -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\">\r\n          <div class=\"bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] p-6 text-white\">\r\n            <div class=\"flex items-center space-x-3\">\r\n              <div class=\"bg-white/20 p-2 rounded-lg\">\r\n                <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                <h3 class=\"text-lg font-semibold\">Informations</h3>\r\n                <p class=\"text-sm text-white/80\">Détails du projet</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"p-6 space-y-4\">\r\n            <!-- Titre -->\r\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\r\n              <div class=\"flex items-start space-x-3\">\r\n                <div class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 p-2 rounded-lg\">\r\n                  <svg class=\"w-4 h-4 text-[#4f5fad] dark:text-[#6d78c9]\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Titre</p>\r\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9] leading-tight\">{{ projet.titre }}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Description -->\r\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\r\n              <div class=\"flex items-start space-x-3\">\r\n                <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\r\n                  <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h7\"></path>\r\n                  </svg>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Description</p>\r\n                  <p class=\"text-sm text-[#3d4a85] dark:text-[#6d78c9] leading-tight line-clamp-3\">{{ projet.description || 'Aucune description' }}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Date limite -->\r\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <div class=\"bg-orange-100 dark:bg-orange-900/30 p-2 rounded-lg\">\r\n                    <svg class=\"w-4 h-4 text-orange-600 dark:text-orange-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Date limite</p>\r\n                    <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet.dateLimite | date : \"dd/MM/yyyy\" }}</p>\r\n                  </div>\r\n                </div>\r\n                <div class=\"text-right\">\r\n                  <p class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">{{ getRemainingDays() }} jours</p>\r\n                  <p class=\"text-xs text-orange-600 dark:text-orange-400\">restants</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Groupe -->\r\n            <div class=\"p-3 bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 rounded-xl\">\r\n              <div class=\"flex items-center space-x-3\">\r\n                <div class=\"bg-purple-100 dark:bg-purple-900/30 p-2 rounded-lg\">\r\n                  <svg class=\"w-4 h-4 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                  </svg>\r\n                </div>\r\n                <div>\r\n                  <p class=\"text-xs font-medium text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider\">Groupe cible</p>\r\n                  <p class=\"text-sm font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">{{ projet.groupe || 'Tous les groupes' }}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Conseils de soumission -->\r\n        <div class=\"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-[#edf1f4]/50 dark:border-[#2a2a2a]\">\r\n          <div class=\"flex items-center space-x-3 mb-4\">\r\n            <div class=\"bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg\">\r\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"text-lg font-semibold text-[#3d4a85] dark:text-[#6d78c9]\">Conseils</h3>\r\n          </div>\r\n\r\n          <div class=\"space-y-3 text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\r\n            <div class=\"flex items-start space-x-2\">\r\n              <svg class=\"w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n              </svg>\r\n              <span>Décrivez clairement votre travail et les technologies utilisées</span>\r\n            </div>\r\n            <div class=\"flex items-start space-x-2\">\r\n              <svg class=\"w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n              </svg>\r\n              <span>Incluez tous les fichiers sources et la documentation</span>\r\n            </div>\r\n            <div class=\"flex items-start space-x-2\">\r\n              <svg class=\"w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n              </svg>\r\n              <span>Mentionnez les difficultés rencontrées et solutions apportées</span>\r\n            </div>\r\n            <div class=\"flex items-start space-x-2\">\r\n              <svg class=\"w-4 h-4 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"></path>\r\n              </svg>\r\n              <span>Vérifiez que tous vos fichiers sont bien sélectionnés</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;IC0C/DC,EAAA,CAAAC,cAAA,cAAyD;IAErDD,EAAA,CAAAE,SAAA,cAAwJ;IAG1JF,EAAA,CAAAG,YAAA,EAAM;;;;;IA+CEH,EAAA,CAAAC,cAAA,eAC8D;IAC5DD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAE,SAAA,gBAAmI;IACrIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,+EAAmE;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;IAsClFH,EAAA,CAAAC,cAAA,eAA2H;IACzHD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAE,SAAA,gBAAmI;IACrIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,sDAA0C;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;;IAUrDH,EAAA,CAAAC,cAAA,eAAkM;IAG5LD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,cAA8G;IAC5GD,EAAA,CAAAE,SAAA,eAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,UAAK;IAC+DD,EAAA,CAAAM,MAAA,GAAe;IAAAN,EAAA,CAAAG,YAAA,EAAI;IACrFH,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAM,MAAA,GAA4B;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAG1FH,EAAA,CAAAC,cAAA,mBAAwG;IAAlFD,EAAA,CAAAO,UAAA,mBAAAC,iFAAA;MAAA,MAAAC,WAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,IAAA;MAAA,MAAAC,KAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,UAAA,CAAAL,KAAA,CAAa;IAAA,EAAC;IAC3CZ,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAE,SAAA,iBAAsG;IACxGF,EAAA,CAAAG,YAAA,EAAM;;;;;IAP8DH,EAAA,CAAAkB,SAAA,GAAe;IAAflB,EAAA,CAAAmB,iBAAA,CAAAC,QAAA,CAAAC,IAAA,CAAe;IAC3BrB,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAAmB,iBAAA,CAAAG,OAAA,CAAAC,WAAA,CAAAH,QAAA,CAAAI,IAAA,EAA4B;;;;;IAf5FxB,EAAA,CAAAC,cAAA,cAAwD;IAEgBD,EAAA,CAAAM,MAAA,wCAAuB;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAC/FH,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAM,MAAA,GAA2E;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAE7IH,EAAA,CAAAC,cAAA,eAAoC;IAClCD,EAAA,CAAAyB,UAAA,IAAAC,uDAAA,oBAiBM;IACR1B,EAAA,CAAAG,YAAA,EAAM;;;;IArBqDH,EAAA,CAAAkB,SAAA,GAA2E;IAA3ElB,EAAA,CAAA2B,kBAAA,KAAAC,MAAA,CAAAC,aAAA,CAAAC,MAAA,cAAAF,MAAA,CAAAC,aAAA,CAAAC,MAAA,oBAA2E;IAG9G9B,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,UAAA,YAAAH,MAAA,CAAAC,aAAA,CAAkB;;;;;IA0DxC7B,EAAA,CAAAI,cAAA,EAAiG;IAAjGJ,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAE,SAAA,eAAuK;IACzKF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAE,SAAA,eAAkH;;;;;IAClHF,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAM,MAAA,0BAAmB;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;IACtDH,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAM,MAAA,6BAAsB;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;IAO5DH,EAAA,CAAAC,cAAA,eAAkK;IAChKD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA2N;IAC7NF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,kDAAsC;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;IAErDH,EAAA,CAAAC,cAAA,eAA4J;IAC1JD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,iCAAgB;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;;;;;IApLzCH,EAAA,CAAAC,cAAA,cAAgF;IAStED,EAAA,CAAAI,cAAA,EAA4G;IAA5GJ,EAAA,CAAAC,cAAA,cAA4G;IAC1GD,EAAA,CAAAE,SAAA,eAAwG;IAC1GF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAqE;IAArEL,EAAA,CAAAC,cAAA,aAAqE;IAAAD,EAAA,CAAAM,MAAA,mCAA4B;IAAAN,EAAA,CAAAG,YAAA,EAAK;IAGxGH,EAAA,CAAAC,cAAA,eAAiG;IAA1CD,EAAA,CAAAO,UAAA,sBAAAyB,oEAAA;MAAAhC,EAAA,CAAAU,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAe,aAAA;MAAA,OAAYf,EAAA,CAAAgB,WAAA,CAAAkB,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAC5EnC,EAAA,CAAAC,cAAA,eAAuB;IAEnBD,EAAA,CAAAI,cAAA,EAA4G;IAA5GJ,EAAA,CAAAC,cAAA,eAA4G;IAC1GD,EAAA,CAAAE,SAAA,gBAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,yBAAiB;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAM,MAAA,SAAC;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAE,SAAA,oBAMY;IACZF,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAI,cAAA,EAAqH;IAArHJ,EAAA,CAAAC,cAAA,eAAqH;IACnHD,EAAA,CAAAE,SAAA,gBAAsM;IACxMF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAK,eAAA,EAA6E;IAA7EL,EAAA,CAAAC,cAAA,eAA6E;IACrED,EAAA,CAAAM,MAAA,yCAA4B;IAAAN,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,IAAsE;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAErFH,EAAA,CAAAyB,UAAA,KAAAW,iDAAA,kBAMM;IACRpC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IAEnBD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAuK;IACzKF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,0BAAkB;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAC/BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAM,MAAA,SAAC;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAErCH,EAAA,CAAAC,cAAA,eAAsB;IAKlBD,EAAA,CAAAO,UAAA,oBAAA8B,oEAAAC,MAAA;MAAAtC,EAAA,CAAAU,aAAA,CAAAuB,IAAA;MAAA,MAAAM,OAAA,GAAAvC,EAAA,CAAAe,aAAA;MAAA,OAAUf,EAAA,CAAAgB,WAAA,CAAAuB,OAAA,CAAAC,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAJjCtC,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAC,cAAA,eAA6O;IAGvOD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAuK;IACzKF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,WAAK;IACuDD,EAAA,CAAAM,MAAA,gCAAwB;IAAAN,EAAA,CAAAG,YAAA,EAAI;IACtFH,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAM,MAAA,iCAAyB;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAErFH,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAM,MAAA,4CAA+B;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAM/FH,EAAA,CAAAyB,UAAA,KAAAgB,iDAAA,kBAKM;IAGNzC,EAAA,CAAAyB,UAAA,KAAAiB,iDAAA,kBAyBM;IACR1C,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAA0I;IAGpID,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAqjB;IAEvjBF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAqE;IAArEL,EAAA,CAAAC,cAAA,cAAqE;IAAAD,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAG,YAAA,EAAK;IAGnFH,EAAA,CAAAC,cAAA,eAAuB;IAOfD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA6G;IAC/GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAM,MAAA,+BAAkB;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAKnCH,EAAA,CAAAC,cAAA,kBAKC;IAEGD,EAAA,CAAAyB,UAAA,KAAAkB,sDAAA,kBAEM;IACN3C,EAAA,CAAAyB,UAAA,KAAAmB,iDAAA,kBAAkH;IAClH5C,EAAA,CAAAyB,UAAA,KAAAoB,kDAAA,mBAAsD;IACtD7C,EAAA,CAAAyB,UAAA,KAAAqB,kDAAA,mBAAwD;IAC1D9C,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAyB,UAAA,KAAAsB,iDAAA,kBAKM;IACN/C,EAAA,CAAAyB,UAAA,KAAAuB,iDAAA,kBAKM;IACRhD,EAAA,CAAAG,YAAA,EAAM;IAOZH,EAAA,CAAAC,cAAA,eAAuB;IAObD,EAAA,CAAAI,cAAA,EAA2E;IAA3EJ,EAAA,CAAAC,cAAA,eAA2E;IACzED,EAAA,CAAAE,SAAA,gBAA2I;IAC7IF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,WAAK;IAC+BD,EAAA,CAAAM,MAAA,oBAAY;IAAAN,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAM,MAAA,8BAAiB;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAK5DH,EAAA,CAAAC,cAAA,eAA2B;IAKnBD,EAAA,CAAAI,cAAA,EAA8G;IAA9GJ,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAE,SAAA,gBAAsN;IACxNF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAoB;IAApBL,EAAA,CAAAC,cAAA,eAAoB;IACyED,EAAA,CAAAM,MAAA,aAAK;IAAAN,EAAA,CAAAG,YAAA,EAAI;IACpGH,EAAA,CAAAC,cAAA,aAAkF;IAAAD,EAAA,CAAAM,MAAA,KAAkB;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAM9GH,EAAA,CAAAC,cAAA,gBAAiE;IAG3DD,EAAA,CAAAI,cAAA,EAA4G;IAA5GJ,EAAA,CAAAC,cAAA,gBAA4G;IAC1GD,EAAA,CAAAE,SAAA,iBAAwG;IAC1GF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAoB;IAApBL,EAAA,CAAAC,cAAA,gBAAoB;IACyED,EAAA,CAAAM,MAAA,oBAAW;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAC1GH,EAAA,CAAAC,cAAA,cAAiF;IAAAD,EAAA,CAAAM,MAAA,KAAgD;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAM3IH,EAAA,CAAAC,cAAA,gBAAiE;IAIzDD,EAAA,CAAAI,cAAA,EAAgH;IAAhHJ,EAAA,CAAAC,cAAA,gBAAgH;IAC9GD,EAAA,CAAAE,SAAA,iBAA6H;IAC/HF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,YAAK;IACwFD,EAAA,CAAAM,MAAA,oBAAW;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAC1GH,EAAA,CAAAC,cAAA,cAAoE;IAAAD,EAAA,CAAAM,MAAA,KAA6C;;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAGzHH,EAAA,CAAAC,cAAA,gBAAwB;IACgCD,EAAA,CAAAM,MAAA,KAA8B;IAAAN,EAAA,CAAAG,YAAA,EAAI;IACxFH,EAAA,CAAAC,cAAA,cAAwD;IAAAD,EAAA,CAAAM,MAAA,iBAAQ;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAM1EH,EAAA,CAAAC,cAAA,gBAAiE;IAG3DD,EAAA,CAAAI,cAAA,EAAgH;IAAhHJ,EAAA,CAAAC,cAAA,gBAAgH;IAC9GD,EAAA,CAAAE,SAAA,kBAAwV;IAC1VF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAK;IAALL,EAAA,CAAAC,cAAA,YAAK;IACwFD,EAAA,CAAAM,MAAA,qBAAY;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAC3GH,EAAA,CAAAC,cAAA,cAAoE;IAAAD,EAAA,CAAAM,MAAA,KAAyC;IAAAN,EAAA,CAAAG,YAAA,EAAI;IAQ3HH,EAAA,CAAAC,cAAA,gBAA0I;IAGpID,EAAA,CAAAI,cAAA,EAA4G;IAA5GJ,EAAA,CAAAC,cAAA,gBAA4G;IAC1GD,EAAA,CAAAE,SAAA,iBAA2I;IAC7IF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAK,eAAA,EAAqE;IAArEL,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAM,MAAA,iBAAQ;IAAAN,EAAA,CAAAG,YAAA,EAAK;IAGpFH,EAAA,CAAAC,cAAA,iBAAkE;IAE9DD,EAAA,CAAAI,cAAA,EAAmI;IAAnIJ,EAAA,CAAAC,cAAA,iBAAmI;IACjID,EAAA,CAAAE,SAAA,kBAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAM,MAAA,kFAA+D;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAE9EH,EAAA,CAAAC,cAAA,iBAAwC;IACtCD,EAAA,CAAAI,cAAA,EAAmI;IAAnIJ,EAAA,CAAAC,cAAA,iBAAmI;IACjID,EAAA,CAAAE,SAAA,kBAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAM,MAAA,8DAAqD;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAEpEH,EAAA,CAAAC,cAAA,iBAAwC;IACtCD,EAAA,CAAAI,cAAA,EAAmI;IAAnIJ,EAAA,CAAAC,cAAA,iBAAmI;IACjID,EAAA,CAAAE,SAAA,kBAA+H;IACjIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAM,MAAA,qFAA6D;IAAAN,EAAA,CAAAG,YAAA,EAAO;IAE5EH,EAAA,CAAAC,cAAA,iBAAwC;IACtCD,EAAA,CAAAI,cAAA,EAAqI;IAArIJ,EAAA,CAAAC,cAAA,iBAAqI;IACnID,EAAA,CAAAE,SAAA,kBAA2N;IAC7NF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAM,MAAA,6EAAqD;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;;;;IAtS5CH,EAAA,CAAAkB,SAAA,GAA4B;IAA5BlB,EAAA,CAAA+B,UAAA,cAAAkB,MAAA,CAAAC,cAAA,CAA4B;IAyB1ClD,EAAA,CAAAkB,SAAA,IAAsE;IAAtElB,EAAA,CAAAmD,kBAAA,OAAAC,OAAA,GAAAH,MAAA,CAAAC,cAAA,CAAAG,GAAA,kCAAAD,OAAA,CAAAE,KAAA,kBAAAF,OAAA,CAAAE,KAAA,CAAAxB,MAAA,2BAAsE;IAExE9B,EAAA,CAAAkB,SAAA,GAA8F;IAA9FlB,EAAA,CAAA+B,UAAA,WAAAwB,OAAA,GAAAN,MAAA,CAAAC,cAAA,CAAAG,GAAA,kCAAAE,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAN,MAAA,CAAAC,cAAA,CAAAG,GAAA,kCAAAE,OAAA,CAAAE,OAAA,EAA8F;IA2C9FzD,EAAA,CAAAkB,SAAA,IAA0D;IAA1DlB,EAAA,CAAA+B,UAAA,SAAAkB,MAAA,CAAApB,aAAA,CAAAC,MAAA,UAAAmB,MAAA,CAAAC,cAAA,CAAAO,OAAA,CAA0D;IAQ1DzD,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAA+B,UAAA,SAAAkB,MAAA,CAAApB,aAAA,CAAAC,MAAA,KAA8B;IA8CjC9B,EAAA,CAAAkB,SAAA,IAA6C;IAA7ClB,EAAA,CAAA+B,UAAA,eAAA/B,EAAA,CAAA0D,eAAA,KAAAC,GAAA,EAAAV,MAAA,CAAAW,QAAA,EAA6C;IAc9C5D,EAAA,CAAAkB,SAAA,GAAiF;IAAjFlB,EAAA,CAAA+B,UAAA,aAAAkB,MAAA,CAAAC,cAAA,CAAAM,OAAA,IAAAP,MAAA,CAAApB,aAAA,CAAAC,MAAA,UAAAmB,MAAA,CAAAY,YAAA,CAAiF;IAIzE7D,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAA+B,UAAA,UAAAkB,MAAA,CAAAY,YAAA,CAAmB;IAGnB7D,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,UAAA,SAAAkB,MAAA,CAAAY,YAAA,CAAkB;IACjB7D,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAA+B,UAAA,UAAAkB,MAAA,CAAAY,YAAA,CAAmB;IACnB7D,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,UAAA,SAAAkB,MAAA,CAAAY,YAAA,CAAkB;IAOvB7D,EAAA,CAAAkB,SAAA,GAA0D;IAA1DlB,EAAA,CAAA+B,UAAA,SAAAkB,MAAA,CAAAC,cAAA,CAAAM,OAAA,IAAAP,MAAA,CAAApB,aAAA,CAAAC,MAAA,OAA0D;IAM1D9B,EAAA,CAAAkB,SAAA,GAAsD;IAAtDlB,EAAA,CAAA+B,UAAA,SAAAkB,MAAA,CAAAC,cAAA,CAAAY,KAAA,IAAAb,MAAA,CAAApB,aAAA,CAAAC,MAAA,KAAsD;IA0C0B9B,EAAA,CAAAkB,SAAA,IAAkB;IAAlBlB,EAAA,CAAAmB,iBAAA,CAAA8B,MAAA,CAAAc,MAAA,CAAAC,KAAA,CAAkB;IAenBhE,EAAA,CAAAkB,SAAA,IAAgD;IAAhDlB,EAAA,CAAAmB,iBAAA,CAAA8B,MAAA,CAAAc,MAAA,CAAAE,WAAA,yBAAgD;IAgB3DjE,EAAA,CAAAkB,SAAA,IAA6C;IAA7ClB,EAAA,CAAAmB,iBAAA,CAAAnB,EAAA,CAAAkE,WAAA,UAAAjB,MAAA,CAAAc,MAAA,CAAAI,UAAA,gBAA6C;IAI7DnE,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAmD,kBAAA,KAAAF,MAAA,CAAAmB,gBAAA,aAA8B;IAgBhBpE,EAAA,CAAAkB,SAAA,IAAyC;IAAzClB,EAAA,CAAAmB,iBAAA,CAAA8B,MAAA,CAAAc,MAAA,CAAAM,MAAA,uBAAyC;;;AD1T/H;AAMA,OAAM,MAAOC,0BAA0B;EAQrCC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAA4B;IAL5B,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAbrB,KAAAjB,QAAQ,GAAW,EAAE;IAGrB,KAAA/B,aAAa,GAAW,EAAE;IAC1B,KAAAiD,SAAS,GAAG,IAAI;IAChB,KAAAjB,YAAY,GAAG,KAAK;IAUlB,IAAI,CAACX,cAAc,GAAG,IAAI,CAACsB,EAAE,CAACO,KAAK,CAAC;MAClCd,WAAW,EAAE,CAAC,EAAE,EAAE,CAAClE,UAAU,CAACiF,QAAQ,EAAEjF,UAAU,CAACkF,SAAS,CAAC,EAAE,CAAC,CAAC;KAClE,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACtB,QAAQ,GAAG,IAAI,CAACa,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAAC/B,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC5D,IAAI,CAACgC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,aAAa,CAACW,aAAa,CAAC,IAAI,CAAC1B,QAAQ,CAAC,CAAC2B,SAAS,CAAC;MACxDC,IAAI,EAAGzB,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACe,SAAS,GAAG,KAAK;MACxB,CAAC;MACDW,KAAK,EAAGC,GAAU,IAAI;QACpBC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEC,GAAG,CAAC;QACzD,IAAI,CAACZ,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC;KACD,CAAC;EACJ;EAEApD,YAAYA,CAACqD,KAAY;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,EAAE;MACf,IAAI,CAACnE,aAAa,GAAGoE,KAAK,CAACC,IAAI,CAACJ,KAAK,CAACE,KAAK,CAAC;;EAEhD;EAEA7D,QAAQA,CAAA;IACN,IAAI,IAAI,CAACe,cAAc,CAACM,OAAO,IAAI,IAAI,CAAC3B,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MAClE;;IAGF,IAAI,CAAC+B,YAAY,GAAG,IAAI;IACxB,MAAMsC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACzC,QAAQ,CAAC;IACxCuC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,IAAI,CAACxB,WAAW,CAACyB,gBAAgB,EAAE,IAAI,EAAE,CAAC;IACtEH,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,IAAI,CAACnD,cAAc,CAACI,KAAK,CAACW,WAAW,CAAC;IAErE,IAAI,CAACpC,aAAa,CAAC0E,OAAO,CAAEC,IAAI,IAAI;MAClCL,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEG,IAAI,CAAC;IACnC,CAAC,CAAC;IAEF,IAAI,CAAC5B,aAAa,CAAC6B,WAAW,CAACN,QAAQ,CAAC,CAACZ,SAAS,CAAC;MACjDC,IAAI,EAAGkB,QAAa,IAAI;QACtBC,KAAK,CAAC,uCAAuC,CAAC;QAC9C,IAAI,CAACjC,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACDH,KAAK,EAAGC,GAAU,IAAI;QACpBC,OAAO,CAACF,KAAK,CAAC,wCAAwC,EAAEC,GAAG,CAAC;QAC5DiB,KAAK,CAAC,yDAAyD,CAAC;QAChE,IAAI,CAAC9C,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEA;EACA5C,UAAUA,CAACJ,KAAa;IACtB,IAAI,CAACgB,aAAa,CAAC+E,MAAM,CAAC/F,KAAK,EAAE,CAAC,CAAC;EACrC;EAEA;EACAU,WAAWA,CAACsF,KAAa;IACvB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAE7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IAEnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE;EAEA;EACA5C,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACL,MAAM,EAAEI,UAAU,EAAE,OAAO,CAAC;IAEtC,MAAMoD,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAAC,IAAI,CAACzD,MAAM,CAACI,UAAU,CAAC;IACjD,MAAMuD,QAAQ,GAAGD,QAAQ,CAACE,OAAO,EAAE,GAAGJ,GAAG,CAACI,OAAO,EAAE;IACnD,MAAMC,QAAQ,GAAGX,IAAI,CAACY,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAOT,IAAI,CAACa,GAAG,CAAC,CAAC,EAAEF,QAAQ,CAAC;EAC9B;;;uBAtGWtD,0BAA0B,EAAAtE,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAnI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAApI,EAAA,CAAA+H,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAAtI,EAAA,CAAA+H,iBAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAAxI,EAAA,CAAA+H,iBAAA,CAAAU,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA1BpE,0BAA0B;MAAAqE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbvCjJ,EAAA,CAAAC,cAAA,aAA0K;UAGtKD,EAAA,CAAAE,SAAA,aAA6K;UAE/KF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuD;UAKkDD,EAAA,CAAAM,MAAA,kBAAW;UAAAN,EAAA,CAAAG,YAAA,EAAI;UAClHH,EAAA,CAAAI,cAAA,EAA2E;UAA3EJ,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAE,SAAA,eAA8F;UAChGF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAA0H;UAA1HL,EAAA,CAAAC,cAAA,aAA0H;UAAAD,EAAA,CAAAM,MAAA,IAA+B;UAAAN,EAAA,CAAAG,YAAA,EAAI;UAC7JH,EAAA,CAAAI,cAAA,EAA2E;UAA3EJ,EAAA,CAAAC,cAAA,cAA2E;UACzED,EAAA,CAAAE,SAAA,eAA8F;UAChGF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAK,eAAA,EAA6D;UAA7DL,EAAA,CAAAC,cAAA,gBAA6D;UAAAD,EAAA,CAAAM,MAAA,iBAAS;UAAAN,EAAA,CAAAG,YAAA,EAAO;UAG/EH,EAAA,CAAAC,cAAA,eAA0I;UAGpID,EAAA,CAAAI,cAAA,EAAsF;UAAtFJ,EAAA,CAAAC,cAAA,eAAsF;UACpFD,EAAA,CAAAE,SAAA,gBAAuK;UACzKF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAK,eAAA,EAAK;UAALL,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAM,MAAA,8BACF;UAAAN,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA8C;UAC5CD,EAAA,CAAAM,MAAA,2EACF;UAAAN,EAAA,CAAAG,YAAA,EAAI;UAOZH,EAAA,CAAAyB,UAAA,KAAA0H,0CAAA,kBAMM;UAGNnJ,EAAA,CAAAyB,UAAA,KAAA2H,0CAAA,qBA2TM;UACRpJ,EAAA,CAAAG,YAAA,EAAM;;;UAhWGH,EAAA,CAAAkB,SAAA,IAA6C;UAA7ClB,EAAA,CAAA+B,UAAA,eAAA/B,EAAA,CAAA0D,eAAA,IAAAC,GAAA,EAAAuF,GAAA,CAAAtF,QAAA,EAA6C;UAA0E5D,EAAA,CAAAkB,SAAA,GAA+B;UAA/BlB,EAAA,CAAAmB,iBAAA,EAAA+H,GAAA,CAAAnF,MAAA,kBAAAmF,GAAA,CAAAnF,MAAA,CAAAC,KAAA,cAA+B;UA2BvJhE,EAAA,CAAAkB,SAAA,IAAe;UAAflB,EAAA,CAAA+B,UAAA,SAAAmH,GAAA,CAAApE,SAAA,CAAe;UASf9E,EAAA,CAAAkB,SAAA,GAA0B;UAA1BlB,EAAA,CAAA+B,UAAA,SAAAmH,GAAA,CAAAnF,MAAA,KAAAmF,GAAA,CAAApE,SAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}