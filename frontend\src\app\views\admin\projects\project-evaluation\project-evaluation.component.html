<div class="container mx-auto px-4 py-6">
  <div class="max-w-4xl mx-auto bg-white rounded-lg shadow p-6">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Évaluation du projet</h1>

    <div *ngIf="isLoading" class="flex justify-center my-8">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>

    <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {{ error }}
    </div>

    <div *ngIf="rendu && !isLoading">
      <div class="mb-6 p-4 bg-gray-50 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Informations sur le rendu</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p><span class="font-medium">Projet:</span> {{ rendu.projet.titre }}</p>
            <p><span class="font-medium">Étudiant:</span> {{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>
          </div>
          <div>
            <p><span class="font-medium">Date de soumission:</span> {{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>
            <p><span class="font-medium">Description:</span> {{ rendu.description || 'Aucune description' }}</p>
          </div>
        </div>

        <div *ngIf="rendu.fichiers && rendu.fichiers.length > 0" class="mt-4">
          <h3 class="font-medium mb-2">Fichiers joints:</h3>
          <ul class="list-disc pl-5">
            <li *ngFor="let fichier of rendu.fichiers">
              <a [href]="'http://localhost:3000/' + fichier" target="_blank" class="text-blue-600 hover:underline">
                {{ fichier.split('/').pop() }}
              </a>
            </li>
          </ul>
        </div>
      </div>

      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">Mode d'évaluation</h2>
          <div class="flex items-center">
            <span class="mr-2">{{ evaluationMode === 'manual' ? 'Manuel' : 'IA' }}</span>
            <button
              (click)="toggleEvaluationMode()"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Changer de mode
            </button>
          </div>
        </div>

        <form [formGroup]="evaluationForm" (ngSubmit)="onSubmit()" *ngIf="evaluationMode === 'manual'">
          <div formGroupName="scores" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="form-group">
              <label class="block text-gray-700 mb-2">Structure du code (0-5)</label>
              <input
                type="number"
                formControlName="structure"
                min="0"
                max="5"
                class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 mb-2">Bonnes pratiques (0-5)</label>
              <input
                type="number"
                formControlName="pratiques"
                min="0"
                max="5"
                class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 mb-2">Fonctionnalité (0-5)</label>
              <input
                type="number"
                formControlName="fonctionnalite"
                min="0"
                max="5"
                class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 mb-2">Originalité (0-5)</label>
              <input
                type="number"
                formControlName="originalite"
                min="0"
                max="5"
                class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>
          </div>

          <div class="form-group mb-6">
            <label class="block text-gray-700 mb-2">Commentaires</label>
            <textarea
              formControlName="commentaires"
              rows="5"
              class="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
          </div>

          <div class="mb-4 p-3 bg-blue-50 rounded">
            <p><strong>Score total: {{ getScoreTotal() }}/{{ getScoreMaximum() }}</strong></p>
          </div>

          <div class="flex justify-between">
            <button
              type="button"
              (click)="annuler()"
              class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Annuler
            </button>
            <button
              type="submit"
              class="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              <span *ngIf="!isSubmitting">Soumettre l'évaluation</span>
              <span *ngIf="isSubmitting">Soumission en cours...</span>
            </button>
          </div>
        </form>

        <div *ngIf="evaluationMode === 'ai'" class="bg-gray-50 p-4 rounded-lg">
          <div *ngIf="!aiProcessing">
            <p class="mb-4">L'évaluation sera réalisée automatiquement par notre système d'IA (Mistral 7B).</p>
            <p class="mb-6">L'IA analysera le code soumis et fournira une évaluation basée sur les critères standards.</p>

            <div class="flex justify-between">
              <button
                type="button"
                (click)="annuler()"
                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                Annuler
              </button>
              <button
                (click)="onSubmit()"
                [disabled]="isSubmitting"
                class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <span *ngIf="!isSubmitting">Lancer l'évaluation IA</span>
                <span *ngIf="isSubmitting">Lancement en cours...</span>
              </button>
            </div>
          </div>

          <div *ngIf="aiProcessing" class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p class="text-gray-700">L'IA analyse le projet...</p>
            <p class="text-sm text-gray-500 mt-2">Cela peut prendre quelques instants</p>
          </div>
      </div>
    </div>
  </div>
</div>
