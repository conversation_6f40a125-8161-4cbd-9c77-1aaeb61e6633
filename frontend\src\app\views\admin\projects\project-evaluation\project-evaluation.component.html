<div class="min-h-screen bg-[#edf1f4] dark:bg-dark-bg-primary transition-colors duration-300">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-5xl mx-auto">
      <!-- Header avec gradient -->
      <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-2xl p-8 mb-8 shadow-xl">
        <div class="flex items-center space-x-4">
          <div class="bg-white/20 dark:bg-black/20 p-3 rounded-xl backdrop-blur-sm">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white mb-2">Évaluation du projet</h1>
            <p class="text-white/80">Système d'évaluation intelligent avec IA intégrée</p>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex flex-col items-center justify-center py-16">
        <div class="relative">
          <div class="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 dark:border-dark-accent-primary/30"></div>
          <div class="animate-spin rounded-full h-16 w-16 border-4 border-transparent border-t-primary dark:border-t-dark-accent-primary absolute top-0 left-0"></div>
        </div>
        <p class="mt-4 text-text dark:text-dark-text-secondary animate-pulse">Chargement des données...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="error" class="bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm">
        <div class="flex items-center space-x-3">
          <svg class="w-5 h-5 text-danger dark:text-danger-dark flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <p class="font-medium">{{ error }}</p>
        </div>
      </div>

      <!-- Contenu principal -->
      <div *ngIf="rendu && !isLoading" class="space-y-8">
        <!-- Informations sur le rendu -->
        <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50">
          <div class="flex items-center space-x-3 mb-6">
            <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-text-dark dark:text-dark-text-primary">Informations sur le rendu</h2>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="bg-gradient-to-r from-primary/5 to-primary-dark/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10 rounded-xl p-4">
                <p class="text-sm text-text dark:text-dark-text-secondary mb-1">Projet</p>
                <p class="font-semibold text-text-dark dark:text-dark-text-primary text-lg">{{ rendu.projet.titre }}</p>
              </div>
              <div class="bg-gradient-to-r from-secondary/5 to-secondary-dark/5 dark:from-dark-accent-secondary/10 dark:to-dark-accent-primary/10 rounded-xl p-4">
                <p class="text-sm text-text dark:text-dark-text-secondary mb-1">Étudiant</p>
                <p class="font-semibold text-text-dark dark:text-dark-text-primary text-lg">{{ rendu.etudiant.nom }} {{ rendu.etudiant.prenom }}</p>
              </div>
            </div>
            <div class="space-y-4">
              <div class="bg-gradient-to-r from-info/5 to-info/10 dark:from-dark-accent-primary/5 dark:to-dark-accent-primary/10 rounded-xl p-4">
                <p class="text-sm text-text dark:text-dark-text-secondary mb-1">Date de soumission</p>
                <p class="font-semibold text-text-dark dark:text-dark-text-primary text-lg">{{ rendu.dateSoumission | date:'dd/MM/yyyy HH:mm' }}</p>
              </div>
              <div class="bg-gradient-to-r from-success/5 to-success/10 dark:from-success/10 dark:to-success/5 rounded-xl p-4">
                <p class="text-sm text-text dark:text-dark-text-secondary mb-1">Description</p>
                <p class="font-semibold text-text-dark dark:text-dark-text-primary">{{ rendu.description || 'Aucune description' }}</p>
              </div>
            </div>
          </div>

          <!-- Fichiers joints -->
          <div *ngIf="rendu.fichiers && rendu.fichiers.length > 0" class="mt-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-tertiary/50 rounded-xl p-4">
            <div class="flex items-center space-x-2 mb-3">
              <svg class="w-5 h-5 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
              </svg>
              <h3 class="font-semibold text-text-dark dark:text-dark-text-primary">Fichiers joints ({{ rendu.fichiers.length }})</h3>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <a *ngFor="let fichier of rendu.fichiers"
                 *ngIf="fichier"
                 [href]="'http://localhost:3000/' + fichier"
                 target="_blank"
                 class="flex items-center space-x-2 p-3 bg-white dark:bg-dark-bg-secondary rounded-lg hover:bg-primary/5 dark:hover:bg-dark-accent-primary/10 transition-all duration-200 border border-gray-200 dark:border-dark-bg-tertiary group">
                <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-sm font-medium text-text-dark dark:text-dark-text-primary truncate">{{ fichier?.split('/').pop() || 'Fichier' }}</span>
              </a>
            </div>
          </div>
        </div>

        <!-- Mode d'évaluation -->
        <div class="bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div class="flex items-center space-x-3">
              <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-text-dark dark:text-dark-text-primary">Mode d'évaluation</h2>
            </div>

            <div class="flex items-center space-x-4">
              <!-- Mode selector -->
              <div class="flex items-center bg-gray-100 dark:bg-dark-bg-tertiary rounded-xl p-1 shadow-inner">
                <button
                  [class]="evaluationMode === 'manual' ? 'bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md' : 'text-text dark:text-dark-text-secondary'"
                  class="px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm"
                  (click)="evaluationMode = 'manual'"
                >
                  Manuel
                </button>
                <button
                  [class]="evaluationMode === 'ai' ? 'bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md' : 'text-text dark:text-dark-text-secondary'"
                  class="px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm"
                  (click)="evaluationMode = 'ai'"
                >
                  IA
                </button>
              </div>

              <!-- Toggle button -->
              <button
                (click)="toggleEvaluationMode()"
                class="px-4 py-2 bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 font-medium"
              >
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
                Basculer
              </button>
            </div>
          </div>

          <!-- Formulaire d'évaluation manuelle -->
          <form [formGroup]="evaluationForm" (ngSubmit)="onSubmit()" *ngIf="evaluationMode === 'manual'" class="space-y-8">
            <!-- Critères d'évaluation -->
            <div class="bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-6 border border-gray-200/50 dark:border-dark-bg-tertiary/50">
              <div class="flex items-center space-x-3 mb-6">
                <div class="bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-text-dark dark:text-dark-text-primary">Critères d'évaluation</h3>
              </div>

              <div formGroupName="scores" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Structure du code -->
                <div class="group">
                  <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                      </svg>
                      <span>Structure du code</span>
                    </div>
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      formControlName="structure"
                      min="0"
                      max="5"
                      class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary"
                      placeholder="0-5"
                    >
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                      <span class="text-sm font-medium text-text dark:text-dark-text-secondary">/5</span>
                    </div>
                  </div>
                </div>

                <!-- Bonnes pratiques -->
                <div class="group">
                  <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>Bonnes pratiques</span>
                    </div>
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      formControlName="pratiques"
                      min="0"
                      max="5"
                      class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary"
                      placeholder="0-5"
                    >
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                      <span class="text-sm font-medium text-text dark:text-dark-text-secondary">/5</span>
                    </div>
                  </div>
                </div>

                <!-- Fonctionnalité -->
                <div class="group">
                  <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                      <span>Fonctionnalité</span>
                    </div>
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      formControlName="fonctionnalite"
                      min="0"
                      max="5"
                      class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary"
                      placeholder="0-5"
                    >
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                      <span class="text-sm font-medium text-text dark:text-dark-text-secondary">/5</span>
                    </div>
                  </div>
                </div>

                <!-- Originalité -->
                <div class="group">
                  <label class="block text-sm font-semibold text-text-dark dark:text-dark-text-primary mb-3">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-primary dark:text-dark-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                      </svg>
                      <span>Originalité</span>
                    </div>
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      formControlName="originalite"
                      min="0"
                      max="5"
                      class="w-full px-4 py-3 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary"
                      placeholder="0-5"
                    >
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                      <span class="text-sm font-medium text-text dark:text-dark-text-secondary">/5</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Score total avec barre de progression -->
              <div class="mt-8 bg-gradient-to-r from-primary/5 to-primary-dark/5 dark:from-dark-accent-primary/10 dark:to-dark-accent-secondary/10 rounded-2xl p-6 border border-primary/20 dark:border-dark-accent-primary/30">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-3">
                    <div class="bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary p-2 rounded-lg">
                      <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                    </div>
                    <span class="text-lg font-bold text-text-dark dark:text-dark-text-primary">Score total</span>
                  </div>
                  <div class="text-right">
                    <div class="text-3xl font-bold text-primary dark:text-dark-accent-primary">{{ getScoreTotal() }}</div>
                    <div class="text-sm text-text dark:text-dark-text-secondary">sur {{ getScoreMaximum() }}</div>
                  </div>
                </div>
                <div class="w-full bg-gray-200 dark:bg-dark-bg-tertiary rounded-full h-3 overflow-hidden">
                  <div
                    class="h-full bg-gradient-to-r from-primary to-primary-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary rounded-full transition-all duration-500 ease-out"
                    [style.width.%]="(getScoreTotal() / getScoreMaximum()) * 100"
                  ></div>
                </div>
                <div class="flex justify-between text-xs text-text dark:text-dark-text-secondary mt-2">
                  <span>0</span>
                  <span>{{ getScoreMaximum() }}</span>
                </div>
              </div>
            </div>

            <!-- Section commentaires -->
            <div class="bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-6 border border-gray-200/50 dark:border-dark-bg-tertiary/50">
              <div class="flex items-center space-x-3 mb-6">
                <div class="bg-gradient-to-r from-info to-primary dark:from-dark-accent-secondary dark:to-dark-accent-primary p-2 rounded-lg">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-text-dark dark:text-dark-text-primary">Commentaires détaillés</h3>
              </div>

              <div class="relative">
                <textarea
                  formControlName="commentaires"
                  rows="6"
                  class="w-full px-4 py-4 bg-white dark:bg-dark-bg-secondary border-2 border-gray-200 dark:border-dark-bg-tertiary rounded-xl focus:outline-none focus:border-primary dark:focus:border-dark-accent-primary focus:ring-4 focus:ring-primary/10 dark:focus:ring-dark-accent-primary/20 transition-all duration-200 text-text-dark dark:text-dark-text-primary placeholder-text dark:placeholder-dark-text-secondary resize-none"
                  placeholder="Décrivez les points forts et les axes d'amélioration du projet. Soyez précis et constructif dans vos commentaires..."
                ></textarea>
                <div class="absolute bottom-3 right-3 text-xs text-text dark:text-dark-text-secondary bg-white/80 dark:bg-dark-bg-secondary/80 px-2 py-1 rounded-lg backdrop-blur-sm">
                  Minimum 50 caractères
                </div>
              </div>

              <div class="mt-4 flex items-start space-x-3 p-4 bg-primary/5 dark:bg-dark-accent-primary/10 rounded-xl border border-primary/20 dark:border-dark-accent-primary/30">
                <svg class="w-5 h-5 text-primary dark:text-dark-accent-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-sm text-text-dark dark:text-dark-text-primary">
                  <p class="font-medium mb-1">Conseils pour une évaluation de qualité :</p>
                  <ul class="space-y-1 text-text dark:text-dark-text-secondary">
                    <li>• Mentionnez les aspects techniques réussis</li>
                    <li>• Identifiez les points d'amélioration</li>
                    <li>• Proposez des suggestions constructives</li>
                    <li>• Évaluez la créativité et l'originalité</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Boutons d'action -->
            <div class="flex flex-col sm:flex-row gap-4 justify-between items-center pt-6">
              <button
                type="button"
                (click)="annuler()"
                class="w-full sm:w-auto group px-6 py-3 bg-gray-100 dark:bg-dark-bg-tertiary text-text-dark dark:text-dark-text-primary rounded-xl hover:bg-gray-200 dark:hover:bg-dark-bg-tertiary/80 transition-all duration-200 font-medium border-2 border-gray-200 dark:border-dark-bg-tertiary hover:border-gray-300 dark:hover:border-dark-bg-tertiary/60"
              >
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                  <span>Annuler</span>
                </div>
              </button>

              <button
                type="submit"
                class="w-full sm:w-auto group px-8 py-3 bg-gradient-to-r from-success to-success-dark dark:from-dark-accent-primary dark:to-dark-accent-secondary text-white rounded-xl hover:shadow-xl hover:scale-105 transition-all duration-200 font-semibold border-2 border-transparent hover:border-success/30 dark:hover:border-dark-accent-primary/30"
              >
                <div class="flex items-center justify-center space-x-2">
                  <svg *ngIf="!isSubmitting" class="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <svg *ngIf="isSubmitting" class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  <span *ngIf="!isSubmitting">Soumettre l'évaluation</span>
                  <span *ngIf="isSubmitting">Soumission en cours...</span>
                </div>
              </button>
            </div>
          </form>

        <div *ngIf="evaluationMode === 'ai'" class="bg-gray-50 p-4 rounded-lg">
          <div *ngIf="!aiProcessing">
            <p class="mb-4">L'évaluation sera réalisée automatiquement par notre système d'IA (Mistral 7B).</p>
            <p class="mb-6">L'IA analysera le code soumis et fournira une évaluation basée sur les critères standards.</p>

            <div class="flex justify-between">
              <button
                type="button"
                (click)="annuler()"
                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                Annuler
              </button>
              <button
                (click)="onSubmit()"
                [disabled]="isSubmitting"
                class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <span *ngIf="!isSubmitting">Lancer l'évaluation IA</span>
                <span *ngIf="isSubmitting">Lancement en cours...</span>
              </button>
            </div>
          </div>

          <div *ngIf="aiProcessing" class="text-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p class="text-gray-700">L'IA analyse le projet...</p>
            <p class="text-sm text-gray-500 mt-2">Cela peut prendre quelques instants</p>
          </div>
      </div>
    </div>
  </div>
</div>
